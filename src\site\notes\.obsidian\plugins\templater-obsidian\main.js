/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var Aa = Object.create;
var Ln = Object.defineProperty;
var _a = Object.getOwnPropertyDescriptor;
var xa = Object.getOwnPropertyNames;
var ya = Object.getPrototypeOf,
  ja = Object.prototype.hasOwnProperty;
var Yi = n => Ln(n, '__esModule', { value: !0 });
var va = (n, e) => {
    Yi(n);
    for (var t in e) Ln(n, t, { get: e[t], enumerable: !0 });
  },
  wa = (n, e, t) => {
    if ((e && typeof e == 'object') || typeof e == 'function')
      for (let r of xa(e))
        !ja.call(n, r) &&
          r !== 'default' &&
          Ln(n, r, {
            get: () => e[r],
            enumerable: !(t = _a(e, r)) || t.enumerable,
          });
    return n;
  },
  X = n =>
    wa(
      Yi(
        Ln(
          n != null ? Aa(ya(n)) : {},
          'default',
          n && n.__esModule && 'default' in n
            ? { get: () => n.default, enumerable: !0 }
            : { value: n, enumerable: !0 }
        )
      ),
      n
    );
var Ui = (() => {
  for (var n = new Uint8Array(128), e = 0; e < 64; e++)
    n[e < 26 ? e + 65 : e < 52 ? e + 71 : e < 62 ? e - 4 : e * 4 - 205] = e;
  return t => {
    for (
      var r = t.length,
        i = new Uint8Array(
          (((r - (t[r - 1] == '=') - (t[r - 2] == '=')) * 3) / 4) | 0
        ),
        o = 0,
        a = 0;
      o < r;

    ) {
      var l = n[t.charCodeAt(o++)],
        c = n[t.charCodeAt(o++)],
        d = n[t.charCodeAt(o++)],
        m = n[t.charCodeAt(o++)];
      ((i[a++] = (l << 2) | (c >> 4)),
        (i[a++] = (c << 4) | (d >> 2)),
        (i[a++] = (d << 6) | m));
    }
    return i;
  };
})();
va(exports, { default: () => Oi });
var jr = X(require('obsidian'));
var L = X(require('obsidian'));
var Gi = X(require('obsidian'));
function oe(n) {
  let e = new Gi.Notice('', 8e3);
  n instanceof O && n.console_msg
    ? ((e.noticeEl.innerHTML = `<b>Templater Error</b>:<br/>${n.message}<br/>Check console for more information`),
      console.error(
        'Templater Error:',
        n.message,
        `
`,
        n.console_msg
      ))
    : (e.noticeEl.innerHTML = `<b>Templater Error</b>:<br/>${n.message}`);
}
var O = class extends Error {
  constructor(e, t) {
    super(e);
    this.console_msg = t;
    ((this.name = this.constructor.name),
      Error.captureStackTrace &&
        Error.captureStackTrace(this, this.constructor));
  }
};
async function Te(n, e) {
  try {
    return await n();
  } catch (t) {
    return (t instanceof O ? oe(t) : oe(new O(e, t.message)), null);
  }
}
function ke(n, e) {
  try {
    return n();
  } catch (t) {
    return (oe(new O(e, t.message)), null);
  }
}
var re = (function () {
  function n() {}
  return (
    (n.explainIfInvalidTSDocTagName = function (e) {
      if (e[0] !== '@') return 'A TSDoc tag name must start with an "@" symbol';
      if (!n._tsdocTagNameRegExp.test(e))
        return 'A TSDoc tag name must start with a letter and contain only letters and numbers';
    }),
    (n.validateTSDocTagName = function (e) {
      var t = n.explainIfInvalidTSDocTagName(e);
      if (t) throw new Error(t);
    }),
    (n.explainIfInvalidLinkUrl = function (e) {
      if (e.length === 0) return 'The URL cannot be empty';
      if (!n._urlSchemeRegExp.test(e))
        return 'An @link URL must begin with a scheme comprised only of letters and numbers followed by "://". (For general URLs, use an HTML "<a>" tag instead.)';
      if (!n._urlSchemeAfterRegExp.test(e))
        return 'An @link URL must have at least one character after "://"';
    }),
    (n.explainIfInvalidHtmlName = function (e) {
      if (!n._htmlNameRegExp.test(e))
        return 'An HTML name must be an ASCII letter followed by zero or more letters, digits, or hyphens';
    }),
    (n.validateHtmlName = function (e) {
      var t = n.explainIfInvalidHtmlName(e);
      if (t) throw new Error(t);
    }),
    (n.explainIfInvalidPackageName = function (e) {
      if (e.length === 0) return 'The package name cannot be an empty string';
      if (!n._validPackageNameRegExp.test(e))
        return 'The package name '.concat(
          JSON.stringify(e),
          ' is not a valid package name'
        );
    }),
    (n.explainIfInvalidImportPath = function (e, t) {
      if (e.length > 0) {
        if (e.indexOf('//') >= 0) return 'An import path must not contain "//"';
        if (e[e.length - 1] === '/')
          return 'An import path must not end with "/"';
        if (!t && e[0] === '/')
          return 'An import path must not start with "/" unless prefixed by a package name';
      }
    }),
    (n.isSystemSelector = function (e) {
      return n._systemSelectors.has(e);
    }),
    (n.explainIfInvalidUnquotedIdentifier = function (e) {
      if (e.length === 0) return 'The identifier cannot be an empty string';
      if (n._identifierBadCharRegExp.test(e))
        return 'The identifier cannot non-word characters';
      if (n._identifierNumberStartRegExp.test(e))
        return 'The identifier must not start with a number';
    }),
    (n.explainIfInvalidUnquotedMemberIdentifier = function (e) {
      var t = n.explainIfInvalidUnquotedIdentifier(e);
      if (t !== void 0) return t;
      if (n.isSystemSelector(e))
        return 'The identifier "'.concat(
          e,
          '" must be quoted because it is a TSDoc system selector name'
        );
    }),
    (n._tsdocTagNameRegExp = /^@[a-z][a-z0-9]*$/i),
    (n._urlSchemeRegExp = /^[a-z][a-z0-9]*\:\/\//i),
    (n._urlSchemeAfterRegExp = /^[a-z][a-z0-9]*\:\/\/./i),
    (n._htmlNameRegExp = /^[a-z]+[a-z0-9\-]*$/i),
    (n._identifierBadCharRegExp = /[^a-z0-9_$]/i),
    (n._identifierNumberStartRegExp = /^[0-9]/),
    (n._validPackageNameRegExp = /^(?:@[a-z0-9\-_\.]+\/)?[a-z0-9\-_\.]+$/i),
    (n._systemSelectors = new Set([
      'instance',
      'static',
      'constructor',
      'class',
      'enum',
      'function',
      'interface',
      'namespace',
      'type',
      'variable',
    ])),
    n
  );
})();
var kr = (function () {
  function n() {
    ((this._docNodeDefinitionsByKind = new Map()),
      (this._docNodeDefinitionsByConstructor = new Map()));
  }
  return (
    (n.prototype.registerDocNodes = function (e, t) {
      var r = re.explainIfInvalidPackageName(e);
      if (r) throw new Error('Invalid NPM package name: ' + r);
      for (var i = 0, o = t; i < o.length; i++) {
        var a = o[i];
        if (!n._nodeKindRegExp.test(a.docNodeKind))
          throw new Error(
            'The DocNode kind '.concat(
              JSON.stringify(a.docNodeKind),
              ' is not a valid identifier.'
            ) +
              ' It must start with an underscore or letter, and be comprised of letters, numbers, and underscores'
          );
        var l = this._docNodeDefinitionsByKind.get(a.docNodeKind);
        if (l !== void 0)
          throw new Error(
            'The DocNode kind "'.concat(
              a.docNodeKind,
              '" was already registered'
            ) + ' by '.concat(l.packageName)
          );
        if (
          ((l = this._docNodeDefinitionsByConstructor.get(a.constructor)),
          l !== void 0)
        )
          throw new Error(
            'This DocNode constructor was already registered by '.concat(
              l.packageName
            ) + ' as '.concat(l.docNodeKind)
          );
        var c = {
          docNodeKind: a.docNodeKind,
          constructor: a.constructor,
          packageName: e,
          allowedChildKinds: new Set(),
        };
        (this._docNodeDefinitionsByKind.set(a.docNodeKind, c),
          this._docNodeDefinitionsByConstructor.set(a.constructor, c));
      }
    }),
    (n.prototype.throwIfNotRegisteredKind = function (e) {
      if (!this._docNodeDefinitionsByKind.has(e))
        throw new Error(
          'The DocNode kind "'.concat(
            e,
            '" was not registered with this TSDocConfiguration'
          )
        );
    }),
    (n.prototype.registerAllowableChildren = function (e, t) {
      for (var r = this._getDefinition(e), i = 0, o = t; i < o.length; i++) {
        var a = o[i];
        (this._getDefinition(a), r.allowedChildKinds.add(a));
      }
    }),
    (n.prototype.isAllowedChild = function (e, t) {
      var r = this._getDefinition(e);
      return r.allowedChildKinds.has(t);
    }),
    (n.prototype._getDefinition = function (e) {
      var t = this._docNodeDefinitionsByKind.get(e);
      if (t === void 0)
        throw new Error(
          'The DocNode kind "'.concat(
            e,
            '" was not registered with this TSDocConfiguration'
          )
        );
      return t;
    }),
    (n._nodeKindRegExp = /^[_a-z][_a-z0-9]*$/i),
    n
  );
})();
var U;
(function (n) {
  ((n.Core = 'Core'),
    (n.Extended = 'Extended'),
    (n.Discretionary = 'Discretionary'),
    (n.None = 'None'));
})(U || (U = {}));
var R;
(function (n) {
  ((n[(n.InlineTag = 0)] = 'InlineTag'),
    (n[(n.BlockTag = 1)] = 'BlockTag'),
    (n[(n.ModifierTag = 2)] = 'ModifierTag'));
})(R || (R = {}));
var Sr = (function () {
  function n(e) {
    (re.validateTSDocTagName(e.tagName),
      (this.tagName = e.tagName),
      (this.tagNameWithUpperCase = e.tagName.toUpperCase()),
      (this.syntaxKind = e.syntaxKind),
      (this.standardization = e.standardization || U.None),
      (this.allowMultiple = !!e.allowMultiple));
  }
  return (
    (n.validateTSDocTagName = function (e) {
      re.validateTSDocTagName(e);
    }),
    n
  );
})();
var G = (function () {
  function n() {}
  return (
    (n._defineTag = function (e) {
      return new Sr(e);
    }),
    (n.alpha = n._defineTag({
      tagName: '@alpha',
      syntaxKind: R.ModifierTag,
      standardization: U.Discretionary,
    })),
    (n.beta = n._defineTag({
      tagName: '@beta',
      syntaxKind: R.ModifierTag,
      standardization: U.Discretionary,
    })),
    (n.decorator = n._defineTag({
      tagName: '@decorator',
      syntaxKind: R.BlockTag,
      allowMultiple: !0,
      standardization: U.Extended,
    })),
    (n.defaultValue = n._defineTag({
      tagName: '@defaultValue',
      syntaxKind: R.BlockTag,
      standardization: U.Extended,
    })),
    (n.deprecated = n._defineTag({
      tagName: '@deprecated',
      syntaxKind: R.BlockTag,
      standardization: U.Core,
    })),
    (n.eventProperty = n._defineTag({
      tagName: '@eventProperty',
      syntaxKind: R.ModifierTag,
      standardization: U.Extended,
    })),
    (n.example = n._defineTag({
      tagName: '@example',
      syntaxKind: R.BlockTag,
      allowMultiple: !0,
      standardization: U.Extended,
    })),
    (n.experimental = n._defineTag({
      tagName: '@experimental',
      syntaxKind: R.ModifierTag,
      standardization: U.Discretionary,
    })),
    (n.inheritDoc = n._defineTag({
      tagName: '@inheritDoc',
      syntaxKind: R.InlineTag,
      standardization: U.Extended,
    })),
    (n.internal = n._defineTag({
      tagName: '@internal',
      syntaxKind: R.ModifierTag,
      standardization: U.Discretionary,
    })),
    (n.label = n._defineTag({
      tagName: '@label',
      syntaxKind: R.InlineTag,
      standardization: U.Core,
    })),
    (n.link = n._defineTag({
      tagName: '@link',
      syntaxKind: R.InlineTag,
      allowMultiple: !0,
      standardization: U.Core,
    })),
    (n.override = n._defineTag({
      tagName: '@override',
      syntaxKind: R.ModifierTag,
      standardization: U.Extended,
    })),
    (n.packageDocumentation = n._defineTag({
      tagName: '@packageDocumentation',
      syntaxKind: R.ModifierTag,
      standardization: U.Core,
    })),
    (n.param = n._defineTag({
      tagName: '@param',
      syntaxKind: R.BlockTag,
      allowMultiple: !0,
      standardization: U.Core,
    })),
    (n.privateRemarks = n._defineTag({
      tagName: '@privateRemarks',
      syntaxKind: R.BlockTag,
      standardization: U.Core,
    })),
    (n.public = n._defineTag({
      tagName: '@public',
      syntaxKind: R.ModifierTag,
      standardization: U.Discretionary,
    })),
    (n.readonly = n._defineTag({
      tagName: '@readonly',
      syntaxKind: R.ModifierTag,
      standardization: U.Extended,
    })),
    (n.remarks = n._defineTag({
      tagName: '@remarks',
      syntaxKind: R.BlockTag,
      standardization: U.Core,
    })),
    (n.returns = n._defineTag({
      tagName: '@returns',
      syntaxKind: R.BlockTag,
      standardization: U.Core,
    })),
    (n.sealed = n._defineTag({
      tagName: '@sealed',
      syntaxKind: R.ModifierTag,
      standardization: U.Extended,
    })),
    (n.see = n._defineTag({
      tagName: '@see',
      syntaxKind: R.BlockTag,
      standardization: U.Extended,
    })),
    (n.throws = n._defineTag({
      tagName: '@throws',
      syntaxKind: R.BlockTag,
      allowMultiple: !0,
      standardization: U.Extended,
    })),
    (n.typeParam = n._defineTag({
      tagName: '@typeParam',
      syntaxKind: R.BlockTag,
      allowMultiple: !0,
      standardization: U.Core,
    })),
    (n.virtual = n._defineTag({
      tagName: '@virtual',
      syntaxKind: R.ModifierTag,
      standardization: U.Extended,
    })),
    (n.allDefinitions = [
      n.alpha,
      n.beta,
      n.defaultValue,
      n.decorator,
      n.deprecated,
      n.eventProperty,
      n.example,
      n.experimental,
      n.inheritDoc,
      n.internal,
      n.label,
      n.link,
      n.override,
      n.packageDocumentation,
      n.param,
      n.privateRemarks,
      n.public,
      n.readonly,
      n.remarks,
      n.returns,
      n.sealed,
      n.see,
      n.throws,
      n.typeParam,
      n.virtual,
    ]),
    n
  );
})();
var Cr = (function () {
  function n() {
    ((this.ignoreUndefinedTags = !1),
      (this.reportUnsupportedTags = !1),
      (this.reportUnsupportedHtmlElements = !1));
  }
  return n;
})();
var g;
(function (n) {
  ((n.Block = 'Block'),
    (n.BlockTag = 'BlockTag'),
    (n.Excerpt = 'Excerpt'),
    (n.FencedCode = 'FencedCode'),
    (n.CodeSpan = 'CodeSpan'),
    (n.Comment = 'Comment'),
    (n.DeclarationReference = 'DeclarationReference'),
    (n.ErrorText = 'ErrorText'),
    (n.EscapedText = 'EscapedText'),
    (n.HtmlAttribute = 'HtmlAttribute'),
    (n.HtmlEndTag = 'HtmlEndTag'),
    (n.HtmlStartTag = 'HtmlStartTag'),
    (n.InheritDocTag = 'InheritDocTag'),
    (n.InlineTag = 'InlineTag'),
    (n.LinkTag = 'LinkTag'),
    (n.MemberIdentifier = 'MemberIdentifier'),
    (n.MemberReference = 'MemberReference'),
    (n.MemberSelector = 'MemberSelector'),
    (n.MemberSymbol = 'MemberSymbol'),
    (n.Paragraph = 'Paragraph'),
    (n.ParamBlock = 'ParamBlock'),
    (n.ParamCollection = 'ParamCollection'),
    (n.PlainText = 'PlainText'),
    (n.Section = 'Section'),
    (n.SoftBreak = 'SoftBreak'));
})(g || (g = {}));
var T = (function () {
  function n(e) {
    this.configuration = e.configuration;
  }
  return (
    (n.prototype.getChildNodes = function () {
      return (
        this.configuration.docNodeManager.throwIfNotRegisteredKind(this.kind),
        this.onGetChildNodes().filter(function (e) {
          return e !== void 0;
        })
      );
    }),
    (n.prototype.onGetChildNodes = function () {
      return [];
    }),
    (n.isParsedParameters = function (e) {
      return e.parsed === !0;
    }),
    n
  );
})();
var Wi = (function () {
  function n() {}
  return (
    (n.register = function (e) {
      var t = e.docNodeManager;
      (t.registerDocNodes('@microsoft/tsdoc', [
        { docNodeKind: g.Block, constructor: Gt },
        { docNodeKind: g.BlockTag, constructor: Hn },
        { docNodeKind: g.CodeSpan, constructor: $n },
        { docNodeKind: g.Comment, constructor: Kn },
        { docNodeKind: g.DeclarationReference, constructor: Rn },
        { docNodeKind: g.ErrorText, constructor: St },
        { docNodeKind: g.EscapedText, constructor: Yn },
        { docNodeKind: g.Excerpt, constructor: j },
        { docNodeKind: g.FencedCode, constructor: Un },
        { docNodeKind: g.HtmlAttribute, constructor: Gn },
        { docNodeKind: g.HtmlEndTag, constructor: Wn },
        { docNodeKind: g.HtmlStartTag, constructor: Vn },
        { docNodeKind: g.InheritDocTag, constructor: un },
        { docNodeKind: g.InlineTag, constructor: Wt },
        { docNodeKind: g.LinkTag, constructor: zn },
        { docNodeKind: g.MemberIdentifier, constructor: fn },
        { docNodeKind: g.MemberReference, constructor: Jn },
        { docNodeKind: g.MemberSelector, constructor: Qn },
        { docNodeKind: g.MemberSymbol, constructor: Xn },
        { docNodeKind: g.Paragraph, constructor: at },
        { docNodeKind: g.ParamBlock, constructor: dn },
        { docNodeKind: g.ParamCollection, constructor: pn },
        { docNodeKind: g.PlainText, constructor: Ge },
        { docNodeKind: g.Section, constructor: _t },
        { docNodeKind: g.SoftBreak, constructor: Zn },
      ]),
        t.registerAllowableChildren(g.Section, [
          g.FencedCode,
          g.Paragraph,
          g.HtmlStartTag,
          g.HtmlEndTag,
        ]),
        t.registerAllowableChildren(g.Paragraph, [
          g.BlockTag,
          g.CodeSpan,
          g.ErrorText,
          g.EscapedText,
          g.HtmlStartTag,
          g.HtmlEndTag,
          g.InlineTag,
          g.LinkTag,
          g.PlainText,
          g.SoftBreak,
        ]));
    }),
    n
  );
})();
var v;
(function (n) {
  ((n.ConfigFileNotFound = 'tsdoc-config-file-not-found'),
    (n.ConfigInvalidJson = 'tsdoc-config-invalid-json'),
    (n.ConfigFileUnsupportedSchema = 'tsdoc-config-unsupported-schema'),
    (n.ConfigFileSchemaError = 'tsdoc-config-schema-error'),
    (n.ConfigFileCyclicExtends = 'tsdoc-config-cyclic-extends'),
    (n.ConfigFileUnresolvedExtends = 'tsdoc-config-unresolved-extends'),
    (n.ConfigFileUndefinedTag = 'tsdoc-config-undefined-tag'),
    (n.ConfigFileDuplicateTagName = 'tsdoc-config-duplicate-tag-name'),
    (n.ConfigFileInvalidTagName = 'tsdoc-config-invalid-tag-name'),
    (n.CommentNotFound = 'tsdoc-comment-not-found'),
    (n.CommentOpeningDelimiterSyntax =
      'tsdoc-comment-missing-opening-delimiter'),
    (n.CommentMissingClosingDelimiter =
      'tsdoc-comment-missing-closing-delimiter'),
    (n.ExtraInheritDocTag = 'tsdoc-extra-inheritdoc-tag'),
    (n.EscapeRightBrace = 'tsdoc-escape-right-brace'),
    (n.EscapeGreaterThan = 'tsdoc-escape-greater-than'),
    (n.MissingDeprecationMessage = 'tsdoc-missing-deprecation-message'),
    (n.InheritDocIncompatibleTag = 'tsdoc-inheritdoc-incompatible-tag'),
    (n.InheritDocIncompatibleSummary = 'tsdoc-inheritdoc-incompatible-summary'),
    (n.InlineTagMissingBraces = 'tsdoc-inline-tag-missing-braces'),
    (n.TagShouldNotHaveBraces = 'tsdoc-tag-should-not-have-braces'),
    (n.UnsupportedTag = 'tsdoc-unsupported-tag'),
    (n.UndefinedTag = 'tsdoc-undefined-tag'),
    (n.ParamTagWithInvalidType = 'tsdoc-param-tag-with-invalid-type'),
    (n.ParamTagWithInvalidOptionalName =
      'tsdoc-param-tag-with-invalid-optional-name'),
    (n.ParamTagWithInvalidName = 'tsdoc-param-tag-with-invalid-name'),
    (n.ParamTagMissingHyphen = 'tsdoc-param-tag-missing-hyphen'),
    (n.UnnecessaryBackslash = 'tsdoc-unnecessary-backslash'),
    (n.MissingTag = 'tsdoc-missing-tag'),
    (n.AtSignInWord = 'tsdoc-at-sign-in-word'),
    (n.AtSignWithoutTagName = 'tsdoc-at-sign-without-tag-name'),
    (n.MalformedInlineTag = 'tsdoc-malformed-inline-tag'),
    (n.CharactersAfterBlockTag = 'tsdoc-characters-after-block-tag'),
    (n.MalformedTagName = 'tsdoc-malformed-tag-name'),
    (n.CharactersAfterInlineTag = 'tsdoc-characters-after-inline-tag'),
    (n.InlineTagMissingRightBrace = 'tsdoc-inline-tag-missing-right-brace'),
    (n.InlineTagUnescapedBrace = 'tsdoc-inline-tag-unescaped-brace'),
    (n.InheritDocTagSyntax = 'tsdoc-inheritdoc-tag-syntax'),
    (n.LinkTagEmpty = 'tsdoc-link-tag-empty'),
    (n.LinkTagUnescapedText = 'tsdoc-link-tag-unescaped-text'),
    (n.LinkTagDestinationSyntax = 'tsdoc-link-tag-destination-syntax'),
    (n.LinkTagInvalidUrl = 'tsdoc-link-tag-invalid-url'),
    (n.ReferenceMissingHash = 'tsdoc-reference-missing-hash'),
    (n.ReferenceHashSyntax = 'tsdoc-reference-hash-syntax'),
    (n.ReferenceMalformedPackageName =
      'tsdoc-reference-malformed-package-name'),
    (n.ReferenceMalformedImportPath = 'tsdoc-reference-malformed-import-path'),
    (n.MissingReference = 'tsdoc-missing-reference'),
    (n.ReferenceMissingDot = 'tsdoc-reference-missing-dot'),
    (n.ReferenceSelectorMissingParens =
      'tsdoc-reference-selector-missing-parens'),
    (n.ReferenceMissingColon = 'tsdoc-reference-missing-colon'),
    (n.ReferenceMissingRightParen = 'tsdoc-reference-missing-right-paren'),
    (n.ReferenceSymbolSyntax = 'tsdoc-reference-symbol-syntax'),
    (n.ReferenceMissingRightBracket = 'tsdoc-reference-missing-right-bracket'),
    (n.ReferenceMissingQuote = 'tsdoc-reference-missing-quote'),
    (n.ReferenceEmptyIdentifier = 'tsdoc-reference-empty-identifier'),
    (n.ReferenceMissingIdentifier = 'tsdoc-reference-missing-identifier'),
    (n.ReferenceUnquotedIdentifier = 'tsdoc-reference-unquoted-identifier'),
    (n.ReferenceMissingLabel = 'tsdoc-reference-missing-label'),
    (n.ReferenceSelectorSyntax = 'tsdoc-reference-selector-syntax'),
    (n.HtmlTagMissingGreaterThan = 'tsdoc-html-tag-missing-greater-than'),
    (n.HtmlTagMissingEquals = 'tsdoc-html-tag-missing-equals'),
    (n.HtmlTagMissingString = 'tsdoc-html-tag-missing-string'),
    (n.HtmlStringMissingQuote = 'tsdoc-html-string-missing-quote'),
    (n.TextAfterHtmlString = 'tsdoc-text-after-html-string'),
    (n.MissingHtmlEndTag = 'tsdoc-missing-html-end-tag'),
    (n.MalformedHtmlName = 'tsdoc-malformed-html-name'),
    (n.UnsupportedHtmlElementName = 'tsdoc-unsupported-html-name'),
    (n.CodeFenceOpeningIndent = 'tsdoc-code-fence-opening-indent'),
    (n.CodeFenceSpecifierSyntax = 'tsdoc-code-fence-specifier-syntax'),
    (n.CodeFenceClosingIndent = 'tsdoc-code-fence-closing-indent'),
    (n.CodeFenceMissingDelimiter = 'tsdoc-code-fence-missing-delimiter'),
    (n.CodeFenceClosingSyntax = 'tsdoc-code-fence-closing-syntax'),
    (n.CodeSpanEmpty = 'tsdoc-code-span-empty'),
    (n.CodeSpanMissingDelimiter = 'tsdoc-code-span-missing-delimiter'));
})(v || (v = {}));
var er = [
  'tsdoc-config-file-not-found',
  'tsdoc-config-invalid-json',
  'tsdoc-config-unsupported-schema',
  'tsdoc-config-schema-error',
  'tsdoc-config-cyclic-extends',
  'tsdoc-config-unresolved-extends',
  'tsdoc-config-undefined-tag',
  'tsdoc-config-duplicate-tag-name',
  'tsdoc-config-invalid-tag-name',
  'tsdoc-comment-not-found',
  'tsdoc-comment-missing-opening-delimiter',
  'tsdoc-comment-missing-closing-delimiter',
  'tsdoc-extra-inheritdoc-tag',
  'tsdoc-escape-right-brace',
  'tsdoc-escape-greater-than',
  'tsdoc-missing-deprecation-message',
  'tsdoc-inheritdoc-incompatible-tag',
  'tsdoc-inheritdoc-incompatible-summary',
  'tsdoc-inline-tag-missing-braces',
  'tsdoc-tag-should-not-have-braces',
  'tsdoc-unsupported-tag',
  'tsdoc-undefined-tag',
  'tsdoc-param-tag-with-invalid-type',
  'tsdoc-param-tag-with-invalid-optional-name',
  'tsdoc-param-tag-with-invalid-name',
  'tsdoc-param-tag-missing-hyphen',
  'tsdoc-unnecessary-backslash',
  'tsdoc-missing-tag',
  'tsdoc-at-sign-in-word',
  'tsdoc-at-sign-without-tag-name',
  'tsdoc-malformed-inline-tag',
  'tsdoc-characters-after-block-tag',
  'tsdoc-malformed-tag-name',
  'tsdoc-characters-after-inline-tag',
  'tsdoc-inline-tag-missing-right-brace',
  'tsdoc-inline-tag-unescaped-brace',
  'tsdoc-inheritdoc-tag-syntax',
  'tsdoc-link-tag-empty',
  'tsdoc-link-tag-unescaped-text',
  'tsdoc-link-tag-destination-syntax',
  'tsdoc-link-tag-invalid-url',
  'tsdoc-reference-missing-hash',
  'tsdoc-reference-hash-syntax',
  'tsdoc-reference-malformed-package-name',
  'tsdoc-reference-malformed-import-path',
  'tsdoc-missing-reference',
  'tsdoc-reference-missing-dot',
  'tsdoc-reference-selector-missing-parens',
  'tsdoc-reference-missing-colon',
  'tsdoc-reference-missing-right-paren',
  'tsdoc-reference-symbol-syntax',
  'tsdoc-reference-missing-right-bracket',
  'tsdoc-reference-missing-quote',
  'tsdoc-reference-empty-identifier',
  'tsdoc-reference-missing-identifier',
  'tsdoc-reference-unquoted-identifier',
  'tsdoc-reference-missing-label',
  'tsdoc-reference-selector-syntax',
  'tsdoc-html-tag-missing-greater-than',
  'tsdoc-html-tag-missing-equals',
  'tsdoc-html-tag-missing-string',
  'tsdoc-html-string-missing-quote',
  'tsdoc-text-after-html-string',
  'tsdoc-missing-html-end-tag',
  'tsdoc-malformed-html-name',
  'tsdoc-code-fence-opening-indent',
  'tsdoc-code-fence-specifier-syntax',
  'tsdoc-code-fence-closing-indent',
  'tsdoc-code-fence-missing-delimiter',
  'tsdoc-code-fence-closing-syntax',
  'tsdoc-code-span-empty',
  'tsdoc-code-span-missing-delimiter',
];
er.sort();
var Vi = new Set(er);
var Dr = (function () {
  function n() {
    ((this._tagDefinitions = []),
      (this._tagDefinitionsByName = new Map()),
      (this._supportedTagDefinitions = new Set()),
      (this._validation = new Cr()),
      (this._docNodeManager = new kr()),
      (this._supportedHtmlElements = new Set()),
      this.clear(!1),
      Wi.register(this));
  }
  return (
    (n.prototype.clear = function (e) {
      (e === void 0 && (e = !1),
        (this._tagDefinitions.length = 0),
        this._tagDefinitionsByName.clear(),
        this._supportedTagDefinitions.clear(),
        (this._validation.ignoreUndefinedTags = !1),
        (this._validation.reportUnsupportedTags = !1),
        (this._validation.reportUnsupportedHtmlElements = !1),
        this._supportedHtmlElements.clear(),
        e || this.addTagDefinitions(G.allDefinitions));
    }),
    Object.defineProperty(n.prototype, 'tagDefinitions', {
      get: function () {
        return this._tagDefinitions;
      },
      enumerable: !1,
      configurable: !0,
    }),
    Object.defineProperty(n.prototype, 'supportedTagDefinitions', {
      get: function () {
        var e = this;
        return this.tagDefinitions.filter(function (t) {
          return e.isTagSupported(t);
        });
      },
      enumerable: !1,
      configurable: !0,
    }),
    Object.defineProperty(n.prototype, 'validation', {
      get: function () {
        return this._validation;
      },
      enumerable: !1,
      configurable: !0,
    }),
    Object.defineProperty(n.prototype, 'supportedHtmlElements', {
      get: function () {
        return Array.from(this._supportedHtmlElements.values());
      },
      enumerable: !1,
      configurable: !0,
    }),
    Object.defineProperty(n.prototype, 'docNodeManager', {
      get: function () {
        return this._docNodeManager;
      },
      enumerable: !1,
      configurable: !0,
    }),
    (n.prototype.tryGetTagDefinition = function (e) {
      return this._tagDefinitionsByName.get(e.toUpperCase());
    }),
    (n.prototype.tryGetTagDefinitionWithUpperCase = function (e) {
      return this._tagDefinitionsByName.get(e);
    }),
    (n.prototype.addTagDefinition = function (e) {
      var t = this._tagDefinitionsByName.get(e.tagNameWithUpperCase);
      if (t !== e) {
        if (t)
          throw new Error(
            'A tag is already defined using the name '.concat(t.tagName)
          );
        (this._tagDefinitions.push(e),
          this._tagDefinitionsByName.set(e.tagNameWithUpperCase, e));
      }
    }),
    (n.prototype.addTagDefinitions = function (e, t) {
      for (var r = 0, i = e; r < i.length; r++) {
        var o = i[r];
        (this.addTagDefinition(o), t !== void 0 && this.setSupportForTag(o, t));
      }
    }),
    (n.prototype.isTagSupported = function (e) {
      return (
        this._requireTagToBeDefined(e),
        this._supportedTagDefinitions.has(e)
      );
    }),
    (n.prototype.setSupportForTag = function (e, t) {
      (this._requireTagToBeDefined(e),
        t
          ? this._supportedTagDefinitions.add(e)
          : this._supportedTagDefinitions.delete(e),
        (this.validation.reportUnsupportedTags = !0));
    }),
    (n.prototype.setSupportForTags = function (e, t) {
      for (var r = 0, i = e; r < i.length; r++) {
        var o = i[r];
        this.setSupportForTag(o, t);
      }
    }),
    (n.prototype.setSupportedHtmlElements = function (e) {
      (this._supportedHtmlElements.clear(),
        (this._validation.reportUnsupportedHtmlElements = !0));
      for (var t = 0, r = e; t < r.length; t++) {
        var i = r[t];
        this._supportedHtmlElements.add(i);
      }
    }),
    (n.prototype.isHtmlElementSupported = function (e) {
      return this._supportedHtmlElements.has(e);
    }),
    (n.prototype.isKnownMessageId = function (e) {
      return Vi.has(e);
    }),
    Object.defineProperty(n.prototype, 'allTsdocMessageIds', {
      get: function () {
        return er;
      },
      enumerable: !1,
      configurable: !0,
    }),
    (n.prototype._requireTagToBeDefined = function (e) {
      var t = this._tagDefinitionsByName.get(e.tagNameWithUpperCase);
      if (!(t && t === e))
        throw new Error(
          'The specified TSDocTagDefinition is not defined for this TSDocConfiguration'
        );
    }),
    n
  );
})();
var Pr = (function () {
  function n() {
    ((this._nodes = []), (this._nodesByName = new Map()));
  }
  return (
    Object.defineProperty(n.prototype, 'nodes', {
      get: function () {
        return this._nodes;
      },
      enumerable: !1,
      configurable: !0,
    }),
    (n.prototype.hasTagName = function (e) {
      return this._nodesByName.has(e.toUpperCase());
    }),
    (n.prototype.hasTag = function (e) {
      return !!this.tryGetTag(e);
    }),
    (n.prototype.tryGetTag = function (e) {
      if (e.syntaxKind !== R.ModifierTag)
        throw new Error('The tag definition is not a modifier tag');
      return this._nodesByName.get(e.tagNameWithUpperCase);
    }),
    (n.prototype.addTag = function (e) {
      return this._nodesByName.has(e.tagNameWithUpperCase)
        ? !1
        : (this._nodesByName.set(e.tagNameWithUpperCase, e),
          this._nodes.push(e),
          !0);
    }),
    n
  );
})();
var ba = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  Nr = (function (n) {
    ba(e, n);
    function e() {
      return (n !== null && n.apply(this, arguments)) || this;
    }
    return (
      (e.prototype.isAlpha = function () {
        return this.hasTag(G.alpha);
      }),
      (e.prototype.isBeta = function () {
        return this.hasTag(G.beta);
      }),
      (e.prototype.isEventProperty = function () {
        return this.hasTag(G.eventProperty);
      }),
      (e.prototype.isExperimental = function () {
        return this.hasTag(G.experimental);
      }),
      (e.prototype.isInternal = function () {
        return this.hasTag(G.internal);
      }),
      (e.prototype.isOverride = function () {
        return this.hasTag(G.override);
      }),
      (e.prototype.isPackageDocumentation = function () {
        return this.hasTag(G.packageDocumentation);
      }),
      (e.prototype.isPublic = function () {
        return this.hasTag(G.public);
      }),
      (e.prototype.isReadonly = function () {
        return this.hasTag(G.readonly);
      }),
      (e.prototype.isSealed = function () {
        return this.hasTag(G.sealed);
      }),
      (e.prototype.isVirtual = function () {
        return this.hasTag(G.virtual);
      }),
      e
    );
  })(Pr);
var Ea = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  tr = (function (n) {
    Ea(e, n);
    function e(t, r) {
      var i = n.call(this, t) || this;
      return (
        (i._nodes = []),
        r !== void 0 && r.length > 0 && i.appendNodes(r),
        i
      );
    }
    return (
      Object.defineProperty(e.prototype, 'nodes', {
        get: function () {
          return this._nodes;
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.appendNode = function (t) {
        if (
          !this.configuration.docNodeManager.isAllowedChild(this.kind, t.kind)
        )
          throw new Error(
            'The TSDocConfiguration does not allow a '.concat(
              this.kind,
              ' node to'
            ) + ' contain a node of type '.concat(t.kind)
          );
        this._nodes.push(t);
      }),
      (e.prototype.appendNodes = function (t) {
        for (var r = 0, i = t; r < i.length; r++) {
          var o = i[r];
          this.appendNode(o);
        }
      }),
      (e.prototype.clearNodes = function () {
        this._nodes.length = 0;
      }),
      (e.prototype.onGetChildNodes = function () {
        return this._nodes;
      }),
      e
    );
  })(T);
var Ta = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  at = (function (n) {
    Ta(e, n);
    function e(t, r) {
      return n.call(this, t, r) || this;
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.Paragraph;
        },
        enumerable: !1,
        configurable: !0,
      }),
      e
    );
  })(tr);
var ka = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  _t = (function (n) {
    ka(e, n);
    function e(t, r) {
      return n.call(this, t, r) || this;
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.Section;
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.appendNodeInParagraph = function (t) {
        var r = void 0;
        if (this.nodes.length > 0) {
          var i = this.nodes[this.nodes.length - 1];
          i.kind === g.Paragraph && (r = i);
        }
        (r ||
          ((r = new at({ configuration: this.configuration })),
          this.appendNode(r)),
          r.appendNode(t));
      }),
      (e.prototype.appendNodesInParagraph = function (t) {
        for (var r = 0, i = t; r < i.length; r++) {
          var o = i[r];
          this.appendNodeInParagraph(o);
        }
      }),
      e
    );
  })(tr);
var Sa = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  Gt = (function (n) {
    Sa(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      return (
        (r._blockTag = t.blockTag),
        (r._content = new _t({ configuration: r.configuration })),
        r
      );
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.Block;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'blockTag', {
        get: function () {
          return this._blockTag;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'content', {
        get: function () {
          return this._content;
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.onGetChildNodes = function () {
        return [this.blockTag, this._content];
      }),
      e
    );
  })(T);
var u;
(function (n) {
  ((n[(n.EndOfInput = 2001)] = 'EndOfInput'),
    (n[(n.Newline = 2002)] = 'Newline'),
    (n[(n.Spacing = 2003)] = 'Spacing'),
    (n[(n.AsciiWord = 2004)] = 'AsciiWord'),
    (n[(n.OtherPunctuation = 2005)] = 'OtherPunctuation'),
    (n[(n.Other = 2006)] = 'Other'),
    (n[(n.Backslash = 2007)] = 'Backslash'),
    (n[(n.LessThan = 2008)] = 'LessThan'),
    (n[(n.GreaterThan = 2009)] = 'GreaterThan'),
    (n[(n.Equals = 2010)] = 'Equals'),
    (n[(n.SingleQuote = 2011)] = 'SingleQuote'),
    (n[(n.DoubleQuote = 2012)] = 'DoubleQuote'),
    (n[(n.Slash = 2013)] = 'Slash'),
    (n[(n.Hyphen = 2014)] = 'Hyphen'),
    (n[(n.AtSign = 2015)] = 'AtSign'),
    (n[(n.LeftCurlyBracket = 2016)] = 'LeftCurlyBracket'),
    (n[(n.RightCurlyBracket = 2017)] = 'RightCurlyBracket'),
    (n[(n.Backtick = 2018)] = 'Backtick'),
    (n[(n.Period = 2019)] = 'Period'),
    (n[(n.Colon = 2020)] = 'Colon'),
    (n[(n.Comma = 2021)] = 'Comma'),
    (n[(n.LeftSquareBracket = 2022)] = 'LeftSquareBracket'),
    (n[(n.RightSquareBracket = 2023)] = 'RightSquareBracket'),
    (n[(n.Pipe = 2024)] = 'Pipe'),
    (n[(n.LeftParenthesis = 2025)] = 'LeftParenthesis'),
    (n[(n.RightParenthesis = 2026)] = 'RightParenthesis'),
    (n[(n.PoundSymbol = 2027)] = 'PoundSymbol'),
    (n[(n.Plus = 2028)] = 'Plus'),
    (n[(n.DollarSign = 2029)] = 'DollarSign'));
})(u || (u = {}));
var Ct = (function () {
  function n(e, t, r) {
    ((this.kind = e), (this.range = t), (this.line = r));
  }
  return (
    (n.prototype.toString = function () {
      return this.kind === u.Newline
        ? `
`
        : this.range.toString();
    }),
    n
  );
})();
var Ca = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  x;
(function (n) {
  ((n.Spacing = 'Spacing'),
    (n.BlockTag = 'BlockTag'),
    (n.CodeSpan_OpeningDelimiter = 'CodeSpan_OpeningDelimiter'),
    (n.CodeSpan_Code = 'CodeSpan_Code'),
    (n.CodeSpan_ClosingDelimiter = 'CodeSpan_ClosingDelimiter'),
    (n.DeclarationReference_PackageName = 'DeclarationReference_PackageName'),
    (n.DeclarationReference_ImportPath = 'DeclarationReference_ImportPath'),
    (n.DeclarationReference_ImportHash = 'DeclarationReference_ImportHash'),
    (n.ErrorText = 'ErrorText'),
    (n.NonstandardText = 'NonstandardText'),
    (n.EscapedText = 'EscapedText'),
    (n.FencedCode_OpeningFence = 'FencedCode_OpeningFence'),
    (n.FencedCode_Language = 'FencedCode_Language'),
    (n.FencedCode_Code = 'FencedCode_Code'),
    (n.FencedCode_ClosingFence = 'FencedCode_ClosingFence'),
    (n.HtmlAttribute_Name = 'HtmlAttribute_Name'),
    (n.HtmlAttribute_Equals = 'HtmlAttribute_Equals'),
    (n.HtmlAttribute_Value = 'HtmlAttribute_Value'),
    (n.HtmlEndTag_OpeningDelimiter = 'HtmlEndTag_OpeningDelimiter'),
    (n.HtmlEndTag_Name = 'HtmlEndTag_Name'),
    (n.HtmlEndTag_ClosingDelimiter = 'HtmlEndTag_ClosingDelimiter'),
    (n.HtmlStartTag_OpeningDelimiter = 'HtmlStartTag_OpeningDelimiter'),
    (n.HtmlStartTag_Name = 'HtmlStartTag_Name'),
    (n.HtmlStartTag_ClosingDelimiter = 'HtmlStartTag_ClosingDelimiter'),
    (n.InlineTag_OpeningDelimiter = 'InlineTag_OpeningDelimiter'),
    (n.InlineTag_TagName = 'InlineTag_TagName'),
    (n.InlineTag_TagContent = 'InlineTag_TagContent'),
    (n.InlineTag_ClosingDelimiter = 'InlineTag_ClosingDelimiter'),
    (n.LinkTag_UrlDestination = 'LinkTag_UrlDestination'),
    (n.LinkTag_Pipe = 'LinkTag_Pipe'),
    (n.LinkTag_LinkText = 'LinkTag_LinkText'),
    (n.MemberIdentifier_LeftQuote = 'MemberIdentifier_LeftQuote'),
    (n.MemberIdentifier_Identifier = 'MemberIdentifier_Identifier'),
    (n.MemberIdentifier_RightQuote = 'MemberIdentifier_RightQuote'),
    (n.MemberReference_Dot = 'MemberReference_Dot'),
    (n.MemberReference_LeftParenthesis = 'MemberReference_LeftParenthesis'),
    (n.MemberReference_Colon = 'MemberReference_Colon'),
    (n.MemberReference_RightParenthesis = 'MemberReference_RightParenthesis'),
    (n.MemberSelector = 'MemberSelector'),
    (n.DocMemberSymbol_LeftBracket = 'DocMemberSymbol_LeftBracket'),
    (n.DocMemberSymbol_RightBracket = 'DocMemberSymbol_RightBracket'),
    (n.ParamBlock_ParameterName = 'ParamBlock_ParameterName'),
    (n.ParamBlock_Hyphen = 'ParamBlock_Hyphen'),
    (n.PlainText = 'PlainText'),
    (n.SoftBreak = 'SoftBreak'));
})(x || (x = {}));
var j = (function (n) {
  Ca(e, n);
  function e(t) {
    var r = n.call(this, t) || this;
    if (t.excerptKind === x.Spacing)
      for (var i = 0, o = t.content.tokens; i < o.length; i++) {
        var a = o[i];
        switch (a.kind) {
          case u.Spacing:
          case u.Newline:
          case u.EndOfInput:
            break;
          default:
            throw new Error(
              'The excerptKind=Spacing but the range contains a non-whitespace token'
            );
        }
      }
    return ((r._excerptKind = t.excerptKind), (r._content = t.content), r);
  }
  return (
    Object.defineProperty(e.prototype, 'kind', {
      get: function () {
        return g.Excerpt;
      },
      enumerable: !1,
      configurable: !0,
    }),
    Object.defineProperty(e.prototype, 'excerptKind', {
      get: function () {
        return this._excerptKind;
      },
      enumerable: !1,
      configurable: !0,
    }),
    Object.defineProperty(e.prototype, 'content', {
      get: function () {
        return this._content;
      },
      enumerable: !1,
      configurable: !0,
    }),
    e
  );
})(T);
var Da = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  Hn = (function (n) {
    Da(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      return (
        re.validateTSDocTagName(t.tagName),
        (r._tagName = t.tagName),
        (r._tagNameWithUpperCase = t.tagName.toUpperCase()),
        T.isParsedParameters(t) &&
          (r._tagNameExcerpt = new j({
            configuration: r.configuration,
            excerptKind: x.BlockTag,
            content: t.tagNameExcerpt,
          })),
        r
      );
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.BlockTag;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'tagName', {
        get: function () {
          return this._tagName;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'tagNameWithUpperCase', {
        get: function () {
          return this._tagNameWithUpperCase;
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.onGetChildNodes = function () {
        return [this._tagNameExcerpt];
      }),
      (e.prototype.getTokenSequence = function () {
        if (!this._tagNameExcerpt)
          throw new Error(
            'DocBlockTag.getTokenSequence() failed because this object did not originate from a parsed input'
          );
        return this._tagNameExcerpt.content;
      }),
      e
    );
  })(T);
var Pa = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  $n = (function (n) {
    Pa(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      return (
        T.isParsedParameters(t)
          ? ((r._openingDelimiterExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.CodeSpan_OpeningDelimiter,
              content: t.openingDelimiterExcerpt,
            })),
            (r._codeExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.CodeSpan_Code,
              content: t.codeExcerpt,
            })),
            (r._closingDelimiterExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.CodeSpan_ClosingDelimiter,
              content: t.closingDelimiterExcerpt,
            })))
          : (r._code = t.code),
        r
      );
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.CodeSpan;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'code', {
        get: function () {
          return (
            this._code === void 0 &&
              (this._code = this._codeExcerpt.content.toString()),
            this._code
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.onGetChildNodes = function () {
        return [
          this._openingDelimiterExcerpt,
          this._codeExcerpt,
          this._closingDelimiterExcerpt,
        ];
      }),
      e
    );
  })(T);
var st = (function () {
  function n() {
    this._chunks = [];
  }
  return (
    (n.prototype.append = function (e) {
      this._chunks.push(e);
    }),
    (n.prototype.toString = function () {
      if (this._chunks.length === 0) return '';
      if (this._chunks.length > 1) {
        var e = this._chunks.join('');
        ((this._chunks.length = 1), (this._chunks[0] = e));
      }
      return this._chunks[0];
    }),
    n
  );
})();
var zi = (function () {
  function n() {}
  return (
    (n.transform = function (e) {
      for (
        var t = [], r = !1, i = [], o = [], a = !1, l = 0, c = e.nodes;
        l < c.length;
        l++
      ) {
        var d = c[l];
        switch (d.kind) {
          case g.PlainText:
            var m = d,
              y = m.text,
              b = /^\s/.test(y),
              E = /\s$/.test(y),
              P = y.replace(/\s+/g, ' ').trim();
            (b && a && (r = !0),
              P.length > 0 &&
                (r && (i.push(' '), (r = !1)), i.push(P), o.push(d), (a = !0)),
              E && a && (r = !0));
            break;
          case g.SoftBreak:
            (a && (r = !0), o.push(d));
            break;
          default:
            (r && (i.push(' '), (r = !1)),
              i.length > 0 &&
                (t.push(
                  new Ge({ configuration: e.configuration, text: i.join('') })
                ),
                (i.length = 0),
                (o.length = 0)),
              t.push(d),
              (a = !0));
        }
      }
      i.length > 0 &&
        (t.push(new Ge({ configuration: e.configuration, text: i.join('') })),
        (i.length = 0),
        (o.length = 0));
      var k = new at({ configuration: e.configuration });
      return (k.appendNodes(t), k);
    }),
    n
  );
})();
var Or = (function () {
  function n() {}
  return (
    (n.trimSpacesInParagraph = function (e) {
      return zi.transform(e);
    }),
    n
  );
})();
var Mr = function (n, e, t) {
    if (t || arguments.length === 2)
      for (var r = 0, i = e.length, o; r < i; r++)
        (o || !(r in e)) &&
          (o || (o = Array.prototype.slice.call(e, 0, r)), (o[r] = e[r]));
    return n.concat(o || Array.prototype.slice.call(e));
  },
  ge;
(function (n) {
  ((n[(n.Closed = 0)] = 'Closed'),
    (n[(n.StartOfLine = 1)] = 'StartOfLine'),
    (n[(n.MiddleOfLine = 2)] = 'MiddleOfLine'));
})(ge || (ge = {}));
var ct = (function () {
  function n() {
    ((this.eol = `
`),
      (this._emitCommentFraming = !0),
      (this._lineState = ge.Closed),
      (this._previousLineHadContent = !1),
      (this._hangingParagraph = !1));
  }
  return (
    (n.prototype.renderComment = function (e, t) {
      ((this._emitCommentFraming = !0), this._renderCompleteObject(e, t));
    }),
    (n.prototype.renderHtmlTag = function (e, t) {
      ((this._emitCommentFraming = !1), this._renderCompleteObject(e, t));
    }),
    (n.prototype.renderDeclarationReference = function (e, t) {
      ((this._emitCommentFraming = !1), this._renderCompleteObject(e, t));
    }),
    (n.prototype._renderCompleteObject = function (e, t) {
      ((this._output = e),
        (this._lineState = ge.Closed),
        (this._previousLineHadContent = !1),
        (this._hangingParagraph = !1),
        this._renderNode(t),
        this._writeEnd());
    }),
    (n.prototype._renderNode = function (e) {
      var t = this;
      if (e !== void 0)
        switch (e.kind) {
          case g.Block:
            var r = e;
            (this._ensureLineSkipped(),
              this._renderNode(r.blockTag),
              r.blockTag.tagNameWithUpperCase ===
                G.returns.tagNameWithUpperCase &&
                (this._writeContent(' '), (this._hangingParagraph = !0)),
              this._renderNode(r.content));
            break;
          case g.BlockTag:
            var i = e;
            (this._lineState === ge.MiddleOfLine && this._writeContent(' '),
              this._writeContent(i.tagName));
            break;
          case g.CodeSpan:
            var o = e;
            (this._writeContent('`'),
              this._writeContent(o.code),
              this._writeContent('`'));
            break;
          case g.Comment:
            var a = e;
            (this._renderNodes(
              Mr(
                Mr(
                  Mr(
                    [
                      a.summarySection,
                      a.remarksBlock,
                      a.privateRemarks,
                      a.deprecatedBlock,
                      a.params,
                      a.typeParams,
                      a.returnsBlock,
                    ],
                    a.customBlocks,
                    !0
                  ),
                  a.seeBlocks,
                  !0
                ),
                [a.inheritDocTag],
                !1
              )
            ),
              a.modifierTagSet.nodes.length > 0 &&
                (this._ensureLineSkipped(),
                this._renderNodes(a.modifierTagSet.nodes)));
            break;
          case g.DeclarationReference:
            var l = e;
            (this._writeContent(l.packageName),
              this._writeContent(l.importPath),
              (l.packageName !== void 0 || l.importPath !== void 0) &&
                this._writeContent('#'),
              this._renderNodes(l.memberReferences));
            break;
          case g.ErrorText:
            var c = e;
            this._writeContent(c.text);
            break;
          case g.EscapedText:
            var d = e;
            this._writeContent(d.encodedText);
            break;
          case g.FencedCode:
            var m = e;
            (this._ensureAtStartOfLine(),
              this._writeContent('```'),
              this._writeContent(m.language),
              this._writeNewline(),
              this._writeContent(m.code),
              this._writeContent('```'),
              this._writeNewline(),
              this._writeNewline());
            break;
          case g.HtmlAttribute:
            var y = e;
            (this._writeContent(y.name),
              this._writeContent(y.spacingAfterName),
              this._writeContent('='),
              this._writeContent(y.spacingAfterEquals),
              this._writeContent(y.value),
              this._writeContent(y.spacingAfterValue));
            break;
          case g.HtmlEndTag:
            var b = e;
            (this._writeContent('</'),
              this._writeContent(b.name),
              this._writeContent('>'));
            break;
          case g.HtmlStartTag:
            var E = e;
            (this._writeContent('<'),
              this._writeContent(E.name),
              this._writeContent(E.spacingAfterName));
            for (
              var P =
                  E.spacingAfterName === void 0 ||
                  E.spacingAfterName.length === 0,
                k = 0,
                w = E.htmlAttributes;
              k < w.length;
              k++
            ) {
              var M = w[k];
              (P && this._writeContent(' '),
                this._renderNode(M),
                (P =
                  M.spacingAfterValue === void 0 ||
                  M.spacingAfterValue.length === 0));
            }
            this._writeContent(E.selfClosingTag ? '/>' : '>');
            break;
          case g.InheritDocTag:
            var $ = e;
            this._renderInlineTag($, function () {
              $.declarationReference &&
                (t._writeContent(' '), t._renderNode($.declarationReference));
            });
            break;
          case g.InlineTag:
            var K = e;
            this._renderInlineTag(K, function () {
              K.tagContent.length > 0 &&
                (t._writeContent(' '), t._writeContent(K.tagContent));
            });
            break;
          case g.LinkTag:
            var C = e;
            this._renderInlineTag(C, function () {
              ((C.urlDestination !== void 0 || C.codeDestination !== void 0) &&
                (C.urlDestination !== void 0
                  ? (t._writeContent(' '), t._writeContent(C.urlDestination))
                  : C.codeDestination !== void 0 &&
                    (t._writeContent(' '), t._renderNode(C.codeDestination))),
                C.linkText !== void 0 &&
                  (t._writeContent(' '),
                  t._writeContent('|'),
                  t._writeContent(' '),
                  t._writeContent(C.linkText)));
            });
            break;
          case g.MemberIdentifier:
            var H = e;
            H.hasQuotes
              ? (this._writeContent('"'),
                this._writeContent(H.identifier),
                this._writeContent('"'))
              : this._writeContent(H.identifier);
            break;
          case g.MemberReference:
            var I = e;
            (I.hasDot && this._writeContent('.'),
              I.selector && this._writeContent('('),
              I.memberSymbol
                ? this._renderNode(I.memberSymbol)
                : this._renderNode(I.memberIdentifier),
              I.selector &&
                (this._writeContent(':'),
                this._renderNode(I.selector),
                this._writeContent(')')));
            break;
          case g.MemberSelector:
            var J = e;
            this._writeContent(J.selector);
            break;
          case g.MemberSymbol:
            var te = e;
            (this._writeContent('['),
              this._renderNode(te.symbolReference),
              this._writeContent(']'));
            break;
          case g.Section:
            var ne = e;
            this._renderNodes(ne.nodes);
            break;
          case g.Paragraph:
            var Q = Or.trimSpacesInParagraph(e);
            Q.nodes.length > 0 &&
              (this._hangingParagraph
                ? (this._hangingParagraph = !1)
                : this._ensureLineSkipped(),
              this._renderNodes(Q.nodes),
              this._writeNewline());
            break;
          case g.ParamBlock:
            var h = e;
            (this._ensureLineSkipped(),
              this._renderNode(h.blockTag),
              this._writeContent(' '),
              this._writeContent(h.parameterName),
              this._writeContent(' - '),
              (this._hangingParagraph = !0),
              this._renderNode(h.content),
              (this._hangingParagraph = !1));
            break;
          case g.ParamCollection:
            var S = e;
            this._renderNodes(S.blocks);
            break;
          case g.PlainText:
            var f = e;
            this._writeContent(f.text);
            break;
        }
    }),
    (n.prototype._renderInlineTag = function (e, t) {
      (this._writeContent('{'),
        this._writeContent(e.tagName),
        t(),
        this._writeContent('}'));
    }),
    (n.prototype._renderNodes = function (e) {
      for (var t = 0, r = e; t < r.length; t++) {
        var i = r[t];
        this._renderNode(i);
      }
    }),
    (n.prototype._ensureAtStartOfLine = function () {
      this._lineState === ge.MiddleOfLine && this._writeNewline();
    }),
    (n.prototype._ensureLineSkipped = function () {
      (this._ensureAtStartOfLine(),
        this._previousLineHadContent && this._writeNewline());
    }),
    (n.prototype._writeContent = function (e) {
      if (!(e === void 0 || e.length === 0)) {
        var t = e.split(/\r?\n/g);
        if (t.length > 1) {
          for (var r = !0, i = 0, o = t; i < o.length; i++) {
            var a = o[i];
            (r ? (r = !1) : this._writeNewline(), this._writeContent(a));
          }
          return;
        }
        (this._lineState === ge.Closed &&
          (this._emitCommentFraming &&
            this._output.append('/**' + this.eol + ' *'),
          (this._lineState = ge.StartOfLine)),
          this._lineState === ge.StartOfLine &&
            this._emitCommentFraming &&
            this._output.append(' '),
          this._output.append(e),
          (this._lineState = ge.MiddleOfLine),
          (this._previousLineHadContent = !0));
      }
    }),
    (n.prototype._writeNewline = function () {
      (this._lineState === ge.Closed &&
        (this._emitCommentFraming &&
          this._output.append('/**' + this.eol + ' *'),
        (this._lineState = ge.StartOfLine)),
        (this._previousLineHadContent = this._lineState === ge.MiddleOfLine),
        this._emitCommentFraming
          ? this._output.append(this.eol + ' *')
          : this._output.append(this.eol),
        (this._lineState = ge.StartOfLine),
        (this._hangingParagraph = !1));
    }),
    (n.prototype._writeEnd = function () {
      (this._lineState === ge.MiddleOfLine &&
        this._emitCommentFraming &&
        this._writeNewline(),
        this._lineState !== ge.Closed &&
          (this._emitCommentFraming && this._output.append('/' + this.eol),
          (this._lineState = ge.Closed)));
    }),
    n
  );
})();
var Na = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  pn = (function (n) {
    Na(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      return ((r._blocks = []), r);
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.ParamCollection;
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype[Symbol.iterator] = function () {
        return this._blocks[Symbol.iterator]();
      }),
      Object.defineProperty(e.prototype, 'blocks', {
        get: function () {
          return this._blocks;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'count', {
        get: function () {
          return this._blocks.length;
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.add = function (t) {
        (this._blocks.push(t),
          this._blocksByName === void 0 && (this._blocksByName = new Map()),
          this._blocksByName.has(t.parameterName) ||
            this._blocksByName.set(t.parameterName, t));
      }),
      (e.prototype.clear = function () {
        ((this._blocks.length = 0), (this._blocksByName = void 0));
      }),
      (e.prototype.tryGetBlockByName = function (t) {
        if (this._blocksByName) return this._blocksByName.get(t);
      }),
      (e.prototype.onGetChildNodes = function () {
        return this._blocks;
      }),
      e
    );
  })(T);
var Oa = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  nr = function (n, e, t) {
    if (t || arguments.length === 2)
      for (var r = 0, i = e.length, o; r < i; r++)
        (o || !(r in e)) &&
          (o || (o = Array.prototype.slice.call(e, 0, r)), (o[r] = e[r]));
    return n.concat(o || Array.prototype.slice.call(e));
  },
  Kn = (function (n) {
    Oa(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      return (
        (r.summarySection = new _t({ configuration: r.configuration })),
        (r.remarksBlock = void 0),
        (r.privateRemarks = void 0),
        (r.deprecatedBlock = void 0),
        (r.params = new pn({ configuration: r.configuration })),
        (r.typeParams = new pn({ configuration: r.configuration })),
        (r.returnsBlock = void 0),
        (r.modifierTagSet = new Nr()),
        (r._seeBlocks = []),
        (r._customBlocks = []),
        r
      );
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.Comment;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'seeBlocks', {
        get: function () {
          return this._seeBlocks;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'customBlocks', {
        get: function () {
          return this._customBlocks;
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype._appendSeeBlock = function (t) {
        this._seeBlocks.push(t);
      }),
      (e.prototype.appendCustomBlock = function (t) {
        this._customBlocks.push(t);
      }),
      (e.prototype.onGetChildNodes = function () {
        return nr(
          nr(
            nr(
              nr(
                [
                  this.summarySection,
                  this.remarksBlock,
                  this.privateRemarks,
                  this.deprecatedBlock,
                  this.params.count > 0 ? this.params : void 0,
                  this.typeParams.count > 0 ? this.typeParams : void 0,
                  this.returnsBlock,
                ],
                this.customBlocks,
                !0
              ),
              this.seeBlocks,
              !0
            ),
            [this.inheritDocTag],
            !1
          ),
          this.modifierTagSet.nodes,
          !0
        );
      }),
      (e.prototype.emitAsTsdoc = function () {
        var t = new st(),
          r = new ct();
        return (r.renderComment(t, this), t.toString());
      }),
      e
    );
  })(T);
var Ma = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  Ba = function (n, e, t) {
    if (t || arguments.length === 2)
      for (var r = 0, i = e.length, o; r < i; r++)
        (o || !(r in e)) &&
          (o || (o = Array.prototype.slice.call(e, 0, r)), (o[r] = e[r]));
    return n.concat(o || Array.prototype.slice.call(e));
  },
  Rn = (function (n) {
    Ma(e, n);
    function e(t) {
      var r,
        i = n.call(this, t) || this;
      return (
        T.isParsedParameters(t)
          ? (t.packageNameExcerpt &&
              (i._packageNameExcerpt = new j({
                configuration: i.configuration,
                excerptKind: x.DeclarationReference_PackageName,
                content: t.packageNameExcerpt,
              })),
            t.importPathExcerpt &&
              (i._importPathExcerpt = new j({
                configuration: i.configuration,
                excerptKind: x.DeclarationReference_ImportPath,
                content: t.importPathExcerpt,
              })),
            t.importHashExcerpt &&
              (i._importHashExcerpt = new j({
                configuration: i.configuration,
                excerptKind: x.DeclarationReference_ImportHash,
                content: t.importHashExcerpt,
              })),
            t.spacingAfterImportHashExcerpt &&
              (i._spacingAfterImportHashExcerpt = new j({
                configuration: i.configuration,
                excerptKind: x.Spacing,
                content: t.spacingAfterImportHashExcerpt,
              })))
          : ((i._packageName = t.packageName), (i._importPath = t.importPath)),
        (i._memberReferences = []),
        t.memberReferences &&
          (r = i._memberReferences).push.apply(r, t.memberReferences),
        i
      );
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.DeclarationReference;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'packageName', {
        get: function () {
          return (
            this._packageName === void 0 &&
              this._packageNameExcerpt !== void 0 &&
              (this._packageName = this._packageNameExcerpt.content.toString()),
            this._packageName
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'importPath', {
        get: function () {
          return (
            this._importPath === void 0 &&
              this._importPathExcerpt !== void 0 &&
              (this._importPath = this._importPathExcerpt.content.toString()),
            this._importPath
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'memberReferences', {
        get: function () {
          return this._memberReferences;
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.emitAsTsdoc = function () {
        var t = new st(),
          r = new ct();
        return (r.renderDeclarationReference(t, this), t.toString());
      }),
      (e.prototype.onGetChildNodes = function () {
        return Ba(
          [
            this._packageNameExcerpt,
            this._importPathExcerpt,
            this._importHashExcerpt,
            this._spacingAfterImportHashExcerpt,
          ],
          this._memberReferences,
          !0
        );
      }),
      e
    );
  })(T);
var Fa = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  St = (function (n) {
    Fa(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      return (
        (r._textExcerpt = new j({
          configuration: r.configuration,
          excerptKind: x.ErrorText,
          content: t.textExcerpt,
        })),
        (r._messageId = t.messageId),
        (r._errorMessage = t.errorMessage),
        (r._errorLocation = t.errorLocation),
        r
      );
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.ErrorText;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'text', {
        get: function () {
          return (
            this._text === void 0 &&
              (this._text = this._textExcerpt.content.toString()),
            this._text
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'textExcerpt', {
        get: function () {
          if (this._textExcerpt) return this._textExcerpt.content;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'messageId', {
        get: function () {
          return this._messageId;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'errorMessage', {
        get: function () {
          return this._errorMessage;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'errorLocation', {
        get: function () {
          return this._errorLocation;
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.onGetChildNodes = function () {
        return [this._textExcerpt];
      }),
      e
    );
  })(T);
var Ia = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  rr;
(function (n) {
  n[(n.CommonMarkBackslash = 0)] = 'CommonMarkBackslash';
})(rr || (rr = {}));
var Yn = (function (n) {
  Ia(e, n);
  function e(t) {
    var r = n.call(this, t) || this;
    return (
      (r._escapeStyle = t.escapeStyle),
      (r._encodedTextExcerpt = new j({
        configuration: r.configuration,
        excerptKind: x.EscapedText,
        content: t.encodedTextExcerpt,
      })),
      (r._decodedText = t.decodedText),
      r
    );
  }
  return (
    Object.defineProperty(e.prototype, 'kind', {
      get: function () {
        return g.EscapedText;
      },
      enumerable: !1,
      configurable: !0,
    }),
    Object.defineProperty(e.prototype, 'escapeStyle', {
      get: function () {
        return this._escapeStyle;
      },
      enumerable: !1,
      configurable: !0,
    }),
    Object.defineProperty(e.prototype, 'encodedText', {
      get: function () {
        return (
          this._encodedText === void 0 &&
            (this._encodedText = this._encodedTextExcerpt.content.toString()),
          this._encodedText
        );
      },
      enumerable: !1,
      configurable: !0,
    }),
    Object.defineProperty(e.prototype, 'decodedText', {
      get: function () {
        return this._decodedText;
      },
      enumerable: !1,
      configurable: !0,
    }),
    (e.prototype.onGetChildNodes = function () {
      return [this._encodedTextExcerpt];
    }),
    e
  );
})(T);
var qa = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  Un = (function (n) {
    qa(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      return (
        T.isParsedParameters(t)
          ? ((r._openingFenceExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.FencedCode_OpeningFence,
              content: t.openingFenceExcerpt,
            })),
            t.spacingAfterOpeningFenceExcerpt &&
              (r._spacingAfterOpeningFenceExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.Spacing,
                content: t.spacingAfterOpeningFenceExcerpt,
              })),
            t.languageExcerpt &&
              (r._languageExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.FencedCode_Language,
                content: t.languageExcerpt,
              })),
            t.spacingAfterLanguageExcerpt &&
              (r._spacingAfterLanguageExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.Spacing,
                content: t.spacingAfterLanguageExcerpt,
              })),
            (r._codeExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.FencedCode_Code,
              content: t.codeExcerpt,
            })),
            t.spacingBeforeClosingFenceExcerpt &&
              (r._spacingBeforeClosingFenceExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.Spacing,
                content: t.spacingBeforeClosingFenceExcerpt,
              })),
            (r._closingFenceExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.FencedCode_ClosingFence,
              content: t.closingFenceExcerpt,
            })),
            t.spacingAfterClosingFenceExcerpt &&
              (r._spacingAfterClosingFenceExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.Spacing,
                content: t.spacingAfterClosingFenceExcerpt,
              })))
          : ((r._code = t.code), (r._language = t.language)),
        r
      );
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.FencedCode;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'language', {
        get: function () {
          return (
            this._language === void 0 &&
              (this._languageExcerpt !== void 0
                ? (this._language = this._languageExcerpt.content.toString())
                : (this._language = '')),
            this._language
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'code', {
        get: function () {
          return (
            this._code === void 0 &&
              this._codeExcerpt !== void 0 &&
              (this._code = this._codeExcerpt.content.toString()),
            this._code
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.onGetChildNodes = function () {
        return [
          this._openingFenceExcerpt,
          this._spacingAfterOpeningFenceExcerpt,
          this._languageExcerpt,
          this._spacingAfterLanguageExcerpt,
          this._codeExcerpt,
          this._spacingBeforeClosingFenceExcerpt,
          this._closingFenceExcerpt,
          this._spacingAfterClosingFenceExcerpt,
        ];
      }),
      e
    );
  })(T);
var La = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  Gn = (function (n) {
    La(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      return (
        T.isParsedParameters(t)
          ? ((r._nameExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.HtmlAttribute_Name,
              content: t.nameExcerpt,
            })),
            t.spacingAfterNameExcerpt &&
              (r._spacingAfterNameExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.Spacing,
                content: t.spacingAfterNameExcerpt,
              })),
            (r._equalsExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.HtmlAttribute_Equals,
              content: t.equalsExcerpt,
            })),
            t.spacingAfterEqualsExcerpt &&
              (r._spacingAfterEqualsExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.Spacing,
                content: t.spacingAfterEqualsExcerpt,
              })),
            (r._valueExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.HtmlAttribute_Value,
              content: t.valueExcerpt,
            })),
            t.spacingAfterValueExcerpt &&
              (r._spacingAfterValueExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.Spacing,
                content: t.spacingAfterValueExcerpt,
              })))
          : ((r._name = t.name),
            (r._spacingAfterName = t.spacingAfterName),
            (r._spacingAfterEquals = t.spacingAfterEquals),
            (r._value = t.value),
            (r._spacingAfterValue = t.spacingAfterValue)),
        r
      );
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.HtmlAttribute;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'name', {
        get: function () {
          return (
            this._name === void 0 &&
              (this._name = this._nameExcerpt.content.toString()),
            this._name
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'spacingAfterName', {
        get: function () {
          return (
            this._spacingAfterName === void 0 &&
              this._spacingAfterNameExcerpt !== void 0 &&
              (this._spacingAfterName =
                this._spacingAfterNameExcerpt.content.toString()),
            this._spacingAfterName
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'spacingAfterEquals', {
        get: function () {
          return (
            this._spacingAfterEquals === void 0 &&
              this._spacingAfterEqualsExcerpt !== void 0 &&
              (this._spacingAfterEquals =
                this._spacingAfterEqualsExcerpt.content.toString()),
            this._spacingAfterEquals
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'value', {
        get: function () {
          return (
            this._value === void 0 &&
              (this._value = this._valueExcerpt.content.toString()),
            this._value
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'spacingAfterValue', {
        get: function () {
          return (
            this._spacingAfterValue === void 0 &&
              this._spacingAfterValueExcerpt !== void 0 &&
              (this._spacingAfterValue =
                this._spacingAfterValueExcerpt.content.toString()),
            this._spacingAfterValue
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.onGetChildNodes = function () {
        return [
          this._nameExcerpt,
          this._spacingAfterNameExcerpt,
          this._equalsExcerpt,
          this._spacingAfterEqualsExcerpt,
          this._valueExcerpt,
          this._spacingAfterValueExcerpt,
        ];
      }),
      e
    );
  })(T);
var Ha = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  Wn = (function (n) {
    Ha(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      return (
        T.isParsedParameters(t)
          ? ((r._openingDelimiterExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.HtmlEndTag_OpeningDelimiter,
              content: t.openingDelimiterExcerpt,
            })),
            (r._nameExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.HtmlEndTag_Name,
              content: t.nameExcerpt,
            })),
            t.spacingAfterNameExcerpt &&
              (r._spacingAfterNameExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.Spacing,
                content: t.spacingAfterNameExcerpt,
              })),
            (r._closingDelimiterExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.HtmlEndTag_ClosingDelimiter,
              content: t.closingDelimiterExcerpt,
            })))
          : (r._name = t.name),
        r
      );
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.HtmlEndTag;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'name', {
        get: function () {
          return (
            this._name === void 0 &&
              (this._name = this._nameExcerpt.content.toString()),
            this._name
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.emitAsHtml = function () {
        var t = new st(),
          r = new ct();
        return (r.renderHtmlTag(t, this), t.toString());
      }),
      (e.prototype.onGetChildNodes = function () {
        return [
          this._openingDelimiterExcerpt,
          this._nameExcerpt,
          this._spacingAfterNameExcerpt,
          this._closingDelimiterExcerpt,
        ];
      }),
      e
    );
  })(T);
var $a = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  Ji = function (n, e, t) {
    if (t || arguments.length === 2)
      for (var r = 0, i = e.length, o; r < i; r++)
        (o || !(r in e)) &&
          (o || (o = Array.prototype.slice.call(e, 0, r)), (o[r] = e[r]));
    return n.concat(o || Array.prototype.slice.call(e));
  },
  Vn = (function (n) {
    $a(e, n);
    function e(t) {
      var r,
        i = n.call(this, t) || this;
      return (
        T.isParsedParameters(t)
          ? ((i._openingDelimiterExcerpt = new j({
              configuration: i.configuration,
              excerptKind: x.HtmlStartTag_OpeningDelimiter,
              content: t.openingDelimiterExcerpt,
            })),
            (i._nameExcerpt = new j({
              configuration: i.configuration,
              excerptKind: x.HtmlStartTag_Name,
              content: t.nameExcerpt,
            })),
            t.spacingAfterNameExcerpt &&
              (i._spacingAfterNameExcerpt = new j({
                configuration: i.configuration,
                excerptKind: x.Spacing,
                content: t.spacingAfterNameExcerpt,
              })),
            (i._closingDelimiterExcerpt = new j({
              configuration: i.configuration,
              excerptKind: x.HtmlStartTag_ClosingDelimiter,
              content: t.closingDelimiterExcerpt,
            })))
          : ((i._name = t.name), (i._spacingAfterName = t.spacingAfterName)),
        (i._htmlAttributes = []),
        t.htmlAttributes &&
          (r = i._htmlAttributes).push.apply(r, t.htmlAttributes),
        (i._selfClosingTag = !!t.selfClosingTag),
        i
      );
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.HtmlStartTag;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'name', {
        get: function () {
          return (
            this._name === void 0 &&
              (this._name = this._nameExcerpt.content.toString()),
            this._name
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'htmlAttributes', {
        get: function () {
          return this._htmlAttributes;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'selfClosingTag', {
        get: function () {
          return this._selfClosingTag;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'spacingAfterName', {
        get: function () {
          return (
            this._spacingAfterName === void 0 &&
              this._spacingAfterNameExcerpt !== void 0 &&
              (this._spacingAfterName =
                this._spacingAfterNameExcerpt.content.toString()),
            this._spacingAfterName
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.emitAsHtml = function () {
        var t = new st(),
          r = new ct();
        return (r.renderHtmlTag(t, this), t.toString());
      }),
      (e.prototype.onGetChildNodes = function () {
        return Ji(
          Ji(
            [
              this._openingDelimiterExcerpt,
              this._nameExcerpt,
              this._spacingAfterNameExcerpt,
            ],
            this._htmlAttributes,
            !0
          ),
          [this._closingDelimiterExcerpt],
          !1
        );
      }),
      e
    );
  })(T);
var Ka = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  Qi = function (n, e, t) {
    if (t || arguments.length === 2)
      for (var r = 0, i = e.length, o; r < i; r++)
        (o || !(r in e)) &&
          (o || (o = Array.prototype.slice.call(e, 0, r)), (o[r] = e[r]));
    return n.concat(o || Array.prototype.slice.call(e));
  },
  Vt = (function (n) {
    Ka(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      return (
        re.validateTSDocTagName(t.tagName),
        T.isParsedParameters(t) &&
          ((r._openingDelimiterExcerpt = new j({
            configuration: r.configuration,
            excerptKind: x.InlineTag_OpeningDelimiter,
            content: t.openingDelimiterExcerpt,
          })),
          (r._tagNameExcerpt = new j({
            configuration: r.configuration,
            excerptKind: x.InlineTag_TagName,
            content: t.tagNameExcerpt,
          })),
          t.spacingAfterTagNameExcerpt &&
            (r._spacingAfterTagNameExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.Spacing,
              content: t.spacingAfterTagNameExcerpt,
            })),
          (r._closingDelimiterExcerpt = new j({
            configuration: r.configuration,
            excerptKind: x.InlineTag_ClosingDelimiter,
            content: t.closingDelimiterExcerpt,
          }))),
        (r._tagName = t.tagName),
        (r._tagNameWithUpperCase = t.tagName.toUpperCase()),
        r
      );
    }
    return (
      Object.defineProperty(e.prototype, 'tagName', {
        get: function () {
          return this._tagName;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'tagNameWithUpperCase', {
        get: function () {
          return this._tagNameWithUpperCase;
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.onGetChildNodes = function () {
        return Qi(
          Qi(
            [
              this._openingDelimiterExcerpt,
              this._tagNameExcerpt,
              this._spacingAfterTagNameExcerpt,
            ],
            this.getChildNodesForContent(),
            !0
          ),
          [this._closingDelimiterExcerpt],
          !1
        );
      }),
      e
    );
  })(T);
var Ra = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  un = (function (n) {
    Ra(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      if (r.tagNameWithUpperCase !== '@INHERITDOC')
        throw new Error(
          'DocInheritDocTag requires the tag name to be "{@inheritDoc}"'
        );
      return ((r._declarationReference = t.declarationReference), r);
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.InheritDocTag;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'declarationReference', {
        get: function () {
          return this._declarationReference;
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.getChildNodesForContent = function () {
        return [this._declarationReference];
      }),
      e
    );
  })(Vt);
var Ya = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  Wt = (function (n) {
    Ya(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      return (
        T.isParsedParameters(t)
          ? t.tagContentExcerpt &&
            (r._tagContentExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.InlineTag_TagContent,
              content: t.tagContentExcerpt,
            }))
          : (r._tagContent = t.tagContent),
        r
      );
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.InlineTag;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'tagContent', {
        get: function () {
          if (this._tagContent === void 0)
            if (this._tagContentExcerpt)
              this._tagContent = this._tagContentExcerpt.content.toString();
            else return '';
          return this._tagContent;
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.getChildNodesForContent = function () {
        return [this._tagContentExcerpt];
      }),
      e
    );
  })(Vt);
var Ua = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  zn = (function (n) {
    Ua(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      if (r.tagNameWithUpperCase !== '@LINK')
        throw new Error('DocLinkTag requires the tag name to be "{@link}"');
      if (((r._codeDestination = t.codeDestination), T.isParsedParameters(t))) {
        if (t.codeDestination !== void 0 && t.urlDestinationExcerpt !== void 0)
          throw new Error(
            'Either the codeDestination or the urlDestination may be specified, but not both'
          );
        (t.urlDestinationExcerpt &&
          (r._urlDestinationExcerpt = new j({
            configuration: r.configuration,
            excerptKind: x.LinkTag_UrlDestination,
            content: t.urlDestinationExcerpt,
          })),
          t.spacingAfterDestinationExcerpt &&
            (r._spacingAfterDestinationExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.Spacing,
              content: t.spacingAfterDestinationExcerpt,
            })),
          t.pipeExcerpt &&
            (r._pipeExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.LinkTag_Pipe,
              content: t.pipeExcerpt,
            })),
          t.spacingAfterPipeExcerpt &&
            (r._spacingAfterPipeExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.Spacing,
              content: t.spacingAfterPipeExcerpt,
            })),
          t.linkTextExcerpt &&
            (r._linkTextExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.LinkTag_LinkText,
              content: t.linkTextExcerpt,
            })),
          t.spacingAfterLinkTextExcerpt &&
            (r._spacingAfterLinkTextExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.Spacing,
              content: t.spacingAfterLinkTextExcerpt,
            })));
      } else {
        if (t.codeDestination !== void 0 && t.urlDestination !== void 0)
          throw new Error(
            'Either the codeDestination or the urlDestination may be specified, but not both'
          );
        ((r._urlDestination = t.urlDestination), (r._linkText = t.linkText));
      }
      return r;
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.LinkTag;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'codeDestination', {
        get: function () {
          return this._codeDestination;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'urlDestination', {
        get: function () {
          return (
            this._urlDestination === void 0 &&
              this._urlDestinationExcerpt !== void 0 &&
              (this._urlDestination =
                this._urlDestinationExcerpt.content.toString()),
            this._urlDestination
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'linkText', {
        get: function () {
          return (
            this._linkText === void 0 &&
              this._linkTextExcerpt !== void 0 &&
              (this._linkText = this._linkTextExcerpt.content.toString()),
            this._linkText
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.getChildNodesForContent = function () {
        return [
          this._codeDestination,
          this._urlDestinationExcerpt,
          this._spacingAfterDestinationExcerpt,
          this._pipeExcerpt,
          this._spacingAfterPipeExcerpt,
          this._linkTextExcerpt,
          this._spacingAfterLinkTextExcerpt,
        ];
      }),
      e
    );
  })(Vt);
var Ga = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  fn = (function (n) {
    Ga(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      return (
        T.isParsedParameters(t)
          ? (t.leftQuoteExcerpt &&
              (r._leftQuoteExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.MemberIdentifier_LeftQuote,
                content: t.leftQuoteExcerpt,
              })),
            (r._identifierExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.MemberIdentifier_Identifier,
              content: t.identifierExcerpt,
            })),
            t.rightQuoteExcerpt &&
              (r._rightQuoteExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.MemberIdentifier_RightQuote,
                content: t.rightQuoteExcerpt,
              })))
          : (r._identifier = t.identifier),
        r
      );
    }
    return (
      (e.isValidIdentifier = function (t) {
        return !re.explainIfInvalidUnquotedMemberIdentifier(t);
      }),
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.MemberIdentifier;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'identifier', {
        get: function () {
          return (
            this._identifier === void 0 &&
              (this._identifier = this._identifierExcerpt.content.toString()),
            this._identifier
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'hasQuotes', {
        get: function () {
          return this._identifierExcerpt
            ? !!this._leftQuoteExcerpt
            : !e.isValidIdentifier(this.identifier);
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.onGetChildNodes = function () {
        return [
          this._leftQuoteExcerpt,
          this._identifierExcerpt,
          this._rightQuoteExcerpt,
        ];
      }),
      e
    );
  })(T);
var Wa = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  Jn = (function (n) {
    Wa(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      return (
        T.isParsedParameters(t)
          ? ((r._hasDot = !!t.dotExcerpt),
            t.dotExcerpt &&
              (r._dotExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.MemberReference_Dot,
                content: t.dotExcerpt,
              })),
            t.spacingAfterDotExcerpt &&
              (r._spacingAfterDotExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.Spacing,
                content: t.spacingAfterDotExcerpt,
              })),
            t.leftParenthesisExcerpt &&
              (r._leftParenthesisExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.MemberReference_LeftParenthesis,
                content: t.leftParenthesisExcerpt,
              })),
            t.spacingAfterLeftParenthesisExcerpt &&
              (r._spacingAfterLeftParenthesisExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.Spacing,
                content: t.spacingAfterLeftParenthesisExcerpt,
              })),
            t.spacingAfterMemberExcerpt &&
              (r._spacingAfterMemberExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.Spacing,
                content: t.spacingAfterMemberExcerpt,
              })),
            t.colonExcerpt &&
              (r._colonExcerpt = new j({
                excerptKind: x.MemberReference_Colon,
                configuration: r.configuration,
                content: t.colonExcerpt,
              })),
            t.spacingAfterColonExcerpt &&
              (r._spacingAfterColonExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.Spacing,
                content: t.spacingAfterColonExcerpt,
              })),
            t.spacingAfterSelectorExcerpt &&
              (r._spacingAfterSelectorExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.Spacing,
                content: t.spacingAfterSelectorExcerpt,
              })),
            t.rightParenthesisExcerpt &&
              (r._rightParenthesisExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.MemberReference_RightParenthesis,
                content: t.rightParenthesisExcerpt,
              })),
            t.spacingAfterRightParenthesisExcerpt &&
              (r._spacingAfterRightParenthesisExcerpt = new j({
                configuration: r.configuration,
                excerptKind: x.Spacing,
                content: t.spacingAfterRightParenthesisExcerpt,
              })))
          : (r._hasDot = t.hasDot),
        (r._memberIdentifier = t.memberIdentifier),
        (r._memberSymbol = t.memberSymbol),
        (r._selector = t.selector),
        r
      );
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.MemberReference;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'hasDot', {
        get: function () {
          return this._hasDot;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'memberIdentifier', {
        get: function () {
          return this._memberIdentifier;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'memberSymbol', {
        get: function () {
          return this._memberSymbol;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'selector', {
        get: function () {
          return this._selector;
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.onGetChildNodes = function () {
        return [
          this._dotExcerpt,
          this._spacingAfterDotExcerpt,
          this._leftParenthesisExcerpt,
          this._spacingAfterLeftParenthesisExcerpt,
          this._memberIdentifier,
          this._memberSymbol,
          this._spacingAfterMemberExcerpt,
          this._colonExcerpt,
          this._spacingAfterColonExcerpt,
          this._selector,
          this._spacingAfterSelectorExcerpt,
          this._rightParenthesisExcerpt,
          this._spacingAfterRightParenthesisExcerpt,
        ];
      }),
      e
    );
  })(T);
var Va = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  zt;
(function (n) {
  ((n.Error = 'error'),
    (n.System = 'system'),
    (n.Index = 'index'),
    (n.Label = 'label'));
})(zt || (zt = {}));
var Qn = (function (n) {
  Va(e, n);
  function e(t) {
    var r = n.call(this, t) || this;
    return (
      T.isParsedParameters(t)
        ? ((r._selectorExcerpt = new j({
            configuration: r.configuration,
            excerptKind: x.MemberSelector,
            content: t.selectorExcerpt,
          })),
          (r._selector = t.selectorExcerpt.toString()))
        : (r._selector = t.selector),
      (r._selectorKind = zt.Error),
      (r._errorMessage = void 0),
      r._selector.length === 0
        ? (r._errorMessage = 'The selector cannot be an empty string')
        : e._likeIndexSelectorRegExp.test(r._selector)
          ? e._indexSelectorRegExp.test(r._selector)
            ? (r._selectorKind = zt.Index)
            : (r._errorMessage =
                'If the selector begins with a number, it must be a positive integer value')
          : e._likeLabelSelectorRegExp.test(r._selector)
            ? e._labelSelectorRegExp.test(r._selector)
              ? (r._selectorKind = zt.Label)
              : (r._errorMessage =
                  'A label selector must be comprised of upper case letters, numbers, and underscores and must not start with a number')
            : re.isSystemSelector(r._selector)
              ? (r._selectorKind = zt.System)
              : e._likeSystemSelectorRegExp.test(r._selector)
                ? (r._errorMessage =
                    'The selector '.concat(JSON.stringify(r._selector)) +
                    ' is not a recognized TSDoc system selector name')
                : (r._errorMessage = 'Invalid syntax for selector'),
      r
    );
  }
  return (
    Object.defineProperty(e.prototype, 'kind', {
      get: function () {
        return g.MemberSelector;
      },
      enumerable: !1,
      configurable: !0,
    }),
    Object.defineProperty(e.prototype, 'selector', {
      get: function () {
        return this._selector;
      },
      enumerable: !1,
      configurable: !0,
    }),
    Object.defineProperty(e.prototype, 'selectorKind', {
      get: function () {
        return this._selectorKind;
      },
      enumerable: !1,
      configurable: !0,
    }),
    Object.defineProperty(e.prototype, 'errorMessage', {
      get: function () {
        return this._errorMessage;
      },
      enumerable: !1,
      configurable: !0,
    }),
    (e.prototype.onGetChildNodes = function () {
      return [this._selectorExcerpt];
    }),
    (e._likeIndexSelectorRegExp = /^[0-9]/),
    (e._indexSelectorRegExp = /^[1-9][0-9]*$/),
    (e._likeLabelSelectorRegExp = /^[A-Z_]/u),
    (e._labelSelectorRegExp = /^[A-Z_][A-Z0-9_]+$/),
    (e._likeSystemSelectorRegExp = /^[a-z]+$/u),
    e
  );
})(T);
var za = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  Xn = (function (n) {
    za(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      return (
        T.isParsedParameters(t) &&
          ((r._leftBracketExcerpt = new j({
            configuration: r.configuration,
            excerptKind: x.DocMemberSymbol_LeftBracket,
            content: t.leftBracketExcerpt,
          })),
          t.spacingAfterLeftBracketExcerpt &&
            (r._spacingAfterLeftBracketExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.Spacing,
              content: t.spacingAfterLeftBracketExcerpt,
            })),
          (r._rightBracketExcerpt = new j({
            configuration: r.configuration,
            excerptKind: x.DocMemberSymbol_RightBracket,
            content: t.rightBracketExcerpt,
          }))),
        (r._symbolReference = t.symbolReference),
        r
      );
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.MemberSymbol;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'symbolReference', {
        get: function () {
          return this._symbolReference;
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.onGetChildNodes = function () {
        return [
          this._leftBracketExcerpt,
          this._spacingAfterLeftBracketExcerpt,
          this._symbolReference,
          this._rightBracketExcerpt,
        ];
      }),
      e
    );
  })(T);
var Ja = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  dn = (function (n) {
    Ja(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      return (
        (r._parameterName = t.parameterName),
        T.isParsedParameters(t) &&
          (t.spacingBeforeParameterNameExcerpt &&
            (r._spacingBeforeParameterNameExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.Spacing,
              content: t.spacingBeforeParameterNameExcerpt,
            })),
          t.unsupportedJsdocTypeBeforeParameterNameExcerpt &&
            (r._unsupportedJsdocTypeBeforeParameterNameExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.NonstandardText,
              content: t.unsupportedJsdocTypeBeforeParameterNameExcerpt,
            })),
          t.unsupportedJsdocOptionalNameOpenBracketExcerpt &&
            (r._unsupportedJsdocOptionalNameOpenBracketExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.NonstandardText,
              content: t.unsupportedJsdocOptionalNameOpenBracketExcerpt,
            })),
          (r._parameterNameExcerpt = new j({
            configuration: r.configuration,
            excerptKind: x.ParamBlock_ParameterName,
            content: t.parameterNameExcerpt,
          })),
          t.unsupportedJsdocOptionalNameRestExcerpt &&
            (r._unsupportedJsdocOptionalNameRestExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.NonstandardText,
              content: t.unsupportedJsdocOptionalNameRestExcerpt,
            })),
          t.spacingAfterParameterNameExcerpt &&
            (r._spacingAfterParameterNameExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.Spacing,
              content: t.spacingAfterParameterNameExcerpt,
            })),
          t.unsupportedJsdocTypeAfterParameterNameExcerpt &&
            (r._unsupportedJsdocTypeAfterParameterNameExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.NonstandardText,
              content: t.unsupportedJsdocTypeAfterParameterNameExcerpt,
            })),
          t.hyphenExcerpt &&
            (r._hyphenExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.ParamBlock_Hyphen,
              content: t.hyphenExcerpt,
            })),
          t.spacingAfterHyphenExcerpt &&
            (r._spacingAfterHyphenExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.Spacing,
              content: t.spacingAfterHyphenExcerpt,
            })),
          t.unsupportedJsdocTypeAfterHyphenExcerpt &&
            (r._unsupportedJsdocTypeAfterHyphenExcerpt = new j({
              configuration: r.configuration,
              excerptKind: x.NonstandardText,
              content: t.unsupportedJsdocTypeAfterHyphenExcerpt,
            }))),
        r
      );
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.ParamBlock;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'parameterName', {
        get: function () {
          return this._parameterName;
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.onGetChildNodes = function () {
        return [
          this.blockTag,
          this._spacingBeforeParameterNameExcerpt,
          this._unsupportedJsdocTypeBeforeParameterNameExcerpt,
          this._unsupportedJsdocOptionalNameOpenBracketExcerpt,
          this._parameterNameExcerpt,
          this._unsupportedJsdocOptionalNameRestExcerpt,
          this._spacingAfterParameterNameExcerpt,
          this._unsupportedJsdocTypeAfterParameterNameExcerpt,
          this._hyphenExcerpt,
          this._spacingAfterHyphenExcerpt,
          this._unsupportedJsdocTypeAfterHyphenExcerpt,
          this.content,
        ];
      }),
      e
    );
  })(Gt);
var Qa = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  Ge = (function (n) {
    Qa(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      if (T.isParsedParameters(t))
        r._textExcerpt = new j({
          configuration: r.configuration,
          excerptKind: x.PlainText,
          content: t.textExcerpt,
        });
      else {
        if (e._newlineCharacterRegExp.test(t.text))
          throw new Error(
            'The DocPlainText content must not contain newline characters'
          );
        r._text = t.text;
      }
      return r;
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.PlainText;
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'text', {
        get: function () {
          return (
            this._text === void 0 &&
              (this._text = this._textExcerpt.content.toString()),
            this._text
          );
        },
        enumerable: !1,
        configurable: !0,
      }),
      Object.defineProperty(e.prototype, 'textExcerpt', {
        get: function () {
          if (this._textExcerpt) return this._textExcerpt.content;
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.onGetChildNodes = function () {
        return [this._textExcerpt];
      }),
      (e._newlineCharacterRegExp = /[\n]/),
      e
    );
  })(T);
var Xa = (function () {
    var n = function (e, t) {
      return (
        (n =
          Object.setPrototypeOf ||
          ({ __proto__: [] } instanceof Array &&
            function (r, i) {
              r.__proto__ = i;
            }) ||
          function (r, i) {
            for (var o in i)
              Object.prototype.hasOwnProperty.call(i, o) && (r[o] = i[o]);
          }),
        n(e, t)
      );
    };
    return function (e, t) {
      if (typeof t != 'function' && t !== null)
        throw new TypeError(
          'Class extends value ' + String(t) + ' is not a constructor or null'
        );
      n(e, t);
      function r() {
        this.constructor = e;
      }
      e.prototype =
        t === null ? Object.create(t) : ((r.prototype = t.prototype), new r());
    };
  })(),
  Zn = (function (n) {
    Xa(e, n);
    function e(t) {
      var r = n.call(this, t) || this;
      if (T.isParsedParameters(t)) {
        var i = t;
        r._softBreakExcerpt = new j({
          configuration: r.configuration,
          excerptKind: x.SoftBreak,
          content: i.softBreakExcerpt,
        });
      }
      return r;
    }
    return (
      Object.defineProperty(e.prototype, 'kind', {
        get: function () {
          return g.SoftBreak;
        },
        enumerable: !1,
        configurable: !0,
      }),
      (e.prototype.onGetChildNodes = function () {
        return [this._softBreakExcerpt];
      }),
      e
    );
  })(T);
var ir = (function () {
  function n() {}
  return (
    (n.hasAnyTextContent = function (e, t) {
      (t === void 0 || t < 1) && (t = 1);
      var r;
      e instanceof T ? (r = [e]) : (r = e);
      var i = n._scanTextContent(r, t, 0);
      return i >= t;
    }),
    (n._scanTextContent = function (e, t, r) {
      for (var i = 0, o = e; i < o.length; i++) {
        var a = o[i];
        switch (a.kind) {
          case g.FencedCode:
            var l = a;
            r += n._countNonSpaceCharacters(l.code);
            break;
          case g.CodeSpan:
            var c = a;
            r += n._countNonSpaceCharacters(c.code);
            break;
          case g.EscapedText:
            var d = a;
            r += n._countNonSpaceCharacters(d.decodedText);
            break;
          case g.LinkTag:
            var m = a;
            r += n._countNonSpaceCharacters(m.linkText || '');
            break;
          case g.PlainText:
            var y = a;
            r += n._countNonSpaceCharacters(y.text);
            break;
        }
        if (
          r >= t ||
          ((r += n._scanTextContent(a.getChildNodes(), t, r)), r >= t)
        )
          break;
      }
      return r;
    }),
    (n._countNonSpaceCharacters = function (e) {
      for (var t = 0, r = e.length, i = 0; i < r; ) {
        switch (e.charCodeAt(i)) {
          case 32:
          case 9:
          case 13:
          case 10:
            break;
          default:
            ++t;
        }
        ++i;
      }
      return t;
    }),
    n
  );
})();
var We = (function () {
  function n(e, t, r) {
    ((this.buffer = e), (this.pos = t), (this.end = r), this._validateBounds());
  }
  return (
    (n.fromString = function (e) {
      return new n(e, 0, e.length);
    }),
    (n.fromStringRange = function (e, t, r) {
      return new n(e, t, r);
    }),
    Object.defineProperty(n.prototype, 'length', {
      get: function () {
        return this.end - this.pos;
      },
      enumerable: !1,
      configurable: !0,
    }),
    (n.prototype.getNewRange = function (e, t) {
      return new n(this.buffer, e, t);
    }),
    (n.prototype.isEmpty = function () {
      return this.pos === this.end;
    }),
    (n.prototype.toString = function () {
      return this.buffer.substring(this.pos, this.end);
    }),
    (n.prototype.getDebugDump = function (e, t) {
      return (
        this.buffer.substring(0, this.pos) +
        e +
        this.buffer.substring(this.pos, this.end) +
        t +
        this.buffer.substring(this.end)
      );
    }),
    (n.prototype.getLocation = function (e) {
      if (e < 0 || e > this.buffer.length) return { line: 0, column: 0 };
      for (var t = 1, r = 1, i = 0; i < e; ) {
        var o = this.buffer[i];
        (++i,
          o !== '\r' &&
            (o ===
            `
`
              ? (++t, (r = 1))
              : ++r));
      }
      return { line: t, column: r };
    }),
    (n.prototype._validateBounds = function () {
      if (this.pos < 0) throw new Error('TextRange.pos cannot be negative');
      if (this.end < 0) throw new Error('TextRange.end cannot be negative');
      if (this.end < this.pos)
        throw new Error('TextRange.end cannot be smaller than TextRange.pos');
      if (this.pos > this.buffer.length)
        throw new Error(
          'TextRange.pos cannot exceed the associated text buffer length'
        );
      if (this.end > this.buffer.length)
        throw new Error(
          'TextRange.end cannot exceed the associated text buffer length'
        );
    }),
    (n.empty = new n('', 0, 0)),
    n
  );
})();
var mn = (function () {
  function n(e) {
    ((this.messageId = e.messageId),
      (this.unformattedText = e.messageText),
      (this.textRange = e.textRange),
      (this.tokenSequence = e.tokenSequence),
      (this.docNode = e.docNode),
      (this._text = void 0));
  }
  return (
    (n._formatMessageText = function (e, t) {
      if (
        (e || (e = 'An unknown error occurred'), t.pos !== 0 || t.end !== 0)
      ) {
        var r = t.getLocation(t.pos);
        if (r.line) return '('.concat(r.line, ',').concat(r.column, '): ') + e;
      }
      return e;
    }),
    Object.defineProperty(n.prototype, 'text', {
      get: function () {
        return (
          this._text === void 0 &&
            (this._text = n._formatMessageText(
              this.unformattedText,
              this.textRange
            )),
          this._text
        );
      },
      enumerable: !1,
      configurable: !0,
    }),
    (n.prototype.toString = function () {
      return this.text;
    }),
    n
  );
})();
var Br = (function () {
  function n() {
    this._messages = [];
  }
  return (
    Object.defineProperty(n.prototype, 'messages', {
      get: function () {
        return this._messages;
      },
      enumerable: !1,
      configurable: !0,
    }),
    (n.prototype.addMessage = function (e) {
      this._messages.push(e);
    }),
    (n.prototype.addMessageForTextRange = function (e, t, r) {
      this.addMessage(new mn({ messageId: e, messageText: t, textRange: r }));
    }),
    (n.prototype.addMessageForTokenSequence = function (e, t, r, i) {
      this.addMessage(
        new mn({
          messageId: e,
          messageText: t,
          textRange: r.getContainingTextRange(),
          tokenSequence: r,
          docNode: i,
        })
      );
    }),
    (n.prototype.addMessageForDocErrorText = function (e) {
      var t;
      (e.textExcerpt ? (t = e.textExcerpt) : (t = e.errorLocation),
        this.addMessage(
          new mn({
            messageId: e.messageId,
            messageText: e.errorMessage,
            textRange: t.getContainingTextRange(),
            tokenSequence: t,
            docNode: e,
          })
        ));
    }),
    n
  );
})();
var Fr = (function () {
  function n(e, t) {
    ((this.commentRange = We.empty),
      (this.lines = []),
      (this.tokens = []),
      (this.configuration = e),
      (this.sourceRange = t),
      (this.docComment = new Kn({ configuration: this.configuration })),
      (this.log = new Br()));
  }
  return n;
})();
var xt = (function () {
  function n(e) {
    ((this.parserContext = e.parserContext),
      (this._startIndex = e.startIndex),
      (this._endIndex = e.endIndex),
      this._validateBounds());
  }
  return (
    (n.createEmpty = function (e) {
      return new n({ parserContext: e, startIndex: 0, endIndex: 0 });
    }),
    Object.defineProperty(n.prototype, 'startIndex', {
      get: function () {
        return this._startIndex;
      },
      enumerable: !1,
      configurable: !0,
    }),
    Object.defineProperty(n.prototype, 'endIndex', {
      get: function () {
        return this._endIndex;
      },
      enumerable: !1,
      configurable: !0,
    }),
    Object.defineProperty(n.prototype, 'tokens', {
      get: function () {
        return this.parserContext.tokens.slice(
          this._startIndex,
          this._endIndex
        );
      },
      enumerable: !1,
      configurable: !0,
    }),
    (n.prototype.getNewSequence = function (e, t) {
      return new n({
        parserContext: this.parserContext,
        startIndex: e,
        endIndex: t,
      });
    }),
    (n.prototype.getContainingTextRange = function () {
      return this.isEmpty()
        ? We.empty
        : this.parserContext.sourceRange.getNewRange(
            this.parserContext.tokens[this._startIndex].range.pos,
            this.parserContext.tokens[this._endIndex - 1].range.end
          );
    }),
    (n.prototype.isEmpty = function () {
      return this._startIndex === this._endIndex;
    }),
    (n.prototype.toString = function () {
      return this.tokens
        .map(function (e) {
          return e.toString();
        })
        .join('');
    }),
    (n.prototype._validateBounds = function () {
      if (this.startIndex < 0)
        throw new Error('TokenSequence.startIndex cannot be negative');
      if (this.endIndex < 0)
        throw new Error('TokenSequence.endIndex cannot be negative');
      if (this.endIndex < this.startIndex)
        throw new Error(
          'TokenSequence.endIndex cannot be smaller than TokenSequence.startIndex'
        );
      if (this.startIndex > this.parserContext.tokens.length)
        throw new Error(
          'TokenSequence.startIndex cannot exceed the associated token array'
        );
      if (this.endIndex > this.parserContext.tokens.length)
        throw new Error(
          'TokenSequence.endIndex cannot exceed the associated token array'
        );
    }),
    n
  );
})();
var pe;
(function (n) {
  ((n[(n.BeginComment1 = 0)] = 'BeginComment1'),
    (n[(n.BeginComment2 = 1)] = 'BeginComment2'),
    (n[(n.CollectingFirstLine = 2)] = 'CollectingFirstLine'),
    (n[(n.CollectingLine = 3)] = 'CollectingLine'),
    (n[(n.AdvancingLine = 4)] = 'AdvancingLine'),
    (n[(n.Done = 5)] = 'Done'));
})(pe || (pe = {}));
var Xi = (function () {
  function n() {}
  return (
    (n.extract = function (e) {
      for (
        var t = e.sourceRange,
          r = t.buffer,
          i = 0,
          o = 0,
          a = 0,
          l = 0,
          c = t.pos,
          d = pe.BeginComment1,
          m = [];
        d !== pe.Done;

      ) {
        if (c >= t.end)
          switch (d) {
            case pe.BeginComment1:
            case pe.BeginComment2:
              return (
                e.log.addMessageForTextRange(
                  v.CommentNotFound,
                  'Expecting a "/**" comment',
                  t
                ),
                !1
              );
            default:
              return (
                e.log.addMessageForTextRange(
                  v.CommentMissingClosingDelimiter,
                  'Unexpected end of input',
                  t
                ),
                !1
              );
          }
        var y = r[c],
          b = c;
        ++c;
        var E = c < t.end ? r[c] : '';
        switch (d) {
          case pe.BeginComment1:
            if (y === '/' && E === '*') ((i = b), ++c, (d = pe.BeginComment2));
            else if (!n._whitespaceCharacterRegExp.test(y))
              return (
                e.log.addMessageForTextRange(
                  v.CommentOpeningDelimiterSyntax,
                  'Expecting a leading "/**"',
                  t.getNewRange(b, b + 1)
                ),
                !1
              );
            break;
          case pe.BeginComment2:
            if (y === '*')
              (E === ' ' && ++c,
                (a = c),
                (l = c),
                (d = pe.CollectingFirstLine));
            else
              return (
                e.log.addMessageForTextRange(
                  v.CommentOpeningDelimiterSyntax,
                  'Expecting a leading "/**"',
                  t.getNewRange(b, b + 1)
                ),
                !1
              );
            break;
          case pe.CollectingFirstLine:
          case pe.CollectingLine:
            y ===
            `
`
              ? ((d !== pe.CollectingFirstLine || l > a) &&
                  m.push(t.getNewRange(a, l)),
                (a = c),
                (l = c),
                (d = pe.AdvancingLine))
              : y === '*' && E === '/'
                ? (l > a && m.push(t.getNewRange(a, l)),
                  (a = 0),
                  (l = 0),
                  ++c,
                  (o = c),
                  (d = pe.Done))
                : n._whitespaceCharacterRegExp.test(y) || (l = c);
            break;
          case pe.AdvancingLine:
            y === '*'
              ? E === '/'
                ? ((a = 0), (l = 0), ++c, (o = c), (d = pe.Done))
                : (E === ' ' && ++c, (a = c), (l = c), (d = pe.CollectingLine))
              : y ===
                  `
`
                ? (m.push(t.getNewRange(b, b)), (a = c))
                : n._whitespaceCharacterRegExp.test(y) ||
                  ((l = c), (d = pe.CollectingLine));
            break;
        }
      }
      return ((e.commentRange = t.getNewRange(i, o)), (e.lines = m), !0);
    }),
    (n._whitespaceCharacterRegExp = /^\s$/),
    n
  );
})();
var gn = (function () {
  function n() {}
  return (
    (n.readTokens = function (e) {
      n._ensureInitialized();
      for (var t = [], r = void 0, i = 0, o = e; i < o.length; i++) {
        var a = o[i];
        (n._pushTokensForLine(t, a), (r = a));
      }
      return (
        r
          ? t.push(new Ct(u.EndOfInput, r.getNewRange(r.end, r.end), r))
          : t.push(new Ct(u.EndOfInput, We.empty, We.empty)),
        t
      );
    }),
    (n.isPunctuation = function (e) {
      return (n._ensureInitialized(), n._punctuationTokens[e] || !1);
    }),
    (n._pushTokensForLine = function (e, t) {
      for (var r = t.buffer, i = t.end, o = t.pos, a = void 0, l = o; o < i; ) {
        var c = r.charCodeAt(o),
          d = n._charCodeMap[c];
        (d === void 0 && (d = u.Other),
          (a !== void 0 && d === a && n._isMultiCharacterToken(a)) ||
            (a !== void 0 && e.push(new Ct(a, t.getNewRange(l, o), t)),
            (l = o),
            (a = d)),
          ++o);
      }
      (a !== void 0 && e.push(new Ct(a, t.getNewRange(l, o), t)),
        e.push(new Ct(u.Newline, t.getNewRange(t.end, t.end), t)));
    }),
    (n._isMultiCharacterToken = function (e) {
      switch (e) {
        case u.Spacing:
        case u.AsciiWord:
        case u.Other:
          return !0;
      }
      return !1;
    }),
    (n._ensureInitialized = function () {
      if (!n._charCodeMap) {
        ((n._charCodeMap = {}), (n._punctuationTokens = {}));
        for (
          var e = n._commonMarkPunctuationCharacters, t = 0;
          t < e.length;
          ++t
        ) {
          var r = e.charCodeAt(t);
          n._charCodeMap[r] = u.OtherPunctuation;
        }
        for (
          var i = {
              '\\': u.Backslash,
              '<': u.LessThan,
              '>': u.GreaterThan,
              '=': u.Equals,
              "'": u.SingleQuote,
              '"': u.DoubleQuote,
              '/': u.Slash,
              '-': u.Hyphen,
              '@': u.AtSign,
              '{': u.LeftCurlyBracket,
              '}': u.RightCurlyBracket,
              '`': u.Backtick,
              '.': u.Period,
              ':': u.Colon,
              ',': u.Comma,
              '[': u.LeftSquareBracket,
              ']': u.RightSquareBracket,
              '|': u.Pipe,
              '(': u.LeftParenthesis,
              ')': u.RightParenthesis,
              '#': u.PoundSymbol,
              '+': u.Plus,
              $: u.DollarSign,
            },
            o = 0,
            a = Object.getOwnPropertyNames(i);
          o < a.length;
          o++
        ) {
          var l = a[o];
          ((n._charCodeMap[l.charCodeAt(0)] = i[l]),
            (n._punctuationTokens[i[l]] = !0));
        }
        n._punctuationTokens[u.OtherPunctuation] = !0;
        for (var c = n._wordCharacters, t = 0; t < c.length; ++t) {
          var r = c.charCodeAt(t);
          n._charCodeMap[r] = u.AsciiWord;
        }
        ((n._charCodeMap[' '.charCodeAt(0)] = u.Spacing),
          (n._charCodeMap['	'.charCodeAt(0)] = u.Spacing));
      }
    }),
    (n._commonMarkPunctuationCharacters = '!"#$%&\'()*+,-./:;<=>?@[\\]^`{|}~'),
    (n._wordCharacters =
      'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_'),
    n
  );
})();
var Ir = (function () {
  function n(e, t) {
    if (((this._parserContext = e), (this.tokens = e.tokens), t)) {
      if (t.parserContext !== this._parserContext)
        throw new Error(
          'The embeddedTokenSequence must use the same parser context'
        );
      ((this._readerStartIndex = t.startIndex),
        (this._readerEndIndex = t.endIndex));
    } else
      ((this._readerStartIndex = 0),
        (this._readerEndIndex = this.tokens.length));
    ((this._currentIndex = this._readerStartIndex),
      (this._accumulatedStartIndex = this._readerStartIndex));
  }
  return (
    (n.prototype.extractAccumulatedSequence = function () {
      if (this._accumulatedStartIndex === this._currentIndex)
        throw new Error(
          'Parser assertion failed: The queue should not be empty when extractAccumulatedSequence() is called'
        );
      var e = new xt({
        parserContext: this._parserContext,
        startIndex: this._accumulatedStartIndex,
        endIndex: this._currentIndex,
      });
      return ((this._accumulatedStartIndex = this._currentIndex), e);
    }),
    (n.prototype.isAccumulatedSequenceEmpty = function () {
      return this._accumulatedStartIndex === this._currentIndex;
    }),
    (n.prototype.tryExtractAccumulatedSequence = function () {
      if (!this.isAccumulatedSequenceEmpty())
        return this.extractAccumulatedSequence();
    }),
    (n.prototype.assertAccumulatedSequenceIsEmpty = function () {
      if (!this.isAccumulatedSequenceEmpty()) {
        var e = new xt({
            parserContext: this._parserContext,
            startIndex: this._accumulatedStartIndex,
            endIndex: this._currentIndex,
          }),
          t = e.tokens.map(function (r) {
            return r.toString();
          });
        throw new Error(
          `Parser assertion failed: The queue should be empty, but it contains:
` + JSON.stringify(t)
        );
      }
    }),
    (n.prototype.peekToken = function () {
      return this.tokens[this._currentIndex];
    }),
    (n.prototype.peekTokenKind = function () {
      return this._currentIndex >= this._readerEndIndex
        ? u.EndOfInput
        : this.tokens[this._currentIndex].kind;
    }),
    (n.prototype.peekTokenAfterKind = function () {
      return this._currentIndex + 1 >= this._readerEndIndex
        ? u.EndOfInput
        : this.tokens[this._currentIndex + 1].kind;
    }),
    (n.prototype.peekTokenAfterAfterKind = function () {
      return this._currentIndex + 2 >= this._readerEndIndex
        ? u.EndOfInput
        : this.tokens[this._currentIndex + 2].kind;
    }),
    (n.prototype.readToken = function () {
      if (this._currentIndex >= this._readerEndIndex)
        throw new Error('Cannot read past end of stream');
      var e = this.tokens[this._currentIndex];
      if (e.kind === u.EndOfInput)
        throw new Error('The EndOfInput token cannot be read');
      return (this._currentIndex++, e);
    }),
    (n.prototype.peekPreviousTokenKind = function () {
      return this._currentIndex === 0
        ? u.EndOfInput
        : this.tokens[this._currentIndex - 1].kind;
    }),
    (n.prototype.createMarker = function () {
      return this._currentIndex;
    }),
    (n.prototype.backtrackToMarker = function (e) {
      if (e > this._currentIndex) throw new Error('The marker has expired');
      ((this._currentIndex = e),
        e < this._accumulatedStartIndex && (this._accumulatedStartIndex = e));
    }),
    n
  );
})();
var or = function () {
  return (
    (or =
      Object.assign ||
      function (n) {
        for (var e, t = 1, r = arguments.length; t < r; t++) {
          e = arguments[t];
          for (var i in e)
            Object.prototype.hasOwnProperty.call(e, i) && (n[i] = e[i]);
        }
        return n;
      }),
    or.apply(this, arguments)
  );
};
function hn(n) {
  return n !== void 0 && Object.hasOwnProperty.call(n, 'failureMessage');
}
var Zi = (function () {
  function n(e) {
    ((this._parserContext = e),
      (this._configuration = e.configuration),
      (this._currentSection = e.docComment.summarySection));
  }
  return (
    (n.prototype.parse = function () {
      for (var e = new Ir(this._parserContext), t = !1; !t; )
        switch (e.peekTokenKind()) {
          case u.EndOfInput:
            t = !0;
            break;
          case u.Newline:
            (this._pushAccumulatedPlainText(e),
              e.readToken(),
              this._pushNode(
                new Zn({
                  parsed: !0,
                  configuration: this._configuration,
                  softBreakExcerpt: e.extractAccumulatedSequence(),
                })
              ));
            break;
          case u.Backslash:
            (this._pushAccumulatedPlainText(e),
              this._pushNode(this._parseBackslashEscape(e)));
            break;
          case u.AtSign:
            (this._pushAccumulatedPlainText(e), this._parseAndPushBlock(e));
            break;
          case u.LeftCurlyBracket: {
            this._pushAccumulatedPlainText(e);
            var r = e.createMarker(),
              i = this._parseInlineTag(e),
              o = this._parserContext.docComment;
            if (i instanceof un) {
              var a = e.createMarker() - 1;
              o.inheritDocTag === void 0
                ? (this._parserContext.docComment.inheritDocTag = i)
                : this._pushNode(
                    this._backtrackAndCreateErrorRange(
                      e,
                      r,
                      a,
                      v.ExtraInheritDocTag,
                      'A doc comment cannot have more than one @inheritDoc tag'
                    )
                  );
            } else this._pushNode(i);
            break;
          }
          case u.RightCurlyBracket:
            (this._pushAccumulatedPlainText(e),
              this._pushNode(
                this._createError(
                  e,
                  v.EscapeRightBrace,
                  'The "}" character should be escaped using a backslash to avoid confusion with a TSDoc inline tag'
                )
              ));
            break;
          case u.LessThan:
            (this._pushAccumulatedPlainText(e),
              e.peekTokenAfterKind() === u.Slash
                ? this._pushNode(this._parseHtmlEndTag(e))
                : this._pushNode(this._parseHtmlStartTag(e)));
            break;
          case u.GreaterThan:
            (this._pushAccumulatedPlainText(e),
              this._pushNode(
                this._createError(
                  e,
                  v.EscapeGreaterThan,
                  'The ">" character should be escaped using a backslash to avoid confusion with an HTML tag'
                )
              ));
            break;
          case u.Backtick:
            (this._pushAccumulatedPlainText(e),
              e.peekTokenAfterKind() === u.Backtick &&
              e.peekTokenAfterAfterKind() === u.Backtick
                ? this._pushNode(this._parseFencedCode(e))
                : this._pushNode(this._parseCodeSpan(e)));
            break;
          default:
            e.readToken();
            break;
        }
      (this._pushAccumulatedPlainText(e), this._performValidationChecks());
    }),
    (n.prototype._performValidationChecks = function () {
      var e = this._parserContext.docComment;
      (e.deprecatedBlock &&
        (ir.hasAnyTextContent(e.deprecatedBlock) ||
          this._parserContext.log.addMessageForTokenSequence(
            v.MissingDeprecationMessage,
            'The '.concat(
              e.deprecatedBlock.blockTag.tagName,
              ' block must include a deprecation message,'
            ) + ' e.g. describing the recommended alternative',
            e.deprecatedBlock.blockTag.getTokenSequence(),
            e.deprecatedBlock
          )),
        e.inheritDocTag &&
          (e.remarksBlock &&
            this._parserContext.log.addMessageForTokenSequence(
              v.InheritDocIncompatibleTag,
              'A "'.concat(
                e.remarksBlock.blockTag.tagName,
                '" block must not be used, because that'
              ) + ' content is provided by the @inheritDoc tag',
              e.remarksBlock.blockTag.getTokenSequence(),
              e.remarksBlock.blockTag
            ),
          ir.hasAnyTextContent(e.summarySection) &&
            this._parserContext.log.addMessageForTextRange(
              v.InheritDocIncompatibleSummary,
              'The summary section must not have any content, because that content is provided by the @inheritDoc tag',
              this._parserContext.commentRange
            )));
    }),
    (n.prototype._validateTagDefinition = function (e, t, r, i, o) {
      if (e) {
        var a = e.syntaxKind === R.InlineTag;
        a !== r
          ? r
            ? this._parserContext.log.addMessageForTokenSequence(
                v.TagShouldNotHaveBraces,
                'The TSDoc tag "'.concat(
                  t,
                  '" is not an inline tag; it must not be enclosed in "{ }" braces'
                ),
                i,
                o
              )
            : this._parserContext.log.addMessageForTokenSequence(
                v.InlineTagMissingBraces,
                'The TSDoc tag "'.concat(
                  t,
                  '" is an inline tag; it must be enclosed in "{ }" braces'
                ),
                i,
                o
              )
          : this._parserContext.configuration.validation
              .reportUnsupportedTags &&
            (this._parserContext.configuration.isTagSupported(e) ||
              this._parserContext.log.addMessageForTokenSequence(
                v.UnsupportedTag,
                'The TSDoc tag "'.concat(t, '" is not supported by this tool'),
                i,
                o
              ));
      } else
        this._parserContext.configuration.validation.ignoreUndefinedTags ||
          this._parserContext.log.addMessageForTokenSequence(
            v.UndefinedTag,
            'The TSDoc tag "'.concat(
              t,
              '" is not defined in this configuration'
            ),
            i,
            o
          );
    }),
    (n.prototype._pushAccumulatedPlainText = function (e) {
      e.isAccumulatedSequenceEmpty() ||
        this._pushNode(
          new Ge({
            parsed: !0,
            configuration: this._configuration,
            textExcerpt: e.extractAccumulatedSequence(),
          })
        );
    }),
    (n.prototype._parseAndPushBlock = function (e) {
      var t = this._parserContext.docComment,
        r = this._parserContext.configuration,
        i = t.modifierTagSet,
        o = this._parseBlockTag(e);
      if (o.kind !== g.BlockTag) {
        this._pushNode(o);
        return;
      }
      var a = o,
        l = r.tryGetTagDefinitionWithUpperCase(a.tagNameWithUpperCase);
      if (
        (this._validateTagDefinition(l, a.tagName, !1, a.getTokenSequence(), a),
        l)
      )
        switch (l.syntaxKind) {
          case R.BlockTag:
            if (a.tagNameWithUpperCase === G.param.tagNameWithUpperCase) {
              var c = this._parseParamBlock(e, a, G.param.tagName);
              (this._parserContext.docComment.params.add(c),
                (this._currentSection = c.content));
              return;
            } else if (
              a.tagNameWithUpperCase === G.typeParam.tagNameWithUpperCase
            ) {
              var c = this._parseParamBlock(e, a, G.typeParam.tagName);
              (this._parserContext.docComment.typeParams.add(c),
                (this._currentSection = c.content));
              return;
            } else {
              var d = new Gt({
                configuration: this._configuration,
                blockTag: a,
              });
              (this._addBlockToDocComment(d),
                (this._currentSection = d.content));
            }
            return;
          case R.ModifierTag:
            i.addTag(a);
            return;
        }
      this._pushNode(a);
    }),
    (n.prototype._addBlockToDocComment = function (e) {
      var t = this._parserContext.docComment;
      switch (e.blockTag.tagNameWithUpperCase) {
        case G.remarks.tagNameWithUpperCase:
          t.remarksBlock = e;
          break;
        case G.privateRemarks.tagNameWithUpperCase:
          t.privateRemarks = e;
          break;
        case G.deprecated.tagNameWithUpperCase:
          t.deprecatedBlock = e;
          break;
        case G.returns.tagNameWithUpperCase:
          t.returnsBlock = e;
          break;
        case G.see.tagNameWithUpperCase:
          t._appendSeeBlock(e);
          break;
        default:
          t.appendCustomBlock(e);
      }
    }),
    (n.prototype._tryParseJSDocTypeOrValueRest = function (e, t, r, i) {
      for (var o, a = 1; a > 0; ) {
        var l = e.peekTokenKind();
        switch (l) {
          case t:
            o === void 0 && a++;
            break;
          case r:
            o === void 0 && a--;
            break;
          case u.Backslash:
            o !== void 0 && (e.readToken(), (l = e.peekTokenKind()));
            break;
          case u.DoubleQuote:
          case u.SingleQuote:
          case u.Backtick:
            o === l ? (o = void 0) : o === void 0 && (o = l);
            break;
        }
        if (l === u.EndOfInput) {
          e.backtrackToMarker(i);
          return;
        }
        e.readToken();
      }
      return e.tryExtractAccumulatedSequence();
    }),
    (n.prototype._tryParseUnsupportedJSDocType = function (e, t, r) {
      if (
        (e.assertAccumulatedSequenceIsEmpty(),
        !(
          e.peekTokenKind() !== u.LeftCurlyBracket ||
          e.peekTokenAfterKind() === u.AtSign
        ))
      ) {
        var i = e.createMarker();
        e.readToken();
        var o = this._tryParseJSDocTypeOrValueRest(
          e,
          u.LeftCurlyBracket,
          u.RightCurlyBracket,
          i
        );
        if (o) {
          this._parserContext.log.addMessageForTokenSequence(
            v.ParamTagWithInvalidType,
            'The ' + r + " block should not include a JSDoc-style '{type}'",
            o,
            t
          );
          var a = this._tryReadSpacingAndNewlines(e);
          a && (o = o.getNewSequence(o.startIndex, a.endIndex));
        }
        return o;
      }
    }),
    (n.prototype._tryParseJSDocOptionalNameRest = function (e) {
      if (
        (e.assertAccumulatedSequenceIsEmpty(),
        e.peekTokenKind() !== u.EndOfInput)
      ) {
        var t = e.createMarker();
        return this._tryParseJSDocTypeOrValueRest(
          e,
          u.LeftSquareBracket,
          u.RightSquareBracket,
          t
        );
      }
    }),
    (n.prototype._parseParamBlock = function (e, t, r) {
      var i = e.createMarker(),
        o = this._tryReadSpacingAndNewlines(e),
        a = this._tryParseUnsupportedJSDocType(e, t, r),
        l;
      e.peekTokenKind() === u.LeftSquareBracket &&
        (e.readToken(), (l = e.extractAccumulatedSequence()));
      for (var c = '', d = !1; !d; )
        switch (e.peekTokenKind()) {
          case u.AsciiWord:
          case u.Period:
          case u.DollarSign:
            c += e.readToken();
            break;
          default:
            d = !0;
            break;
        }
      var m = re.explainIfInvalidUnquotedIdentifier(c);
      if (m !== void 0) {
        e.backtrackToMarker(i);
        var y = new dn({
            configuration: this._configuration,
            blockTag: t,
            parameterName: '',
          }),
          b =
            c.length > 0
              ? 'The ' +
                r +
                ' block should be followed by a valid parameter name: ' +
                m
              : 'The ' + r + ' block should be followed by a parameter name';
        return (
          this._parserContext.log.addMessageForTokenSequence(
            v.ParamTagWithInvalidName,
            b,
            t.getTokenSequence(),
            t
          ),
          y
        );
      }
      var E = e.extractAccumulatedSequence(),
        P;
      if (l) {
        P = this._tryParseJSDocOptionalNameRest(e);
        var k = l;
        (P &&
          (k = t.getTokenSequence().getNewSequence(l.startIndex, P.endIndex)),
          this._parserContext.log.addMessageForTokenSequence(
            v.ParamTagWithInvalidOptionalName,
            'The ' +
              r +
              " should not include a JSDoc-style optional name; it must not be enclosed in '[ ]' brackets.",
            k,
            t
          ));
      }
      var w = this._tryReadSpacingAndNewlines(e),
        M = this._tryParseUnsupportedJSDocType(e, t, r),
        $,
        K,
        C;
      return (
        e.peekTokenKind() === u.Hyphen
          ? (e.readToken(),
            ($ = e.extractAccumulatedSequence()),
            (K = this._tryReadSpacingAndNewlines(e)),
            (C = this._tryParseUnsupportedJSDocType(e, t, r)))
          : this._parserContext.log.addMessageForTokenSequence(
              v.ParamTagMissingHyphen,
              'The ' +
                r +
                ' block should be followed by a parameter name and then a hyphen',
              t.getTokenSequence(),
              t
            ),
        new dn({
          parsed: !0,
          configuration: this._configuration,
          blockTag: t,
          spacingBeforeParameterNameExcerpt: o,
          unsupportedJsdocTypeBeforeParameterNameExcerpt: a,
          unsupportedJsdocOptionalNameOpenBracketExcerpt: l,
          parameterNameExcerpt: E,
          parameterName: c,
          unsupportedJsdocOptionalNameRestExcerpt: P,
          spacingAfterParameterNameExcerpt: w,
          unsupportedJsdocTypeAfterParameterNameExcerpt: M,
          hyphenExcerpt: $,
          spacingAfterHyphenExcerpt: K,
          unsupportedJsdocTypeAfterHyphenExcerpt: C,
        })
      );
    }),
    (n.prototype._pushNode = function (e) {
      this._configuration.docNodeManager.isAllowedChild(g.Paragraph, e.kind)
        ? this._currentSection.appendNodeInParagraph(e)
        : this._currentSection.appendNode(e);
    }),
    (n.prototype._parseBackslashEscape = function (e) {
      e.assertAccumulatedSequenceIsEmpty();
      var t = e.createMarker();
      if ((e.readToken(), e.peekTokenKind() === u.EndOfInput))
        return this._backtrackAndCreateError(
          e,
          t,
          v.UnnecessaryBackslash,
          'A backslash must precede another character that is being escaped'
        );
      var r = e.readToken();
      if (!gn.isPunctuation(r.kind))
        return this._backtrackAndCreateError(
          e,
          t,
          v.UnnecessaryBackslash,
          'A backslash can only be used to escape a punctuation character'
        );
      var i = e.extractAccumulatedSequence();
      return new Yn({
        parsed: !0,
        configuration: this._configuration,
        escapeStyle: rr.CommonMarkBackslash,
        encodedTextExcerpt: i,
        decodedText: r.toString(),
      });
    }),
    (n.prototype._parseBlockTag = function (e) {
      e.assertAccumulatedSequenceIsEmpty();
      var t = e.createMarker();
      if (e.peekTokenKind() !== u.AtSign)
        return this._backtrackAndCreateError(
          e,
          t,
          v.MissingTag,
          'Expecting a TSDoc tag starting with "@"'
        );
      switch (e.peekPreviousTokenKind()) {
        case u.EndOfInput:
        case u.Spacing:
        case u.Newline:
          break;
        default:
          return this._backtrackAndCreateError(
            e,
            t,
            v.AtSignInWord,
            'The "@" character looks like part of a TSDoc tag; use a backslash to escape it'
          );
      }
      var r = e.readToken().toString();
      if (e.peekTokenKind() !== u.AsciiWord)
        return this._backtrackAndCreateError(
          e,
          t,
          v.AtSignWithoutTagName,
          'Expecting a TSDoc tag name after "@"; if it is not a tag, use a backslash to escape this character'
        );
      for (var i = e.createMarker(); e.peekTokenKind() === u.AsciiWord; )
        r += e.readToken().toString();
      switch (e.peekTokenKind()) {
        case u.Spacing:
        case u.Newline:
        case u.EndOfInput:
          break;
        default:
          var o = e.peekToken().range.toString()[0];
          return this._backtrackAndCreateError(
            e,
            t,
            v.CharactersAfterBlockTag,
            'The token "'.concat(
              r,
              '" looks like a TSDoc tag but contains an invalid character'
            ) +
              ' '.concat(
                JSON.stringify(o),
                '; if it is not a tag, use a backslash to escape the "@"'
              )
          );
      }
      if (re.explainIfInvalidTSDocTagName(r)) {
        var a = this._createFailureForTokensSince(
          e,
          v.MalformedTagName,
          'A TSDoc tag name must start with a letter and contain only letters and numbers',
          i
        );
        return this._backtrackAndCreateErrorForFailure(e, t, '', a);
      }
      return new Hn({
        parsed: !0,
        configuration: this._configuration,
        tagName: r,
        tagNameExcerpt: e.extractAccumulatedSequence(),
      });
    }),
    (n.prototype._parseInlineTag = function (e) {
      e.assertAccumulatedSequenceIsEmpty();
      var t = e.createMarker();
      if (e.peekTokenKind() !== u.LeftCurlyBracket)
        return this._backtrackAndCreateError(
          e,
          t,
          v.MissingTag,
          'Expecting a TSDoc tag starting with "{"'
        );
      e.readToken();
      var r = e.extractAccumulatedSequence(),
        i = e.createMarker();
      if (e.peekTokenKind() !== u.AtSign)
        return this._backtrackAndCreateError(
          e,
          t,
          v.MalformedInlineTag,
          'Expecting a TSDoc tag starting with "{@"'
        );
      for (
        var o = e.readToken().toString();
        e.peekTokenKind() === u.AsciiWord;

      )
        o += e.readToken().toString();
      if (o === '@') {
        var a = this._createFailureForTokensSince(
          e,
          v.MalformedInlineTag,
          'Expecting a TSDoc inline tag name after the "{@" characters',
          i
        );
        return this._backtrackAndCreateErrorRangeForFailure(e, t, i, '', a);
      }
      if (re.explainIfInvalidTSDocTagName(o)) {
        var a = this._createFailureForTokensSince(
          e,
          v.MalformedTagName,
          'A TSDoc tag name must start with a letter and contain only letters and numbers',
          i
        );
        return this._backtrackAndCreateErrorRangeForFailure(e, t, i, '', a);
      }
      var l = e.extractAccumulatedSequence(),
        c = this._tryReadSpacingAndNewlines(e);
      if (c === void 0 && e.peekTokenKind() !== u.RightCurlyBracket) {
        var d = e.peekToken().range.toString()[0],
          a = this._createFailureForToken(
            e,
            v.CharactersAfterInlineTag,
            'The character '.concat(
              JSON.stringify(d),
              ' cannot appear after the TSDoc tag name; expecting a space'
            )
          );
        return this._backtrackAndCreateErrorRangeForFailure(e, t, i, '', a);
      }
      for (var m = !1; !m; )
        switch (e.peekTokenKind()) {
          case u.EndOfInput:
            return this._backtrackAndCreateErrorRange(
              e,
              t,
              i,
              v.InlineTagMissingRightBrace,
              'The TSDoc inline tag name is missing its closing "}"'
            );
          case u.Backslash:
            if ((e.readToken(), !gn.isPunctuation(e.peekTokenKind()))) {
              var a = this._createFailureForToken(
                e,
                v.UnnecessaryBackslash,
                'A backslash can only be used to escape a punctuation character'
              );
              return this._backtrackAndCreateErrorRangeForFailure(
                e,
                t,
                i,
                'Error reading inline TSDoc tag: ',
                a
              );
            }
            e.readToken();
            break;
          case u.LeftCurlyBracket: {
            var a = this._createFailureForToken(
              e,
              v.InlineTagUnescapedBrace,
              'The "{" character must be escaped with a backslash when used inside a TSDoc inline tag'
            );
            return this._backtrackAndCreateErrorRangeForFailure(e, t, i, '', a);
          }
          case u.RightCurlyBracket:
            m = !0;
            break;
          default:
            e.readToken();
            break;
        }
      var y = e.tryExtractAccumulatedSequence();
      e.readToken();
      var b = e.extractAccumulatedSequence(),
        E = {
          parsed: !0,
          configuration: this._configuration,
          openingDelimiterExcerpt: r,
          tagNameExcerpt: l,
          tagName: o,
          spacingAfterTagNameExcerpt: c,
          tagContentExcerpt: y,
          closingDelimiterExcerpt: b,
        },
        P = o.toUpperCase(),
        k = new Ir(
          this._parserContext,
          y || xt.createEmpty(this._parserContext)
        ),
        w;
      switch (P) {
        case G.inheritDoc.tagNameWithUpperCase:
          w = this._parseInheritDocTag(E, k);
          break;
        case G.link.tagNameWithUpperCase:
          w = this._parseLinkTag(E, k);
          break;
        default:
          w = new Wt(E);
      }
      var M =
        this._parserContext.configuration.tryGetTagDefinitionWithUpperCase(P);
      return (this._validateTagDefinition(M, o, !0, l, w), w);
    }),
    (n.prototype._parseInheritDocTag = function (e, t) {
      var r = new Wt(e),
        i = or({}, e);
      if (t.peekTokenKind() !== u.EndOfInput) {
        if (
          ((i.declarationReference = this._parseDeclarationReference(
            t,
            e.tagNameExcerpt,
            r
          )),
          !i.declarationReference)
        )
          return r;
        if (t.peekTokenKind() !== u.EndOfInput)
          return (
            t.readToken(),
            this._parserContext.log.addMessageForTokenSequence(
              v.InheritDocTagSyntax,
              'Unexpected character after declaration reference',
              t.extractAccumulatedSequence(),
              r
            ),
            r
          );
      }
      return new un(i);
    }),
    (n.prototype._parseLinkTag = function (e, t) {
      var r = new Wt(e),
        i = or({}, e);
      if (!e.tagContentExcerpt)
        return (
          this._parserContext.log.addMessageForTokenSequence(
            v.LinkTagEmpty,
            'The @link tag content is missing',
            i.tagNameExcerpt,
            r
          ),
          r
        );
      for (
        var o =
            t.peekTokenKind() === u.Slash && t.peekTokenAfterKind() === u.Slash,
          a = t.createMarker(),
          l = o;
        !l;

      )
        switch (t.peekTokenKind()) {
          case u.AsciiWord:
          case u.Period:
          case u.Hyphen:
          case u.Plus:
            t.readToken();
            break;
          case u.Colon:
            (t.readToken(),
              (o =
                t.peekTokenKind() === u.Slash &&
                t.peekTokenAfterKind() === u.Slash),
              (l = !0));
            break;
          default:
            l = !0;
        }
      if ((t.backtrackToMarker(a), o)) {
        if (!this._parseLinkTagUrlDestination(t, i, e.tagNameExcerpt, r))
          return r;
      } else if (!this._parseLinkTagCodeDestination(t, i, e.tagNameExcerpt, r))
        return r;
      if (t.peekTokenKind() === u.Spacing)
        throw new Error('Unconsumed spacing encountered after construct');
      if (t.peekTokenKind() === u.Pipe) {
        (t.readToken(),
          (i.pipeExcerpt = t.extractAccumulatedSequence()),
          (i.spacingAfterPipeExcerpt = this._tryReadSpacingAndNewlines(t)),
          (l = !1));
        for (var c = void 0; !l; )
          switch (t.peekTokenKind()) {
            case u.EndOfInput:
              l = !0;
              break;
            case u.Pipe:
            case u.LeftCurlyBracket:
              var d = t.readToken().toString();
              return (
                this._parserContext.log.addMessageForTokenSequence(
                  v.LinkTagUnescapedText,
                  'The "'.concat(
                    d,
                    '" character may not be used in the link text without escaping it'
                  ),
                  t.extractAccumulatedSequence(),
                  r
                ),
                r
              );
            case u.Spacing:
            case u.Newline:
              t.readToken();
              break;
            default:
              ((c = t.createMarker() + 1), t.readToken());
          }
        var m = t.tryExtractAccumulatedSequence();
        m &&
          (c === void 0
            ? (i.spacingAfterLinkTextExcerpt = m)
            : c >= m.endIndex
              ? (i.linkTextExcerpt = m)
              : ((i.linkTextExcerpt = m.getNewSequence(m.startIndex, c)),
                (i.spacingAfterLinkTextExcerpt = m.getNewSequence(
                  c,
                  m.endIndex
                ))));
      } else if (t.peekTokenKind() !== u.EndOfInput)
        return (
          t.readToken(),
          this._parserContext.log.addMessageForTokenSequence(
            v.LinkTagDestinationSyntax,
            'Unexpected character after link destination',
            t.extractAccumulatedSequence(),
            r
          ),
          r
        );
      return new zn(i);
    }),
    (n.prototype._parseLinkTagUrlDestination = function (e, t, r, i) {
      for (var o = '', a = !1; !a; )
        switch (e.peekTokenKind()) {
          case u.Spacing:
          case u.Newline:
          case u.EndOfInput:
          case u.Pipe:
          case u.RightCurlyBracket:
            a = !0;
            break;
          default:
            o += e.readToken();
            break;
        }
      if (o.length === 0)
        throw new Error('Missing URL in _parseLinkTagUrlDestination()');
      var l = e.extractAccumulatedSequence(),
        c = re.explainIfInvalidLinkUrl(o);
      return c
        ? (this._parserContext.log.addMessageForTokenSequence(
            v.LinkTagInvalidUrl,
            c,
            l,
            i
          ),
          !1)
        : ((t.urlDestinationExcerpt = l),
          (t.spacingAfterDestinationExcerpt =
            this._tryReadSpacingAndNewlines(e)),
          !0);
    }),
    (n.prototype._parseLinkTagCodeDestination = function (e, t, r, i) {
      return (
        (t.codeDestination = this._parseDeclarationReference(e, r, i)),
        !!t.codeDestination
      );
    }),
    (n.prototype._parseDeclarationReference = function (e, t, r) {
      e.assertAccumulatedSequenceIsEmpty();
      for (var i = e.createMarker(), o = !1, a = !0, l = !1, c = !1; !c; )
        switch (e.peekTokenKind()) {
          case u.DoubleQuote:
          case u.EndOfInput:
          case u.LeftCurlyBracket:
          case u.LeftParenthesis:
          case u.LeftSquareBracket:
          case u.Newline:
          case u.Pipe:
          case u.RightCurlyBracket:
          case u.RightParenthesis:
          case u.RightSquareBracket:
          case u.SingleQuote:
          case u.Spacing:
            c = !0;
            break;
          case u.PoundSymbol:
            ((o = !0), (c = !0));
            break;
          case u.Slash:
          case u.AtSign:
            (a && (l = !0), e.readToken());
            break;
          case u.AsciiWord:
          case u.Period:
          case u.Hyphen:
            e.readToken();
            break;
          default:
            ((a = !1), e.readToken());
        }
      if (!o && l) {
        this._parserContext.log.addMessageForTokenSequence(
          v.ReferenceMissingHash,
          'The declaration reference appears to contain a package name or import path, but it is missing the "#" delimiter',
          e.extractAccumulatedSequence(),
          r
        );
        return;
      }
      e.backtrackToMarker(i);
      var d, m, y, b;
      if (o) {
        if (e.peekTokenKind() !== u.Period) {
          var E = e.peekTokenKind() === u.AtSign,
            P = !1;
          for (c = !1; !c; )
            switch (e.peekTokenKind()) {
              case u.EndOfInput:
                throw new Error('Expecting pound symbol');
              case u.Slash:
                E && !P ? (e.readToken(), (P = !0)) : (c = !0);
                break;
              case u.PoundSymbol:
                c = !0;
                break;
              default:
                e.readToken();
            }
          if (!e.isAccumulatedSequenceEmpty()) {
            d = e.extractAccumulatedSequence();
            var k = re.explainIfInvalidPackageName(d.toString());
            if (k) {
              this._parserContext.log.addMessageForTokenSequence(
                v.ReferenceMalformedPackageName,
                k,
                d,
                r
              );
              return;
            }
          }
        }
        for (c = !1; !c; )
          switch (e.peekTokenKind()) {
            case u.EndOfInput:
              throw new Error('Expecting pound symbol');
            case u.PoundSymbol:
              c = !0;
              break;
            default:
              e.readToken();
          }
        if (!e.isAccumulatedSequenceEmpty()) {
          m = e.extractAccumulatedSequence();
          var k = re.explainIfInvalidImportPath(m.toString(), !!d);
          if (k) {
            this._parserContext.log.addMessageForTokenSequence(
              v.ReferenceMalformedImportPath,
              k,
              m,
              r
            );
            return;
          }
        }
        if (e.peekTokenKind() !== u.PoundSymbol)
          throw new Error('Expecting pound symbol');
        if (
          (e.readToken(),
          (y = e.extractAccumulatedSequence()),
          (b = this._tryReadSpacingAndNewlines(e)),
          d === void 0 && m === void 0)
        ) {
          this._parserContext.log.addMessageForTokenSequence(
            v.ReferenceHashSyntax,
            'The hash character must be preceded by a package name or import path',
            y,
            r
          );
          return;
        }
      }
      var w = [];
      for (c = !1; !c; )
        switch (e.peekTokenKind()) {
          case u.Period:
          case u.LeftParenthesis:
          case u.AsciiWord:
          case u.Colon:
          case u.LeftSquareBracket:
          case u.DoubleQuote:
            var M = w.length > 0,
              $ = this._parseMemberReference(e, M, t, r);
            if (!$) return;
            w.push($);
            break;
          default:
            c = !0;
        }
      if (d === void 0 && m === void 0 && w.length === 0) {
        this._parserContext.log.addMessageForTokenSequence(
          v.MissingReference,
          'Expecting a declaration reference',
          t,
          r
        );
        return;
      }
      return new Rn({
        parsed: !0,
        configuration: this._configuration,
        packageNameExcerpt: d,
        importPathExcerpt: m,
        importHashExcerpt: y,
        spacingAfterImportHashExcerpt: b,
        memberReferences: w,
      });
    }),
    (n.prototype._parseMemberReference = function (e, t, r, i) {
      var o = { parsed: !0, configuration: this._configuration };
      if (t) {
        if (e.peekTokenKind() !== u.Period) {
          this._parserContext.log.addMessageForTokenSequence(
            v.ReferenceMissingDot,
            'Expecting a period before the next component of a declaration reference',
            r,
            i
          );
          return;
        }
        (e.readToken(),
          (o.dotExcerpt = e.extractAccumulatedSequence()),
          (o.spacingAfterDotExcerpt = this._tryReadSpacingAndNewlines(e)));
      }
      if (
        (e.peekTokenKind() === u.LeftParenthesis &&
          (e.readToken(),
          (o.leftParenthesisExcerpt = e.extractAccumulatedSequence()),
          (o.spacingAfterLeftParenthesisExcerpt =
            this._tryReadSpacingAndNewlines(e))),
        e.peekTokenKind() === u.LeftSquareBracket)
      ) {
        if (((o.memberSymbol = this._parseMemberSymbol(e, i)), !o.memberSymbol))
          return;
      } else if (
        ((o.memberIdentifier = this._parseMemberIdentifier(e, r, i)),
        !o.memberIdentifier)
      )
        return;
      if (
        ((o.spacingAfterMemberExcerpt = this._tryReadSpacingAndNewlines(e)),
        e.peekTokenKind() === u.Colon)
      ) {
        if (
          (e.readToken(),
          (o.colonExcerpt = e.extractAccumulatedSequence()),
          (o.spacingAfterColonExcerpt = this._tryReadSpacingAndNewlines(e)),
          !o.leftParenthesisExcerpt)
        ) {
          this._parserContext.log.addMessageForTokenSequence(
            v.ReferenceSelectorMissingParens,
            'Syntax error in declaration reference: the member selector must be enclosed in parentheses',
            o.colonExcerpt,
            i
          );
          return;
        }
        if (
          ((o.selector = this._parseMemberSelector(e, o.colonExcerpt, i)),
          !o.selector)
        )
          return;
        o.spacingAfterSelectorExcerpt = this._tryReadSpacingAndNewlines(e);
      } else if (o.leftParenthesisExcerpt) {
        this._parserContext.log.addMessageForTokenSequence(
          v.ReferenceMissingColon,
          'Expecting a colon after the identifier because the expression is in parentheses',
          o.leftParenthesisExcerpt,
          i
        );
        return;
      }
      if (o.leftParenthesisExcerpt) {
        if (e.peekTokenKind() !== u.RightParenthesis) {
          this._parserContext.log.addMessageForTokenSequence(
            v.ReferenceMissingRightParen,
            'Expecting a matching right parenthesis',
            o.leftParenthesisExcerpt,
            i
          );
          return;
        }
        (e.readToken(),
          (o.rightParenthesisExcerpt = e.extractAccumulatedSequence()),
          (o.spacingAfterRightParenthesisExcerpt =
            this._tryReadSpacingAndNewlines(e)));
      }
      return new Jn(o);
    }),
    (n.prototype._parseMemberSymbol = function (e, t) {
      if (e.peekTokenKind() !== u.LeftSquareBracket)
        throw new Error('Expecting "["');
      e.readToken();
      var r = e.extractAccumulatedSequence(),
        i = this._tryReadSpacingAndNewlines(e),
        o = this._parseDeclarationReference(e, r, t);
      if (!o) {
        this._parserContext.log.addMessageForTokenSequence(
          v.ReferenceSymbolSyntax,
          'Missing declaration reference in symbol reference',
          r,
          t
        );
        return;
      }
      if (e.peekTokenKind() !== u.RightSquareBracket) {
        this._parserContext.log.addMessageForTokenSequence(
          v.ReferenceMissingRightBracket,
          'Missing closing square bracket for symbol reference',
          r,
          t
        );
        return;
      }
      e.readToken();
      var a = e.extractAccumulatedSequence();
      return new Xn({
        parsed: !0,
        configuration: this._configuration,
        leftBracketExcerpt: r,
        spacingAfterLeftBracketExcerpt: i,
        symbolReference: o,
        rightBracketExcerpt: a,
      });
    }),
    (n.prototype._parseMemberIdentifier = function (e, t, r) {
      var i = void 0,
        o = void 0;
      if (e.peekTokenKind() === u.DoubleQuote) {
        for (
          e.readToken(), i = e.extractAccumulatedSequence();
          e.peekTokenKind() !== u.DoubleQuote;

        ) {
          if (e.peekTokenKind() === u.EndOfInput) {
            this._parserContext.log.addMessageForTokenSequence(
              v.ReferenceMissingQuote,
              'Unexpected end of input inside quoted member identifier',
              i,
              r
            );
            return;
          }
          e.readToken();
        }
        if (e.isAccumulatedSequenceEmpty()) {
          this._parserContext.log.addMessageForTokenSequence(
            v.ReferenceEmptyIdentifier,
            'The quoted identifier cannot be empty',
            i,
            r
          );
          return;
        }
        var a = e.extractAccumulatedSequence();
        return (
          e.readToken(),
          (o = e.extractAccumulatedSequence()),
          new fn({
            parsed: !0,
            configuration: this._configuration,
            leftQuoteExcerpt: i,
            identifierExcerpt: a,
            rightQuoteExcerpt: o,
          })
        );
      } else {
        for (var l = !1; !l; )
          switch (e.peekTokenKind()) {
            case u.AsciiWord:
            case u.DollarSign:
              e.readToken();
              break;
            default:
              l = !0;
              break;
          }
        if (e.isAccumulatedSequenceEmpty()) {
          this._parserContext.log.addMessageForTokenSequence(
            v.ReferenceMissingIdentifier,
            'Syntax error in declaration reference: expecting a member identifier',
            t,
            r
          );
          return;
        }
        var a = e.extractAccumulatedSequence(),
          c = a.toString(),
          d = re.explainIfInvalidUnquotedMemberIdentifier(c);
        if (d) {
          this._parserContext.log.addMessageForTokenSequence(
            v.ReferenceUnquotedIdentifier,
            d,
            a,
            r
          );
          return;
        }
        return new fn({
          parsed: !0,
          configuration: this._configuration,
          leftQuoteExcerpt: i,
          identifierExcerpt: a,
          rightQuoteExcerpt: o,
        });
      }
    }),
    (n.prototype._parseMemberSelector = function (e, t, r) {
      e.peekTokenKind() !== u.AsciiWord &&
        this._parserContext.log.addMessageForTokenSequence(
          v.ReferenceMissingLabel,
          'Expecting a selector label after the colon',
          t,
          r
        );
      var i = e.readToken().toString(),
        o = e.extractAccumulatedSequence(),
        a = new Qn({
          parsed: !0,
          configuration: this._configuration,
          selectorExcerpt: o,
          selector: i,
        });
      if (a.errorMessage) {
        this._parserContext.log.addMessageForTokenSequence(
          v.ReferenceSelectorSyntax,
          a.errorMessage,
          o,
          r
        );
        return;
      }
      return a;
    }),
    (n.prototype._parseHtmlStartTag = function (e) {
      e.assertAccumulatedSequenceIsEmpty();
      var t = e.createMarker(),
        r = e.readToken();
      if (r.kind !== u.LessThan)
        throw new Error('Expecting an HTML tag starting with "<"');
      var i = e.extractAccumulatedSequence(),
        o = this._parseHtmlName(e);
      if (hn(o))
        return this._backtrackAndCreateErrorForFailure(
          e,
          t,
          'Invalid HTML element: ',
          o
        );
      for (
        var a = this._tryReadSpacingAndNewlines(e), l = [];
        e.peekTokenKind() === u.AsciiWord;

      ) {
        var c = this._parseHtmlAttribute(e);
        if (hn(c))
          return this._backtrackAndCreateErrorForFailure(
            e,
            t,
            'The HTML element has an invalid attribute: ',
            c
          );
        l.push(c);
      }
      e.assertAccumulatedSequenceIsEmpty();
      var d = e.createMarker(),
        m = !1;
      if (
        (e.peekTokenKind() === u.Slash && (e.readToken(), (m = !0)),
        e.peekTokenKind() !== u.GreaterThan)
      ) {
        var y = this._createFailureForTokensSince(
          e,
          v.HtmlTagMissingGreaterThan,
          'Expecting an attribute or ">" or "/>"',
          d
        );
        return this._backtrackAndCreateErrorForFailure(
          e,
          t,
          'The HTML tag has invalid syntax: ',
          y
        );
      }
      e.readToken();
      var b = e.extractAccumulatedSequence();
      return new Vn({
        parsed: !0,
        configuration: this._configuration,
        openingDelimiterExcerpt: i,
        nameExcerpt: o,
        spacingAfterNameExcerpt: a,
        htmlAttributes: l,
        selfClosingTag: m,
        closingDelimiterExcerpt: b,
      });
    }),
    (n.prototype._parseHtmlAttribute = function (e) {
      e.assertAccumulatedSequenceIsEmpty();
      var t = this._parseHtmlName(e);
      if (hn(t)) return t;
      var r = this._tryReadSpacingAndNewlines(e);
      if (e.peekTokenKind() !== u.Equals)
        return this._createFailureForToken(
          e,
          v.HtmlTagMissingEquals,
          'Expecting "=" after HTML attribute name'
        );
      e.readToken();
      var i = e.extractAccumulatedSequence(),
        o = this._tryReadSpacingAndNewlines(e),
        a = this._parseHtmlString(e);
      if (hn(a)) return a;
      var l = e.extractAccumulatedSequence(),
        c = this._tryReadSpacingAndNewlines(e);
      return new Gn({
        parsed: !0,
        configuration: this._configuration,
        nameExcerpt: t,
        spacingAfterNameExcerpt: r,
        equalsExcerpt: i,
        spacingAfterEqualsExcerpt: o,
        valueExcerpt: l,
        spacingAfterValueExcerpt: c,
      });
    }),
    (n.prototype._parseHtmlString = function (e) {
      var t = e.createMarker(),
        r = e.peekTokenKind();
      if (r !== u.DoubleQuote && r !== u.SingleQuote)
        return this._createFailureForToken(
          e,
          v.HtmlTagMissingString,
          'Expecting an HTML string starting with a single-quote or double-quote character'
        );
      e.readToken();
      for (var i = ''; ; ) {
        var o = e.peekTokenKind();
        if (o === r) {
          e.readToken();
          break;
        }
        if (o === u.EndOfInput || o === u.Newline)
          return this._createFailureForToken(
            e,
            v.HtmlStringMissingQuote,
            'The HTML string is missing its closing quote',
            t
          );
        i += e.readToken().toString();
      }
      return e.peekTokenKind() === u.AsciiWord
        ? this._createFailureForToken(
            e,
            v.TextAfterHtmlString,
            'The next character after a closing quote must be spacing or punctuation'
          )
        : i;
    }),
    (n.prototype._parseHtmlEndTag = function (e) {
      e.assertAccumulatedSequenceIsEmpty();
      var t = e.createMarker(),
        r = e.peekToken();
      if (r.kind !== u.LessThan)
        return this._backtrackAndCreateError(
          e,
          t,
          v.MissingHtmlEndTag,
          'Expecting an HTML tag starting with "</"'
        );
      e.readToken();
      var i = e.peekToken();
      if (i.kind !== u.Slash)
        return this._backtrackAndCreateError(
          e,
          t,
          v.MissingHtmlEndTag,
          'Expecting an HTML tag starting with "</"'
        );
      e.readToken();
      var o = e.extractAccumulatedSequence(),
        a = this._parseHtmlName(e);
      if (hn(a))
        return this._backtrackAndCreateErrorForFailure(
          e,
          t,
          'Expecting an HTML element name: ',
          a
        );
      var l = this._tryReadSpacingAndNewlines(e);
      if (e.peekTokenKind() !== u.GreaterThan) {
        var c = this._createFailureForToken(
          e,
          v.HtmlTagMissingGreaterThan,
          'Expecting a closing ">" for the HTML tag'
        );
        return this._backtrackAndCreateErrorForFailure(e, t, '', c);
      }
      e.readToken();
      var d = e.extractAccumulatedSequence();
      return new Wn({
        parsed: !0,
        configuration: this._configuration,
        openingDelimiterExcerpt: o,
        nameExcerpt: a,
        spacingAfterNameExcerpt: l,
        closingDelimiterExcerpt: d,
      });
    }),
    (n.prototype._parseHtmlName = function (e) {
      var t = e.createMarker();
      if (e.peekTokenKind() === u.Spacing)
        return this._createFailureForTokensSince(
          e,
          v.MalformedHtmlName,
          'A space is not allowed here',
          t
        );
      for (var r = !1; !r; )
        switch (e.peekTokenKind()) {
          case u.Hyphen:
          case u.Period:
          case u.AsciiWord:
            e.readToken();
            break;
          default:
            r = !0;
            break;
        }
      var i = e.tryExtractAccumulatedSequence();
      if (!i)
        return this._createFailureForToken(
          e,
          v.MalformedHtmlName,
          'Expecting an HTML name'
        );
      var o = i.toString(),
        a = re.explainIfInvalidHtmlName(o);
      return a
        ? this._createFailureForTokensSince(e, v.MalformedHtmlName, a, t)
        : this._configuration.validation.reportUnsupportedHtmlElements &&
            !this._configuration.isHtmlElementSupported(o)
          ? this._createFailureForToken(
              e,
              v.UnsupportedHtmlElementName,
              'The HTML element name '.concat(
                JSON.stringify(o),
                ' is not defined by your TSDoc configuration'
              ),
              t
            )
          : i;
    }),
    (n.prototype._parseFencedCode = function (e) {
      e.assertAccumulatedSequenceIsEmpty();
      var t = e.createMarker(),
        r = t + 2;
      switch (e.peekPreviousTokenKind()) {
        case u.Newline:
        case u.EndOfInput:
          break;
        default:
          return this._backtrackAndCreateErrorRange(
            e,
            t,
            r,
            v.CodeFenceOpeningIndent,
            'The opening backtick for a code fence must appear at the start of the line'
          );
      }
      var i = '';
      if (
        ((i += e.readToken()),
        (i += e.readToken()),
        (i += e.readToken()),
        i !== '```')
      )
        throw new Error('Expecting three backticks');
      for (
        var o = e.extractAccumulatedSequence();
        e.peekTokenKind() === u.Spacing;

      )
        e.readToken();
      for (var a = e.tryExtractAccumulatedSequence(), l = !1, c = void 0; !l; )
        switch (e.peekTokenKind()) {
          case u.Spacing:
          case u.Newline:
            (c === void 0 && (c = e.createMarker()),
              e.peekTokenKind() === u.Newline && (l = !0),
              e.readToken());
            break;
          case u.Backtick:
            var d = this._createFailureForToken(
              e,
              v.CodeFenceSpecifierSyntax,
              'The language specifier cannot contain backtick characters'
            );
            return this._backtrackAndCreateErrorRangeForFailure(
              e,
              t,
              r,
              'Error parsing code fence: ',
              d
            );
          case u.EndOfInput:
            var m = this._createFailureForToken(
              e,
              v.CodeFenceMissingDelimiter,
              'Missing closing delimiter'
            );
            return this._backtrackAndCreateErrorRangeForFailure(
              e,
              t,
              r,
              'Error parsing code fence: ',
              m
            );
          default:
            ((c = void 0), e.readToken());
            break;
        }
      var y = e.extractAccumulatedSequence(),
        b = y.getNewSequence(y.startIndex, c),
        E = y.getNewSequence(c, y.endIndex),
        P = -1,
        k = -1;
      l = !1;
      for (var w; !l; )
        switch (e.peekTokenKind()) {
          case u.EndOfInput:
            var m = this._createFailureForToken(
              e,
              v.CodeFenceMissingDelimiter,
              'Missing closing delimiter'
            );
            return this._backtrackAndCreateErrorRangeForFailure(
              e,
              t,
              r,
              'Error parsing code fence: ',
              m
            );
          case u.Newline:
            for (
              w = e.readToken(), P = e.createMarker();
              e.peekTokenKind() === u.Spacing;

            )
              w = e.readToken();
            if (
              e.peekTokenKind() !== u.Backtick ||
              ((k = e.createMarker()),
              e.readToken(),
              e.peekTokenKind() !== u.Backtick) ||
              (e.readToken(), e.peekTokenKind() !== u.Backtick)
            )
              break;
            (e.readToken(), (l = !0));
            break;
          default:
            e.readToken();
            break;
        }
      w.kind !== u.Newline &&
        this._parserContext.log.addMessageForTextRange(
          v.CodeFenceClosingIndent,
          'The closing delimiter for a code fence must not be indented',
          w.range
        );
      var M = e.extractAccumulatedSequence(),
        $ = M.getNewSequence(M.startIndex, P),
        K = M.getNewSequence(P, k),
        C = M.getNewSequence(k, M.endIndex);
      for (l = !1; !l; )
        switch (e.peekTokenKind()) {
          case u.Spacing:
            e.readToken();
            break;
          case u.Newline:
            ((l = !0), e.readToken());
            break;
          case u.EndOfInput:
            l = !0;
            break;
          default:
            (this._parserContext.log.addMessageForTextRange(
              v.CodeFenceClosingSyntax,
              'Unexpected characters after closing delimiter for code fence',
              e.peekToken().range
            ),
              (l = !0));
            break;
        }
      var H = e.tryExtractAccumulatedSequence();
      return new Un({
        parsed: !0,
        configuration: this._configuration,
        openingFenceExcerpt: o,
        spacingAfterOpeningFenceExcerpt: a,
        languageExcerpt: b,
        spacingAfterLanguageExcerpt: E,
        codeExcerpt: $,
        spacingBeforeClosingFenceExcerpt: K,
        closingFenceExcerpt: C,
        spacingAfterClosingFenceExcerpt: H,
      });
    }),
    (n.prototype._parseCodeSpan = function (e) {
      e.assertAccumulatedSequenceIsEmpty();
      var t = e.createMarker();
      if (e.peekTokenKind() !== u.Backtick)
        throw new Error(
          'Expecting a code span starting with a backtick character "`"'
        );
      e.readToken();
      for (var r = e.extractAccumulatedSequence(), i = void 0, o = void 0; ; ) {
        var a = e.peekTokenKind();
        if (a === u.Backtick) {
          if (e.isAccumulatedSequenceEmpty())
            return this._backtrackAndCreateErrorRange(
              e,
              t,
              t + 1,
              v.CodeSpanEmpty,
              'A code span must contain at least one character between the backticks'
            );
          ((i = e.extractAccumulatedSequence()),
            e.readToken(),
            (o = e.extractAccumulatedSequence()));
          break;
        }
        if (a === u.EndOfInput || a === u.Newline)
          return this._backtrackAndCreateError(
            e,
            t,
            v.CodeSpanMissingDelimiter,
            'The code span is missing its closing backtick'
          );
        e.readToken();
      }
      return new $n({
        parsed: !0,
        configuration: this._configuration,
        openingDelimiterExcerpt: r,
        codeExcerpt: i,
        closingDelimiterExcerpt: o,
      });
    }),
    (n.prototype._tryReadSpacingAndNewlines = function (e) {
      var t = !1;
      do
        switch (e.peekTokenKind()) {
          case u.Spacing:
          case u.Newline:
            e.readToken();
            break;
          default:
            t = !0;
            break;
        }
      while (!t);
      return e.tryExtractAccumulatedSequence();
    }),
    (n.prototype._createError = function (e, t, r) {
      e.readToken();
      var i = e.extractAccumulatedSequence(),
        o = new St({
          parsed: !0,
          configuration: this._configuration,
          textExcerpt: i,
          messageId: t,
          errorMessage: r,
          errorLocation: i,
        });
      return (this._parserContext.log.addMessageForDocErrorText(o), o);
    }),
    (n.prototype._backtrackAndCreateError = function (e, t, r, i) {
      return (e.backtrackToMarker(t), this._createError(e, r, i));
    }),
    (n.prototype._backtrackAndCreateErrorRange = function (e, t, r, i, o) {
      for (e.backtrackToMarker(t); e.createMarker() !== r; ) e.readToken();
      e.peekTokenKind() !== u.EndOfInput && e.readToken();
      var a = e.extractAccumulatedSequence(),
        l = new St({
          parsed: !0,
          configuration: this._configuration,
          textExcerpt: a,
          messageId: i,
          errorMessage: o,
          errorLocation: a,
        });
      return (this._parserContext.log.addMessageForDocErrorText(l), l);
    }),
    (n.prototype._backtrackAndCreateErrorForFailure = function (e, t, r, i) {
      (e.backtrackToMarker(t), e.readToken());
      var o = e.extractAccumulatedSequence(),
        a = new St({
          parsed: !0,
          configuration: this._configuration,
          textExcerpt: o,
          messageId: i.failureMessageId,
          errorMessage: r + i.failureMessage,
          errorLocation: i.failureLocation,
        });
      return (this._parserContext.log.addMessageForDocErrorText(a), a);
    }),
    (n.prototype._backtrackAndCreateErrorRangeForFailure = function (
      e,
      t,
      r,
      i,
      o
    ) {
      for (e.backtrackToMarker(t); e.createMarker() !== r; ) e.readToken();
      e.peekTokenKind() !== u.EndOfInput && e.readToken();
      var a = e.extractAccumulatedSequence(),
        l = new St({
          parsed: !0,
          configuration: this._configuration,
          textExcerpt: a,
          messageId: o.failureMessageId,
          errorMessage: i + o.failureMessage,
          errorLocation: o.failureLocation,
        });
      return (this._parserContext.log.addMessageForDocErrorText(l), l);
    }),
    (n.prototype._createFailureForToken = function (e, t, r, i) {
      i || (i = e.createMarker());
      var o = new xt({
        parserContext: this._parserContext,
        startIndex: i,
        endIndex: i + 1,
      });
      return { failureMessageId: t, failureMessage: r, failureLocation: o };
    }),
    (n.prototype._createFailureForTokensSince = function (e, t, r, i) {
      var o = e.createMarker();
      if (o < i) throw new Error('Invalid startMarker');
      o === i && ++o;
      var a = new xt({
        parserContext: this._parserContext,
        startIndex: i,
        endIndex: o,
      });
      return { failureMessageId: t, failureMessage: r, failureLocation: a };
    }),
    n
  );
})();
var eo = (function () {
  function n() {}
  return (
    (n.splitParagraphs = function (e) {
      if (e instanceof _t) n.splitParagraphsForSection(e);
      else
        for (var t = 0, r = e.getChildNodes(); t < r.length; t++) {
          var i = r[t];
          n.splitParagraphs(i);
        }
    }),
    (n.splitParagraphsForSection = function (e) {
      for (var t = e.nodes, r = [], i = 0, o = t; i < o.length; i++) {
        var a = o[i];
        a.kind === g.Paragraph ? n._splitParagraph(a, r) : r.push(a);
      }
      (e.clearNodes(), e.appendNodes(r));
    }),
    (n._splitParagraph = function (e, t) {
      var r = e.nodes,
        i = new at({ configuration: e.configuration });
      t.push(i);
      var o;
      (function (b) {
        ((b[(b.Start = 0)] = 'Start'),
          (b[(b.AwaitingTrailer = 1)] = 'AwaitingTrailer'),
          (b[(b.ReadingTrailer = 2)] = 'ReadingTrailer'));
      })(o || (o = {}));
      for (var a = o.Start, l = 0; l < r.length; ) {
        var c = !0,
          d = l;
        do {
          var m = r[d++];
          if (m.kind === g.SoftBreak) break;
          c && (this._isWhitespace(m) || (c = !1));
        } while (d < r.length);
        switch (a) {
          case o.Start:
            c || (a = o.AwaitingTrailer);
            break;
          case o.AwaitingTrailer:
            c && (a = o.ReadingTrailer);
            break;
          case o.ReadingTrailer:
            c ||
              ((i = new at({ configuration: e.configuration })),
              t.push(i),
              (a = o.AwaitingTrailer));
            break;
        }
        for (var y = l; y < d; ++y) i.appendNode(r[y]);
        l = d;
      }
    }),
    (n._isWhitespace = function (e) {
      switch (e.kind) {
        case g.PlainText:
          var t = e;
          return n._whitespaceRegExp.test(t.text);
        default:
          return !1;
      }
    }),
    (n._whitespaceRegExp = /^\s*$/),
    n
  );
})();
var qr = (function () {
  function n(e) {
    e ? (this.configuration = e) : (this.configuration = new Dr());
  }
  return (
    (n.prototype.parseString = function (e) {
      return this.parseRange(We.fromString(e));
    }),
    (n.prototype.parseRange = function (e) {
      var t = new Fr(this.configuration, e);
      if (Xi.extract(t)) {
        t.tokens = gn.readTokens(t.lines);
        var r = new Zi(t);
        (r.parse(), eo.splitParagraphs(t.docComment));
      }
      return t;
    }),
    n
  );
})();
var to = X(require('obsidian')),
  Lr = class extends to.TFile {
    constructor(e) {
      super(e.vault, e.path);
      Object.assign(this, e);
    }
  },
  Hr = class {
    constructor(e, t) {
      ((this.name = e), (this.description = t));
    }
  };
var Ve = X(require('obsidian'));
function ar(n) {
  return new Promise(e => setTimeout(e, n));
}
function no(n) {
  return n.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
function ro() {
  return /(<%(?:-|_)?\s*[*~]{0,1})\+((?:.|\s)*?%>)/g;
}
function Za(n, e) {
  e = (0, Ve.normalizePath)(e);
  let t = n.vault.getAbstractFileByPath(e);
  if (!t) throw new O(`Folder "${e}" doesn't exist`);
  if (!(t instanceof Ve.TFolder)) throw new O(`${e} is a file, not a folder`);
  return t;
}
function Dt(n, e) {
  e = (0, Ve.normalizePath)(e);
  let t = n.vault.getAbstractFileByPath(e);
  if (!t) throw new O(`File "${e}" doesn't exist`);
  if (!(t instanceof Ve.TFile)) throw new O(`${e} is a folder, not a file`);
  return t;
}
function ze(n, e) {
  let t = Za(n, e),
    r = [];
  return (
    Ve.Vault.recurseChildren(t, i => {
      i instanceof Ve.TFile && r.push(i);
    }),
    r.sort((i, o) => i.path.localeCompare(o.path)),
    r
  );
}
async function io(n, e) {
  return await Promise.all(
    e.map(async r => {
      let i = await n.vault.cachedRead(r);
      return es(r, i);
    })
  );
}
function es(n, e) {
  let r = new qr().parseString(e),
    i = new Lr(n);
  return (
    (i.description = ts(r.docComment.summarySection)),
    (i.returns = ns(r.docComment.returnsBlock)),
    (i.arguments = rs(r.docComment.params)),
    i
  );
}
function ts(n) {
  try {
    return n.nodes.map(t =>
      t
        .getChildNodes()
        .filter(r => r instanceof Ge)
        .map(r => r.text).join(`
`)
    ).join(`
`);
  } catch (e) {
    throw (console.error('Failed to parse sumamry section'), e);
  }
}
function ns(n) {
  if (!n) return '';
  try {
    return n.content.nodes[0].getChildNodes()[0].text.trim();
  } catch {
    return '';
  }
}
function rs(n) {
  try {
    return n.blocks.map(r => {
      let i = r.parameterName,
        o = r.content
          .getChildNodes()[0]
          .getChildNodes()
          .filter(a => a instanceof Ge)
          .map(a => a.text)
          .join(' ');
      return new Hr(i, o);
    });
  } catch {
    return [];
  }
}
function Pt(n, e, t) {
  if (t < 0 || t === n.length) return;
  let r = n[e];
  ((n[e] = n[t]), (n[t] = r));
}
function Jt(n) {
  return n.workspace.activeEditor?.file ?? n.workspace.getActiveFile();
}
function oo(n) {
  let e = n.lastIndexOf('/');
  return e !== -1 ? n.slice(0, e) : '';
}
function $r(n) {
  return n !== null && typeof n == 'object';
}
function ao(n) {
  let e = n.toString(),
    t = e.indexOf('(');
  return e
    .substring(t + 1, e.indexOf(')'))
    .replace(/ /g, '')
    .split(',');
}
function Kr(n, e, t) {
  let r = n instanceof HTMLOListElement ? 'li' : 'p',
    i = n.createEl(r),
    o = n.createEl('b', { text: e });
  return (
    i.appendChild(o),
    i.appendChild(document.createTextNode(`: ${t}`)),
    i
  );
}
var Po = X(require('obsidian'));
var Co = X(require('obsidian'));
var ie = 'top',
  ue = 'bottom',
  ce = 'right',
  ae = 'left',
  sr = 'auto',
  yt = [ie, ue, ce, ae],
  lt = 'start',
  Nt = 'end',
  so = 'clippingParents',
  cr = 'viewport',
  Qt = 'popper',
  co = 'reference',
  Rr = yt.reduce(function (n, e) {
    return n.concat([e + '-' + lt, e + '-' + Nt]);
  }, []),
  lr = [].concat(yt, [sr]).reduce(function (n, e) {
    return n.concat([e, e + '-' + lt, e + '-' + Nt]);
  }, []),
  is = 'beforeRead',
  os = 'read',
  as = 'afterRead',
  ss = 'beforeMain',
  cs = 'main',
  ls = 'afterMain',
  ps = 'beforeWrite',
  us = 'write',
  fs = 'afterWrite',
  lo = [is, os, as, ss, cs, ls, ps, us, fs];
function de(n) {
  return n ? (n.nodeName || '').toLowerCase() : null;
}
function Z(n) {
  if (n == null) return window;
  if (n.toString() !== '[object Window]') {
    var e = n.ownerDocument;
    return (e && e.defaultView) || window;
  }
  return n;
}
function De(n) {
  var e = Z(n).Element;
  return n instanceof e || n instanceof Element;
}
function fe(n) {
  var e = Z(n).HTMLElement;
  return n instanceof e || n instanceof HTMLElement;
}
function Xt(n) {
  if (typeof ShadowRoot == 'undefined') return !1;
  var e = Z(n).ShadowRoot;
  return n instanceof e || n instanceof ShadowRoot;
}
function ds(n) {
  var e = n.state;
  Object.keys(e.elements).forEach(function (t) {
    var r = e.styles[t] || {},
      i = e.attributes[t] || {},
      o = e.elements[t];
    !fe(o) ||
      !de(o) ||
      (Object.assign(o.style, r),
      Object.keys(i).forEach(function (a) {
        var l = i[a];
        l === !1 ? o.removeAttribute(a) : o.setAttribute(a, l === !0 ? '' : l);
      }));
  });
}
function ms(n) {
  var e = n.state,
    t = {
      popper: {
        position: e.options.strategy,
        left: '0',
        top: '0',
        margin: '0',
      },
      arrow: { position: 'absolute' },
      reference: {},
    };
  return (
    Object.assign(e.elements.popper.style, t.popper),
    (e.styles = t),
    e.elements.arrow && Object.assign(e.elements.arrow.style, t.arrow),
    function () {
      Object.keys(e.elements).forEach(function (r) {
        var i = e.elements[r],
          o = e.attributes[r] || {},
          a = Object.keys(e.styles.hasOwnProperty(r) ? e.styles[r] : t[r]),
          l = a.reduce(function (c, d) {
            return ((c[d] = ''), c);
          }, {});
        !fe(i) ||
          !de(i) ||
          (Object.assign(i.style, l),
          Object.keys(o).forEach(function (c) {
            i.removeAttribute(c);
          }));
      });
    }
  );
}
var po = {
  name: 'applyStyles',
  enabled: !0,
  phase: 'write',
  fn: ds,
  effect: ms,
  requires: ['computeStyles'],
};
function me(n) {
  return n.split('-')[0];
}
var Fe = Math.max,
  Ot = Math.min,
  pt = Math.round;
function Zt() {
  var n = navigator.userAgentData;
  return n != null && n.brands && Array.isArray(n.brands)
    ? n.brands
        .map(function (e) {
          return e.brand + '/' + e.version;
        })
        .join(' ')
    : navigator.userAgent;
}
function An() {
  return !/^((?!chrome|android).)*safari/i.test(Zt());
}
function Pe(n, e, t) {
  (e === void 0 && (e = !1), t === void 0 && (t = !1));
  var r = n.getBoundingClientRect(),
    i = 1,
    o = 1;
  e &&
    fe(n) &&
    ((i = (n.offsetWidth > 0 && pt(r.width) / n.offsetWidth) || 1),
    (o = (n.offsetHeight > 0 && pt(r.height) / n.offsetHeight) || 1));
  var a = De(n) ? Z(n) : window,
    l = a.visualViewport,
    c = !An() && t,
    d = (r.left + (c && l ? l.offsetLeft : 0)) / i,
    m = (r.top + (c && l ? l.offsetTop : 0)) / o,
    y = r.width / i,
    b = r.height / o;
  return {
    width: y,
    height: b,
    top: m,
    right: d + y,
    bottom: m + b,
    left: d,
    x: d,
    y: m,
  };
}
function Mt(n) {
  var e = Pe(n),
    t = n.offsetWidth,
    r = n.offsetHeight;
  return (
    Math.abs(e.width - t) <= 1 && (t = e.width),
    Math.abs(e.height - r) <= 1 && (r = e.height),
    { x: n.offsetLeft, y: n.offsetTop, width: t, height: r }
  );
}
function _n(n, e) {
  var t = e.getRootNode && e.getRootNode();
  if (n.contains(e)) return !0;
  if (t && Xt(t)) {
    var r = e;
    do {
      if (r && n.isSameNode(r)) return !0;
      r = r.parentNode || r.host;
    } while (r);
  }
  return !1;
}
function ve(n) {
  return Z(n).getComputedStyle(n);
}
function Yr(n) {
  return ['table', 'td', 'th'].indexOf(de(n)) >= 0;
}
function he(n) {
  return ((De(n) ? n.ownerDocument : n.document) || window.document)
    .documentElement;
}
function ut(n) {
  return de(n) === 'html'
    ? n
    : n.assignedSlot || n.parentNode || (Xt(n) ? n.host : null) || he(n);
}
function uo(n) {
  return !fe(n) || ve(n).position === 'fixed' ? null : n.offsetParent;
}
function gs(n) {
  var e = /firefox/i.test(Zt()),
    t = /Trident/i.test(Zt());
  if (t && fe(n)) {
    var r = ve(n);
    if (r.position === 'fixed') return null;
  }
  var i = ut(n);
  for (Xt(i) && (i = i.host); fe(i) && ['html', 'body'].indexOf(de(i)) < 0; ) {
    var o = ve(i);
    if (
      o.transform !== 'none' ||
      o.perspective !== 'none' ||
      o.contain === 'paint' ||
      ['transform', 'perspective'].indexOf(o.willChange) !== -1 ||
      (e && o.willChange === 'filter') ||
      (e && o.filter && o.filter !== 'none')
    )
      return i;
    i = i.parentNode;
  }
  return null;
}
function Ie(n) {
  for (var e = Z(n), t = uo(n); t && Yr(t) && ve(t).position === 'static'; )
    t = uo(t);
  return t &&
    (de(t) === 'html' || (de(t) === 'body' && ve(t).position === 'static'))
    ? e
    : t || gs(n) || e;
}
function Bt(n) {
  return ['top', 'bottom'].indexOf(n) >= 0 ? 'x' : 'y';
}
function Ft(n, e, t) {
  return Fe(n, Ot(e, t));
}
function fo(n, e, t) {
  var r = Ft(n, e, t);
  return r > t ? t : r;
}
function xn() {
  return { top: 0, right: 0, bottom: 0, left: 0 };
}
function yn(n) {
  return Object.assign({}, xn(), n);
}
function jn(n, e) {
  return e.reduce(function (t, r) {
    return ((t[r] = n), t);
  }, {});
}
var hs = function (e, t) {
  return (
    (e =
      typeof e == 'function'
        ? e(Object.assign({}, t.rects, { placement: t.placement }))
        : e),
    yn(typeof e != 'number' ? e : jn(e, yt))
  );
};
function As(n) {
  var e,
    t = n.state,
    r = n.name,
    i = n.options,
    o = t.elements.arrow,
    a = t.modifiersData.popperOffsets,
    l = me(t.placement),
    c = Bt(l),
    d = [ae, ce].indexOf(l) >= 0,
    m = d ? 'height' : 'width';
  if (!(!o || !a)) {
    var y = hs(i.padding, t),
      b = Mt(o),
      E = c === 'y' ? ie : ae,
      P = c === 'y' ? ue : ce,
      k =
        t.rects.reference[m] + t.rects.reference[c] - a[c] - t.rects.popper[m],
      w = a[c] - t.rects.reference[c],
      M = Ie(o),
      $ = M ? (c === 'y' ? M.clientHeight || 0 : M.clientWidth || 0) : 0,
      K = k / 2 - w / 2,
      C = y[E],
      H = $ - b[m] - y[P],
      I = $ / 2 - b[m] / 2 + K,
      J = Ft(C, I, H),
      te = c;
    t.modifiersData[r] = ((e = {}), (e[te] = J), (e.centerOffset = J - I), e);
  }
}
function _s(n) {
  var e = n.state,
    t = n.options,
    r = t.element,
    i = r === void 0 ? '[data-popper-arrow]' : r;
  i != null &&
    ((typeof i == 'string' && ((i = e.elements.popper.querySelector(i)), !i)) ||
      !_n(e.elements.popper, i) ||
      (e.elements.arrow = i));
}
var mo = {
  name: 'arrow',
  enabled: !0,
  phase: 'main',
  fn: As,
  effect: _s,
  requires: ['popperOffsets'],
  requiresIfExists: ['preventOverflow'],
};
function Ne(n) {
  return n.split('-')[1];
}
var xs = { top: 'auto', right: 'auto', bottom: 'auto', left: 'auto' };
function ys(n, e) {
  var t = n.x,
    r = n.y,
    i = e.devicePixelRatio || 1;
  return { x: pt(t * i) / i || 0, y: pt(r * i) / i || 0 };
}
function go(n) {
  var e,
    t = n.popper,
    r = n.popperRect,
    i = n.placement,
    o = n.variation,
    a = n.offsets,
    l = n.position,
    c = n.gpuAcceleration,
    d = n.adaptive,
    m = n.roundOffsets,
    y = n.isFixed,
    b = a.x,
    E = b === void 0 ? 0 : b,
    P = a.y,
    k = P === void 0 ? 0 : P,
    w = typeof m == 'function' ? m({ x: E, y: k }) : { x: E, y: k };
  ((E = w.x), (k = w.y));
  var M = a.hasOwnProperty('x'),
    $ = a.hasOwnProperty('y'),
    K = ae,
    C = ie,
    H = window;
  if (d) {
    var I = Ie(t),
      J = 'clientHeight',
      te = 'clientWidth';
    if (
      (I === Z(t) &&
        ((I = he(t)),
        ve(I).position !== 'static' &&
          l === 'absolute' &&
          ((J = 'scrollHeight'), (te = 'scrollWidth'))),
      (I = I),
      i === ie || ((i === ae || i === ce) && o === Nt))
    ) {
      C = ue;
      var ne =
        y && I === H && H.visualViewport ? H.visualViewport.height : I[J];
      ((k -= ne - r.height), (k *= c ? 1 : -1));
    }
    if (i === ae || ((i === ie || i === ue) && o === Nt)) {
      K = ce;
      var Q = y && I === H && H.visualViewport ? H.visualViewport.width : I[te];
      ((E -= Q - r.width), (E *= c ? 1 : -1));
    }
  }
  var h = Object.assign({ position: l }, d && xs),
    S = m === !0 ? ys({ x: E, y: k }, Z(t)) : { x: E, y: k };
  if (((E = S.x), (k = S.y), c)) {
    var f;
    return Object.assign(
      {},
      h,
      ((f = {}),
      (f[C] = $ ? '0' : ''),
      (f[K] = M ? '0' : ''),
      (f.transform =
        (H.devicePixelRatio || 1) <= 1
          ? 'translate(' + E + 'px, ' + k + 'px)'
          : 'translate3d(' + E + 'px, ' + k + 'px, 0)'),
      f)
    );
  }
  return Object.assign(
    {},
    h,
    ((e = {}),
    (e[C] = $ ? k + 'px' : ''),
    (e[K] = M ? E + 'px' : ''),
    (e.transform = ''),
    e)
  );
}
function js(n) {
  var e = n.state,
    t = n.options,
    r = t.gpuAcceleration,
    i = r === void 0 ? !0 : r,
    o = t.adaptive,
    a = o === void 0 ? !0 : o,
    l = t.roundOffsets,
    c = l === void 0 ? !0 : l,
    d = {
      placement: me(e.placement),
      variation: Ne(e.placement),
      popper: e.elements.popper,
      popperRect: e.rects.popper,
      gpuAcceleration: i,
      isFixed: e.options.strategy === 'fixed',
    };
  (e.modifiersData.popperOffsets != null &&
    (e.styles.popper = Object.assign(
      {},
      e.styles.popper,
      go(
        Object.assign({}, d, {
          offsets: e.modifiersData.popperOffsets,
          position: e.options.strategy,
          adaptive: a,
          roundOffsets: c,
        })
      )
    )),
    e.modifiersData.arrow != null &&
      (e.styles.arrow = Object.assign(
        {},
        e.styles.arrow,
        go(
          Object.assign({}, d, {
            offsets: e.modifiersData.arrow,
            position: 'absolute',
            adaptive: !1,
            roundOffsets: c,
          })
        )
      )),
    (e.attributes.popper = Object.assign({}, e.attributes.popper, {
      'data-popper-placement': e.placement,
    })));
}
var ho = {
  name: 'computeStyles',
  enabled: !0,
  phase: 'beforeWrite',
  fn: js,
  data: {},
};
var pr = { passive: !0 };
function vs(n) {
  var e = n.state,
    t = n.instance,
    r = n.options,
    i = r.scroll,
    o = i === void 0 ? !0 : i,
    a = r.resize,
    l = a === void 0 ? !0 : a,
    c = Z(e.elements.popper),
    d = [].concat(e.scrollParents.reference, e.scrollParents.popper);
  return (
    o &&
      d.forEach(function (m) {
        m.addEventListener('scroll', t.update, pr);
      }),
    l && c.addEventListener('resize', t.update, pr),
    function () {
      (o &&
        d.forEach(function (m) {
          m.removeEventListener('scroll', t.update, pr);
        }),
        l && c.removeEventListener('resize', t.update, pr));
    }
  );
}
var Ao = {
  name: 'eventListeners',
  enabled: !0,
  phase: 'write',
  fn: function () {},
  effect: vs,
  data: {},
};
var ws = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };
function en(n) {
  return n.replace(/left|right|bottom|top/g, function (e) {
    return ws[e];
  });
}
var bs = { start: 'end', end: 'start' };
function ur(n) {
  return n.replace(/start|end/g, function (e) {
    return bs[e];
  });
}
function It(n) {
  var e = Z(n),
    t = e.pageXOffset,
    r = e.pageYOffset;
  return { scrollLeft: t, scrollTop: r };
}
function qt(n) {
  return Pe(he(n)).left + It(n).scrollLeft;
}
function Ur(n, e) {
  var t = Z(n),
    r = he(n),
    i = t.visualViewport,
    o = r.clientWidth,
    a = r.clientHeight,
    l = 0,
    c = 0;
  if (i) {
    ((o = i.width), (a = i.height));
    var d = An();
    (d || (!d && e === 'fixed')) && ((l = i.offsetLeft), (c = i.offsetTop));
  }
  return { width: o, height: a, x: l + qt(n), y: c };
}
function Gr(n) {
  var e,
    t = he(n),
    r = It(n),
    i = (e = n.ownerDocument) == null ? void 0 : e.body,
    o = Fe(
      t.scrollWidth,
      t.clientWidth,
      i ? i.scrollWidth : 0,
      i ? i.clientWidth : 0
    ),
    a = Fe(
      t.scrollHeight,
      t.clientHeight,
      i ? i.scrollHeight : 0,
      i ? i.clientHeight : 0
    ),
    l = -r.scrollLeft + qt(n),
    c = -r.scrollTop;
  return (
    ve(i || t).direction === 'rtl' &&
      (l += Fe(t.clientWidth, i ? i.clientWidth : 0) - o),
    { width: o, height: a, x: l, y: c }
  );
}
function Lt(n) {
  var e = ve(n),
    t = e.overflow,
    r = e.overflowX,
    i = e.overflowY;
  return /auto|scroll|overlay|hidden/.test(t + i + r);
}
function fr(n) {
  return ['html', 'body', '#document'].indexOf(de(n)) >= 0
    ? n.ownerDocument.body
    : fe(n) && Lt(n)
      ? n
      : fr(ut(n));
}
function jt(n, e) {
  var t;
  e === void 0 && (e = []);
  var r = fr(n),
    i = r === ((t = n.ownerDocument) == null ? void 0 : t.body),
    o = Z(r),
    a = i ? [o].concat(o.visualViewport || [], Lt(r) ? r : []) : r,
    l = e.concat(a);
  return i ? l : l.concat(jt(ut(a)));
}
function tn(n) {
  return Object.assign({}, n, {
    left: n.x,
    top: n.y,
    right: n.x + n.width,
    bottom: n.y + n.height,
  });
}
function Es(n, e) {
  var t = Pe(n, !1, e === 'fixed');
  return (
    (t.top = t.top + n.clientTop),
    (t.left = t.left + n.clientLeft),
    (t.bottom = t.top + n.clientHeight),
    (t.right = t.left + n.clientWidth),
    (t.width = n.clientWidth),
    (t.height = n.clientHeight),
    (t.x = t.left),
    (t.y = t.top),
    t
  );
}
function _o(n, e, t) {
  return e === cr ? tn(Ur(n, t)) : De(e) ? Es(e, t) : tn(Gr(he(n)));
}
function Ts(n) {
  var e = jt(ut(n)),
    t = ['absolute', 'fixed'].indexOf(ve(n).position) >= 0,
    r = t && fe(n) ? Ie(n) : n;
  return De(r)
    ? e.filter(function (i) {
        return De(i) && _n(i, r) && de(i) !== 'body';
      })
    : [];
}
function Wr(n, e, t, r) {
  var i = e === 'clippingParents' ? Ts(n) : [].concat(e),
    o = [].concat(i, [t]),
    a = o[0],
    l = o.reduce(
      function (c, d) {
        var m = _o(n, d, r);
        return (
          (c.top = Fe(m.top, c.top)),
          (c.right = Ot(m.right, c.right)),
          (c.bottom = Ot(m.bottom, c.bottom)),
          (c.left = Fe(m.left, c.left)),
          c
        );
      },
      _o(n, a, r)
    );
  return (
    (l.width = l.right - l.left),
    (l.height = l.bottom - l.top),
    (l.x = l.left),
    (l.y = l.top),
    l
  );
}
function vn(n) {
  var e = n.reference,
    t = n.element,
    r = n.placement,
    i = r ? me(r) : null,
    o = r ? Ne(r) : null,
    a = e.x + e.width / 2 - t.width / 2,
    l = e.y + e.height / 2 - t.height / 2,
    c;
  switch (i) {
    case ie:
      c = { x: a, y: e.y - t.height };
      break;
    case ue:
      c = { x: a, y: e.y + e.height };
      break;
    case ce:
      c = { x: e.x + e.width, y: l };
      break;
    case ae:
      c = { x: e.x - t.width, y: l };
      break;
    default:
      c = { x: e.x, y: e.y };
  }
  var d = i ? Bt(i) : null;
  if (d != null) {
    var m = d === 'y' ? 'height' : 'width';
    switch (o) {
      case lt:
        c[d] = c[d] - (e[m] / 2 - t[m] / 2);
        break;
      case Nt:
        c[d] = c[d] + (e[m] / 2 - t[m] / 2);
        break;
      default:
    }
  }
  return c;
}
function qe(n, e) {
  e === void 0 && (e = {});
  var t = e,
    r = t.placement,
    i = r === void 0 ? n.placement : r,
    o = t.strategy,
    a = o === void 0 ? n.strategy : o,
    l = t.boundary,
    c = l === void 0 ? so : l,
    d = t.rootBoundary,
    m = d === void 0 ? cr : d,
    y = t.elementContext,
    b = y === void 0 ? Qt : y,
    E = t.altBoundary,
    P = E === void 0 ? !1 : E,
    k = t.padding,
    w = k === void 0 ? 0 : k,
    M = yn(typeof w != 'number' ? w : jn(w, yt)),
    $ = b === Qt ? co : Qt,
    K = n.rects.popper,
    C = n.elements[P ? $ : b],
    H = Wr(De(C) ? C : C.contextElement || he(n.elements.popper), c, m, a),
    I = Pe(n.elements.reference),
    J = vn({ reference: I, element: K, strategy: 'absolute', placement: i }),
    te = tn(Object.assign({}, K, J)),
    ne = b === Qt ? te : I,
    Q = {
      top: H.top - ne.top + M.top,
      bottom: ne.bottom - H.bottom + M.bottom,
      left: H.left - ne.left + M.left,
      right: ne.right - H.right + M.right,
    },
    h = n.modifiersData.offset;
  if (b === Qt && h) {
    var S = h[i];
    Object.keys(Q).forEach(function (f) {
      var Me = [ce, ue].indexOf(f) >= 0 ? 1 : -1,
        be = [ie, ue].indexOf(f) >= 0 ? 'y' : 'x';
      Q[f] += S[be] * Me;
    });
  }
  return Q;
}
function Vr(n, e) {
  e === void 0 && (e = {});
  var t = e,
    r = t.placement,
    i = t.boundary,
    o = t.rootBoundary,
    a = t.padding,
    l = t.flipVariations,
    c = t.allowedAutoPlacements,
    d = c === void 0 ? lr : c,
    m = Ne(r),
    y = m
      ? l
        ? Rr
        : Rr.filter(function (P) {
            return Ne(P) === m;
          })
      : yt,
    b = y.filter(function (P) {
      return d.indexOf(P) >= 0;
    });
  b.length === 0 && (b = y);
  var E = b.reduce(function (P, k) {
    return (
      (P[k] = qe(n, { placement: k, boundary: i, rootBoundary: o, padding: a })[
        me(k)
      ]),
      P
    );
  }, {});
  return Object.keys(E).sort(function (P, k) {
    return E[P] - E[k];
  });
}
function ks(n) {
  if (me(n) === sr) return [];
  var e = en(n);
  return [ur(n), e, ur(e)];
}
function Ss(n) {
  var e = n.state,
    t = n.options,
    r = n.name;
  if (!e.modifiersData[r]._skip) {
    for (
      var i = t.mainAxis,
        o = i === void 0 ? !0 : i,
        a = t.altAxis,
        l = a === void 0 ? !0 : a,
        c = t.fallbackPlacements,
        d = t.padding,
        m = t.boundary,
        y = t.rootBoundary,
        b = t.altBoundary,
        E = t.flipVariations,
        P = E === void 0 ? !0 : E,
        k = t.allowedAutoPlacements,
        w = e.options.placement,
        M = me(w),
        $ = M === w,
        K = c || ($ || !P ? [en(w)] : ks(w)),
        C = [w].concat(K).reduce(function (B, D) {
          return B.concat(
            me(D) === sr
              ? Vr(e, {
                  placement: D,
                  boundary: m,
                  rootBoundary: y,
                  padding: d,
                  flipVariations: P,
                  allowedAutoPlacements: k,
                })
              : D
          );
        }, []),
        H = e.rects.reference,
        I = e.rects.popper,
        J = new Map(),
        te = !0,
        ne = C[0],
        Q = 0;
      Q < C.length;
      Q++
    ) {
      var h = C[Q],
        S = me(h),
        f = Ne(h) === lt,
        Me = [ie, ue].indexOf(S) >= 0,
        be = Me ? 'width' : 'height',
        Ae = qe(e, {
          placement: h,
          boundary: m,
          rootBoundary: y,
          altBoundary: b,
          padding: d,
        }),
        _e = Me ? (f ? ce : ae) : f ? ue : ie;
      H[be] > I[be] && (_e = en(_e));
      var Ke = en(_e),
        Ee = [];
      if (
        (o && Ee.push(Ae[S] <= 0),
        l && Ee.push(Ae[_e] <= 0, Ae[Ke] <= 0),
        Ee.every(function (B) {
          return B;
        }))
      ) {
        ((ne = h), (te = !1));
        break;
      }
      J.set(h, Ee);
    }
    if (te)
      for (
        var $t = P ? 3 : 1,
          Re = function (D) {
            var q = C.find(function (ee) {
              var et = J.get(ee);
              if (et)
                return et.slice(0, D).every(function (W) {
                  return W;
                });
            });
            if (q) return ((ne = q), 'break');
          },
          Ze = $t;
        Ze > 0;
        Ze--
      ) {
        var xe = Re(Ze);
        if (xe === 'break') break;
      }
    e.placement !== ne &&
      ((e.modifiersData[r]._skip = !0), (e.placement = ne), (e.reset = !0));
  }
}
var xo = {
  name: 'flip',
  enabled: !0,
  phase: 'main',
  fn: Ss,
  requiresIfExists: ['offset'],
  data: { _skip: !1 },
};
function yo(n, e, t) {
  return (
    t === void 0 && (t = { x: 0, y: 0 }),
    {
      top: n.top - e.height - t.y,
      right: n.right - e.width + t.x,
      bottom: n.bottom - e.height + t.y,
      left: n.left - e.width - t.x,
    }
  );
}
function jo(n) {
  return [ie, ce, ue, ae].some(function (e) {
    return n[e] >= 0;
  });
}
function Cs(n) {
  var e = n.state,
    t = n.name,
    r = e.rects.reference,
    i = e.rects.popper,
    o = e.modifiersData.preventOverflow,
    a = qe(e, { elementContext: 'reference' }),
    l = qe(e, { altBoundary: !0 }),
    c = yo(a, r),
    d = yo(l, i, o),
    m = jo(c),
    y = jo(d);
  ((e.modifiersData[t] = {
    referenceClippingOffsets: c,
    popperEscapeOffsets: d,
    isReferenceHidden: m,
    hasPopperEscaped: y,
  }),
    (e.attributes.popper = Object.assign({}, e.attributes.popper, {
      'data-popper-reference-hidden': m,
      'data-popper-escaped': y,
    })));
}
var vo = {
  name: 'hide',
  enabled: !0,
  phase: 'main',
  requiresIfExists: ['preventOverflow'],
  fn: Cs,
};
function Ds(n, e, t) {
  var r = me(n),
    i = [ae, ie].indexOf(r) >= 0 ? -1 : 1,
    o = typeof t == 'function' ? t(Object.assign({}, e, { placement: n })) : t,
    a = o[0],
    l = o[1];
  return (
    (a = a || 0),
    (l = (l || 0) * i),
    [ae, ce].indexOf(r) >= 0 ? { x: l, y: a } : { x: a, y: l }
  );
}
function Ps(n) {
  var e = n.state,
    t = n.options,
    r = n.name,
    i = t.offset,
    o = i === void 0 ? [0, 0] : i,
    a = lr.reduce(function (m, y) {
      return ((m[y] = Ds(y, e.rects, o)), m);
    }, {}),
    l = a[e.placement],
    c = l.x,
    d = l.y;
  (e.modifiersData.popperOffsets != null &&
    ((e.modifiersData.popperOffsets.x += c),
    (e.modifiersData.popperOffsets.y += d)),
    (e.modifiersData[r] = a));
}
var wo = {
  name: 'offset',
  enabled: !0,
  phase: 'main',
  requires: ['popperOffsets'],
  fn: Ps,
};
function Ns(n) {
  var e = n.state,
    t = n.name;
  e.modifiersData[t] = vn({
    reference: e.rects.reference,
    element: e.rects.popper,
    strategy: 'absolute',
    placement: e.placement,
  });
}
var bo = {
  name: 'popperOffsets',
  enabled: !0,
  phase: 'read',
  fn: Ns,
  data: {},
};
function zr(n) {
  return n === 'x' ? 'y' : 'x';
}
function Os(n) {
  var e = n.state,
    t = n.options,
    r = n.name,
    i = t.mainAxis,
    o = i === void 0 ? !0 : i,
    a = t.altAxis,
    l = a === void 0 ? !1 : a,
    c = t.boundary,
    d = t.rootBoundary,
    m = t.altBoundary,
    y = t.padding,
    b = t.tether,
    E = b === void 0 ? !0 : b,
    P = t.tetherOffset,
    k = P === void 0 ? 0 : P,
    w = qe(e, { boundary: c, rootBoundary: d, padding: y, altBoundary: m }),
    M = me(e.placement),
    $ = Ne(e.placement),
    K = !$,
    C = Bt(M),
    H = zr(C),
    I = e.modifiersData.popperOffsets,
    J = e.rects.reference,
    te = e.rects.popper,
    ne =
      typeof k == 'function'
        ? k(Object.assign({}, e.rects, { placement: e.placement }))
        : k,
    Q =
      typeof ne == 'number'
        ? { mainAxis: ne, altAxis: ne }
        : Object.assign({ mainAxis: 0, altAxis: 0 }, ne),
    h = e.modifiersData.offset ? e.modifiersData.offset[e.placement] : null,
    S = { x: 0, y: 0 };
  if (!!I) {
    if (o) {
      var f,
        Me = C === 'y' ? ie : ae,
        be = C === 'y' ? ue : ce,
        Ae = C === 'y' ? 'height' : 'width',
        _e = I[C],
        Ke = _e + w[Me],
        Ee = _e - w[be],
        $t = E ? -te[Ae] / 2 : 0,
        Re = $ === lt ? J[Ae] : te[Ae],
        Ze = $ === lt ? -te[Ae] : -J[Ae],
        xe = e.elements.arrow,
        B = E && xe ? Mt(xe) : { width: 0, height: 0 },
        D = e.modifiersData['arrow#persistent']
          ? e.modifiersData['arrow#persistent'].padding
          : xn(),
        q = D[Me],
        ee = D[be],
        et = Ft(0, J[Ae], B[Ae]),
        W = K ? J[Ae] / 2 - $t - et - q - Q.mainAxis : Re - et - q - Q.mainAxis,
        ye = K
          ? -J[Ae] / 2 + $t + et + ee + Q.mainAxis
          : Ze + et + ee + Q.mainAxis,
        bt = e.elements.arrow && Ie(e.elements.arrow),
        Sn = bt ? (C === 'y' ? bt.clientTop || 0 : bt.clientLeft || 0) : 0,
        tt = (f = h == null ? void 0 : h[C]) != null ? f : 0,
        nt = _e + W - tt - Sn,
        gt = _e + ye - tt,
        Et = Ft(E ? Ot(Ke, nt) : Ke, _e, E ? Fe(Ee, gt) : Ee);
      ((I[C] = Et), (S[C] = Et - _e));
    }
    if (l) {
      var Cn,
        Dn = C === 'x' ? ie : ae,
        Pn = C === 'x' ? ue : ce,
        rt = I[H],
        Kt = H === 'y' ? 'height' : 'width',
        Nn = rt + w[Dn],
        On = rt - w[Pn],
        an = [ie, ae].indexOf(M) !== -1,
        Tt = (Cn = h == null ? void 0 : h[H]) != null ? Cn : 0,
        Mn = an ? Nn : rt - J[Kt] - te[Kt] - Tt + Q.altAxis,
        Ye = an ? rt + J[Kt] + te[Kt] - Tt - Q.altAxis : On,
        se = E && an ? fo(Mn, rt, Ye) : Ft(E ? Mn : Nn, rt, E ? Ye : On);
      ((I[H] = se), (S[H] = se - rt));
    }
    e.modifiersData[r] = S;
  }
}
var Eo = {
  name: 'preventOverflow',
  enabled: !0,
  phase: 'main',
  fn: Os,
  requiresIfExists: ['offset'],
};
function Jr(n) {
  return { scrollLeft: n.scrollLeft, scrollTop: n.scrollTop };
}
function Qr(n) {
  return n === Z(n) || !fe(n) ? It(n) : Jr(n);
}
function Ms(n) {
  var e = n.getBoundingClientRect(),
    t = pt(e.width) / n.offsetWidth || 1,
    r = pt(e.height) / n.offsetHeight || 1;
  return t !== 1 || r !== 1;
}
function Xr(n, e, t) {
  t === void 0 && (t = !1);
  var r = fe(e),
    i = fe(e) && Ms(e),
    o = he(e),
    a = Pe(n, i, t),
    l = { scrollLeft: 0, scrollTop: 0 },
    c = { x: 0, y: 0 };
  return (
    (r || (!r && !t)) &&
      ((de(e) !== 'body' || Lt(o)) && (l = Qr(e)),
      fe(e)
        ? ((c = Pe(e, !0)), (c.x += e.clientLeft), (c.y += e.clientTop))
        : o && (c.x = qt(o))),
    {
      x: a.left + l.scrollLeft - c.x,
      y: a.top + l.scrollTop - c.y,
      width: a.width,
      height: a.height,
    }
  );
}
function Bs(n) {
  var e = new Map(),
    t = new Set(),
    r = [];
  n.forEach(function (o) {
    e.set(o.name, o);
  });
  function i(o) {
    t.add(o.name);
    var a = [].concat(o.requires || [], o.requiresIfExists || []);
    (a.forEach(function (l) {
      if (!t.has(l)) {
        var c = e.get(l);
        c && i(c);
      }
    }),
      r.push(o));
  }
  return (
    n.forEach(function (o) {
      t.has(o.name) || i(o);
    }),
    r
  );
}
function Zr(n) {
  var e = Bs(n);
  return lo.reduce(function (t, r) {
    return t.concat(
      e.filter(function (i) {
        return i.phase === r;
      })
    );
  }, []);
}
function ei(n) {
  var e;
  return function () {
    return (
      e ||
        (e = new Promise(function (t) {
          Promise.resolve().then(function () {
            ((e = void 0), t(n()));
          });
        })),
      e
    );
  };
}
function ti(n) {
  var e = n.reduce(function (t, r) {
    var i = t[r.name];
    return (
      (t[r.name] = i
        ? Object.assign({}, i, r, {
            options: Object.assign({}, i.options, r.options),
            data: Object.assign({}, i.data, r.data),
          })
        : r),
      t
    );
  }, {});
  return Object.keys(e).map(function (t) {
    return e[t];
  });
}
var To = { placement: 'bottom', modifiers: [], strategy: 'absolute' };
function ko() {
  for (var n = arguments.length, e = new Array(n), t = 0; t < n; t++)
    e[t] = arguments[t];
  return !e.some(function (r) {
    return !(r && typeof r.getBoundingClientRect == 'function');
  });
}
function So(n) {
  n === void 0 && (n = {});
  var e = n,
    t = e.defaultModifiers,
    r = t === void 0 ? [] : t,
    i = e.defaultOptions,
    o = i === void 0 ? To : i;
  return function (l, c, d) {
    d === void 0 && (d = o);
    var m = {
        placement: 'bottom',
        orderedModifiers: [],
        options: Object.assign({}, To, o),
        modifiersData: {},
        elements: { reference: l, popper: c },
        attributes: {},
        styles: {},
      },
      y = [],
      b = !1,
      E = {
        state: m,
        setOptions: function (M) {
          var $ = typeof M == 'function' ? M(m.options) : M;
          (k(),
            (m.options = Object.assign({}, o, m.options, $)),
            (m.scrollParents = {
              reference: De(l)
                ? jt(l)
                : l.contextElement
                  ? jt(l.contextElement)
                  : [],
              popper: jt(c),
            }));
          var K = Zr(ti([].concat(r, m.options.modifiers)));
          return (
            (m.orderedModifiers = K.filter(function (C) {
              return C.enabled;
            })),
            P(),
            E.update()
          );
        },
        forceUpdate: function () {
          if (!b) {
            var M = m.elements,
              $ = M.reference,
              K = M.popper;
            if (!!ko($, K)) {
              ((m.rects = {
                reference: Xr($, Ie(K), m.options.strategy === 'fixed'),
                popper: Mt(K),
              }),
                (m.reset = !1),
                (m.placement = m.options.placement),
                m.orderedModifiers.forEach(function (Q) {
                  return (m.modifiersData[Q.name] = Object.assign({}, Q.data));
                }));
              for (var C = 0; C < m.orderedModifiers.length; C++) {
                if (m.reset === !0) {
                  ((m.reset = !1), (C = -1));
                  continue;
                }
                var H = m.orderedModifiers[C],
                  I = H.fn,
                  J = H.options,
                  te = J === void 0 ? {} : J,
                  ne = H.name;
                typeof I == 'function' &&
                  (m =
                    I({ state: m, options: te, name: ne, instance: E }) || m);
              }
            }
          }
        },
        update: ei(function () {
          return new Promise(function (w) {
            (E.forceUpdate(), w(m));
          });
        }),
        destroy: function () {
          (k(), (b = !0));
        },
      };
    if (!ko(l, c)) return E;
    E.setOptions(d).then(function (w) {
      !b && d.onFirstUpdate && d.onFirstUpdate(w);
    });
    function P() {
      m.orderedModifiers.forEach(function (w) {
        var M = w.name,
          $ = w.options,
          K = $ === void 0 ? {} : $,
          C = w.effect;
        if (typeof C == 'function') {
          var H = C({ state: m, name: M, instance: E, options: K }),
            I = function () {};
          y.push(H || I);
        }
      });
    }
    function k() {
      (y.forEach(function (w) {
        return w();
      }),
        (y = []));
    }
    return E;
  };
}
var Fs = [Ao, bo, ho, po, wo, xo, Eo, mo, vo],
  ni = So({ defaultModifiers: Fs });
var Is = (n, e) => ((n % e) + e) % e,
  Do = class {
    constructor(e, t, r) {
      ((this.owner = e),
        (this.containerEl = t),
        t.on('click', '.suggestion-item', this.onSuggestionClick.bind(this)),
        t.on(
          'mousemove',
          '.suggestion-item',
          this.onSuggestionMouseover.bind(this)
        ),
        r.register([], 'ArrowUp', i => {
          if (!i.isComposing)
            return (this.setSelectedItem(this.selectedItem - 1, !0), !1);
        }),
        r.register([], 'ArrowDown', i => {
          if (!i.isComposing)
            return (this.setSelectedItem(this.selectedItem + 1, !0), !1);
        }),
        r.register([], 'Enter', i => {
          if (!i.isComposing) return (this.useSelectedItem(i), !1);
        }));
    }
    onSuggestionClick(e, t) {
      e.preventDefault();
      let r = this.suggestions.indexOf(t);
      (this.setSelectedItem(r, !1), this.useSelectedItem(e));
    }
    onSuggestionMouseover(e, t) {
      let r = this.suggestions.indexOf(t);
      this.setSelectedItem(r, !1);
    }
    setSuggestions(e) {
      this.containerEl.empty();
      let t = [];
      (e.forEach(r => {
        let i = this.containerEl.createDiv('suggestion-item');
        (this.owner.renderSuggestion(r, i), t.push(i));
      }),
        (this.values = e),
        (this.suggestions = t),
        this.setSelectedItem(0, !1));
    }
    useSelectedItem(e) {
      let t = this.values[this.selectedItem];
      t && this.owner.selectSuggestion(t, e);
    }
    setSelectedItem(e, t) {
      let r = Is(e, this.suggestions.length),
        i = this.suggestions[this.selectedItem],
        o = this.suggestions[r];
      (i?.removeClass('is-selected'),
        o?.addClass('is-selected'),
        (this.selectedItem = r),
        t && o.scrollIntoView(!1));
    }
  },
  wn = class {
    constructor(e, t) {
      ((this.app = e),
        (this.inputEl = t),
        (this.scope = new Co.Scope()),
        (this.suggestEl = createDiv('suggestion-container')));
      let r = this.suggestEl.createDiv('suggestion');
      ((this.suggest = new Do(this, r, this.scope)),
        this.scope.register([], 'Escape', this.close.bind(this)),
        this.inputEl.addEventListener('input', this.onInputChanged.bind(this)),
        this.inputEl.addEventListener('focus', this.onInputChanged.bind(this)),
        this.inputEl.addEventListener('blur', this.close.bind(this)),
        this.suggestEl.on('mousedown', '.suggestion-container', i => {
          i.preventDefault();
        }));
    }
    onInputChanged() {
      let e = this.inputEl.value,
        t = this.getSuggestions(e);
      if (!t) {
        this.close();
        return;
      }
      t.length > 0
        ? (this.suggest.setSuggestions(t),
          this.open(this.app.dom.appContainerEl, this.inputEl))
        : this.close();
    }
    open(e, t) {
      (this.app.keymap.pushScope(this.scope),
        e.appendChild(this.suggestEl),
        (this.popper = ni(t, this.suggestEl, {
          placement: 'bottom-start',
          modifiers: [
            {
              name: 'sameWidth',
              enabled: !0,
              fn: ({ state: r, instance: i }) => {
                let o = `${r.rects.reference.width}px`;
                r.styles.popper.width !== o &&
                  ((r.styles.popper.width = o), i.update());
              },
              phase: 'beforeWrite',
              requires: ['computeStyles'],
            },
          ],
        })));
    }
    close() {
      (this.app.keymap.popScope(this.scope),
        this.suggest.setSuggestions([]),
        this.popper && this.popper.destroy(),
        this.suggestEl.detach());
    }
  };
var Le;
(function (t) {
  ((t[(t.TemplateFiles = 0)] = 'TemplateFiles'),
    (t[(t.ScriptFiles = 1)] = 'ScriptFiles'));
})(Le || (Le = {}));
var nn = class extends wn {
  constructor(e, t, r) {
    super(t.app, e);
    this.inputEl = e;
    this.plugin = t;
    this.mode = r;
  }
  get_folder(e) {
    switch (e) {
      case 0:
        return this.plugin.settings.templates_folder;
      case 1:
        return this.plugin.settings.user_scripts_folder;
    }
  }
  get_error_msg(e) {
    switch (e) {
      case 0:
        return "Templates folder doesn't exist";
      case 1:
        return "User Scripts folder doesn't exist";
    }
  }
  getSuggestions(e) {
    let t = ke(
      () => ze(this.plugin.app, this.get_folder(this.mode)),
      this.get_error_msg(this.mode)
    );
    if (!t) return [];
    let r = [],
      i = e.toLowerCase();
    return (
      t.forEach(o => {
        o instanceof Po.TFile &&
          o.extension === 'md' &&
          o.path.toLowerCase().contains(i) &&
          r.push(o);
      }),
      r.slice(0, 1e3)
    );
  }
  renderSuggestion(e, t) {
    t.setText(e.path);
  }
  selectSuggestion(e) {
    ((this.inputEl.value = e.path),
      this.inputEl.trigger('input'),
      this.close());
  }
};
var No = X(require('obsidian'));
var bn = class extends wn {
  constructor(e, t) {
    super(e, t);
  }
  getSuggestions(e) {
    let t = this.app.vault.getAllLoadedFiles(),
      r = [],
      i = e.toLowerCase();
    return (
      t.forEach(o => {
        o instanceof No.TFolder &&
          o.path.toLowerCase().contains(i) &&
          r.push(o);
      }),
      r.slice(0, 1e3)
    );
  }
  renderSuggestion(e, t) {
    t.setText(e.path);
  }
  selectSuggestion(e) {
    ((this.inputEl.value = e.path),
      this.inputEl.trigger('input'),
      this.close());
  }
};
var ft;
(function (o) {
  ((o[(o.Off = 0)] = 'Off'),
    (o[(o.RenderDescriptionParameterReturn = 1)] =
      'RenderDescriptionParameterReturn'),
    (o[(o.RenderDescriptionParameterList = 2)] =
      'RenderDescriptionParameterList'),
    (o[(o.RenderDescriptionReturn = 3)] = 'RenderDescriptionReturn'),
    (o[(o.RenderDescriptionOnly = 4)] = 'RenderDescriptionOnly'));
})(ft || (ft = {}));
function Oo(n) {
  return isBoolean(n) ? n : [1, 3].includes(n);
}
function Mo(n) {
  return isBoolean(n) ? n : [1, 2].includes(n);
}
function Bo(n) {
  return isBoolean(n) ? n : n != 0;
}
var Fo = {
    command_timeout: 5,
    templates_folder: '',
    templates_pairs: [['', '']],
    trigger_on_file_creation: !1,
    auto_jump_to_cursor: !1,
    enable_system_commands: !1,
    shell_path: '',
    user_scripts_folder: '',
    enable_folder_templates: !0,
    folder_templates: [{ folder: '', template: '' }],
    enable_file_templates: !1,
    file_templates: [{ regex: '.*', template: '' }],
    syntax_highlighting: !0,
    syntax_highlighting_mobile: !1,
    enabled_templates_hotkeys: [''],
    startup_templates: [''],
    intellisense_render: ft.RenderDescriptionParameterReturn,
  },
  ri = class extends L.PluginSettingTab {
    constructor(e) {
      super(e.app, e);
      this.plugin = e;
    }
    display() {
      (this.containerEl.empty(),
        this.add_template_folder_setting(),
        this.add_internal_functions_setting(),
        this.add_syntax_highlighting_settings(),
        this.add_auto_jump_to_cursor(),
        this.add_trigger_on_new_file_creation_setting(),
        this.plugin.settings.trigger_on_file_creation &&
          (this.add_folder_templates_setting(),
          this.add_file_templates_setting()),
        this.add_templates_hotkeys_setting(),
        this.add_startup_templates_setting(),
        this.add_user_script_functions_setting(),
        this.add_user_system_command_functions_setting(),
        this.add_donating_setting());
    }
    add_template_folder_setting() {
      new L.Setting(this.containerEl)
        .setName('Template folder location')
        .setDesc('Files in this folder will be available as templates.')
        .addSearch(e => {
          (new bn(this.app, e.inputEl),
            e
              .setPlaceholder('Example: folder1/folder2')
              .setValue(this.plugin.settings.templates_folder)
              .onChange(t => {
                ((t = t.trim()),
                  (t = t.replace(/\/$/, '')),
                  (this.plugin.settings.templates_folder = t),
                  this.plugin.save_settings());
              }),
            e.containerEl.addClass('templater_search'));
        });
    }
    add_internal_functions_setting() {
      let e = document.createDocumentFragment();
      (e.append(
        'Templater provides multiples predefined variables / functions that you can use.',
        e.createEl('br'),
        'Check the ',
        e.createEl('a', {
          href: 'https://silentvoid13.github.io/Templater/',
          text: 'documentation',
        }),
        ' to get a list of all the available internal variables / functions.'
      ),
        new L.Setting(this.containerEl)
          .setName('Internal variables and functions')
          .setDesc(e));
    }
    add_syntax_highlighting_settings() {
      let e = document.createDocumentFragment();
      e.append('Adds syntax highlighting for Templater commands in edit mode.');
      let t = document.createDocumentFragment();
      (t.append(
        'Adds syntax highlighting for Templater commands in edit mode on mobile. Use with caution: this may break live preview on mobile platforms.'
      ),
        new L.Setting(this.containerEl)
          .setName('Syntax highlighting on desktop')
          .setDesc(e)
          .addToggle(r => {
            r.setValue(this.plugin.settings.syntax_highlighting).onChange(i => {
              ((this.plugin.settings.syntax_highlighting = i),
                this.plugin.save_settings(),
                this.plugin.event_handler.update_syntax_highlighting());
            });
          }),
        new L.Setting(this.containerEl)
          .setName('Syntax highlighting on mobile')
          .setDesc(t)
          .addToggle(r => {
            r.setValue(
              this.plugin.settings.syntax_highlighting_mobile
            ).onChange(i => {
              ((this.plugin.settings.syntax_highlighting_mobile = i),
                this.plugin.save_settings(),
                this.plugin.event_handler.update_syntax_highlighting());
            });
          }));
    }
    add_auto_jump_to_cursor() {
      let e = document.createDocumentFragment();
      (e.append(
        'Automatically triggers ',
        e.createEl('code', { text: 'tp.file.cursor' }),
        ' after inserting a template.',
        e.createEl('br'),
        'You can also set a hotkey to manually trigger ',
        e.createEl('code', { text: 'tp.file.cursor' }),
        '.'
      ),
        new L.Setting(this.containerEl)
          .setName('Automatic jump to cursor')
          .setDesc(e)
          .addToggle(t => {
            t.setValue(this.plugin.settings.auto_jump_to_cursor).onChange(r => {
              ((this.plugin.settings.auto_jump_to_cursor = r),
                this.plugin.save_settings());
            });
          }));
    }
    add_trigger_on_new_file_creation_setting() {
      let e = document.createDocumentFragment();
      (e.append(
        "Templater will listen for the new file creation event, and, if it matches a rule you've set, replace every command it finds in the new file's content. ",
        'This makes Templater compatible with other plugins like the Daily note core plugin, Calendar plugin, Review plugin, Note refactor plugin, etc. ',
        e.createEl('br'),
        e.createEl('br'),
        'Make sure to set up rules under either folder templates or file regex template below.',
        e.createEl('br'),
        e.createEl('br'),
        e.createEl('b', { text: 'Warning: ' }),
        "This can be dangerous if you create new files with unknown / unsafe content on creation. Make sure that every new file's content is safe on creation."
      ),
        new L.Setting(this.containerEl)
          .setName('Trigger Templater on new file creation')
          .setDesc(e)
          .addToggle(t => {
            t.setValue(this.plugin.settings.trigger_on_file_creation).onChange(
              r => {
                ((this.plugin.settings.trigger_on_file_creation = r),
                  this.plugin.save_settings(),
                  this.plugin.event_handler.update_trigger_file_on_creation(),
                  this.display());
              }
            );
          }));
    }
    add_templates_hotkeys_setting() {
      new L.Setting(this.containerEl).setName('Template hotkeys').setHeading();
      let e = document.createDocumentFragment();
      (e.append('Template hotkeys allows you to bind a template to a hotkey.'),
        new L.Setting(this.containerEl).setDesc(e),
        this.plugin.settings.enabled_templates_hotkeys.forEach((t, r) => {
          new L.Setting(this.containerEl)
            .addSearch(o => {
              (new nn(o.inputEl, this.plugin, Le.TemplateFiles),
                o
                  .setPlaceholder('Example: folder1/template_file')
                  .setValue(t)
                  .onChange(a => {
                    if (
                      a &&
                      this.plugin.settings.enabled_templates_hotkeys.contains(a)
                    ) {
                      oe(new O('This template is already bound to a hotkey'));
                      return;
                    }
                    (this.plugin.command_handler.add_template_hotkey(
                      this.plugin.settings.enabled_templates_hotkeys[r],
                      a
                    ),
                      (this.plugin.settings.enabled_templates_hotkeys[r] = a),
                      this.plugin.save_settings());
                  }),
                o.containerEl.addClass('templater_search'));
            })
            .addExtraButton(o => {
              o.setIcon('any-key')
                .setTooltip('Configure Hotkey')
                .onClick(() => {
                  this.app.setting.openTabById('hotkeys');
                  let a = this.app.setting.activeTab;
                  ((a.searchComponent.inputEl.value = t),
                    a.updateHotkeyVisibility());
                });
            })
            .addExtraButton(o => {
              o.setIcon('up-chevron-glyph')
                .setTooltip('Move up')
                .onClick(() => {
                  (Pt(this.plugin.settings.enabled_templates_hotkeys, r, r - 1),
                    this.plugin.save_settings(),
                    this.display());
                });
            })
            .addExtraButton(o => {
              o.setIcon('down-chevron-glyph')
                .setTooltip('Move down')
                .onClick(() => {
                  (Pt(this.plugin.settings.enabled_templates_hotkeys, r, r + 1),
                    this.plugin.save_settings(),
                    this.display());
                });
            })
            .addExtraButton(o => {
              o.setIcon('cross')
                .setTooltip('Delete')
                .onClick(() => {
                  (this.plugin.command_handler.remove_template_hotkey(
                    this.plugin.settings.enabled_templates_hotkeys[r]
                  ),
                    this.plugin.settings.enabled_templates_hotkeys.splice(r, 1),
                    this.plugin.save_settings(),
                    this.display());
                });
            })
            .infoEl.remove();
        }),
        new L.Setting(this.containerEl).addButton(t => {
          t.setButtonText('Add new hotkey for template')
            .setCta()
            .onClick(() => {
              (this.plugin.settings.enabled_templates_hotkeys.push(''),
                this.plugin.save_settings(),
                this.display());
            });
        }));
    }
    add_folder_templates_setting() {
      new L.Setting(this.containerEl).setName('Folder templates').setHeading();
      let e = document.createDocumentFragment();
      (e.append(
        'Folder templates are triggered when a new ',
        e.createEl('strong', { text: 'empty ' }),
        'file is created in a given folder.',
        e.createEl('br'),
        'Templater will fill the empty file with the specified template.',
        e.createEl('br'),
        'The deepest match is used. A global default template would be defined on the root ',
        e.createEl('code', { text: '/' }),
        '.'
      ),
        new L.Setting(this.containerEl).setDesc(e));
      let t = document.createDocumentFragment();
      (t.append(
        'When enabled, Templater will make use of the folder templates defined below. This option is mutually exclusive with file regex templates below, so enabling one will disable the other.'
      ),
        new L.Setting(this.containerEl)
          .setName('Enable folder templates')
          .setDesc(t)
          .addToggle(r => {
            r.setValue(this.plugin.settings.enable_folder_templates).onChange(
              i => {
                ((this.plugin.settings.enable_folder_templates = i),
                  i && (this.plugin.settings.enable_file_templates = !1),
                  this.plugin.save_settings(),
                  this.display());
              }
            );
          }),
        !!this.plugin.settings.enable_folder_templates &&
          (this.plugin.settings.folder_templates.forEach((r, i) => {
            new L.Setting(this.containerEl)
              .addSearch(a => {
                (new bn(this.app, a.inputEl),
                  a
                    .setPlaceholder('Folder')
                    .setValue(r.folder)
                    .onChange(l => {
                      if (
                        l &&
                        this.plugin.settings.folder_templates.some(
                          c => c.folder == l
                        )
                      ) {
                        oe(
                          new O(
                            'This folder already has a template associated with it'
                          )
                        );
                        return;
                      }
                      ((this.plugin.settings.folder_templates[i].folder = l),
                        this.plugin.save_settings());
                    }),
                  a.containerEl.addClass('templater_search'));
              })
              .addSearch(a => {
                (new nn(a.inputEl, this.plugin, Le.TemplateFiles),
                  a
                    .setPlaceholder('Template')
                    .setValue(r.template)
                    .onChange(l => {
                      ((this.plugin.settings.folder_templates[i].template = l),
                        this.plugin.save_settings());
                    }),
                  a.containerEl.addClass('templater_search'));
              })
              .addExtraButton(a => {
                a.setIcon('up-chevron-glyph')
                  .setTooltip('Move up')
                  .onClick(() => {
                    (Pt(this.plugin.settings.folder_templates, i, i - 1),
                      this.plugin.save_settings(),
                      this.display());
                  });
              })
              .addExtraButton(a => {
                a.setIcon('down-chevron-glyph')
                  .setTooltip('Move down')
                  .onClick(() => {
                    (Pt(this.plugin.settings.folder_templates, i, i + 1),
                      this.plugin.save_settings(),
                      this.display());
                  });
              })
              .addExtraButton(a => {
                a.setIcon('cross')
                  .setTooltip('Delete')
                  .onClick(() => {
                    (this.plugin.settings.folder_templates.splice(i, 1),
                      this.plugin.save_settings(),
                      this.display());
                  });
              })
              .infoEl.remove();
          }),
          new L.Setting(this.containerEl).addButton(r => {
            r.setButtonText('Add new folder template')
              .setTooltip('Add additional folder template')
              .setCta()
              .onClick(() => {
                (this.plugin.settings.folder_templates.push({
                  folder: '',
                  template: '',
                }),
                  this.plugin.save_settings(),
                  this.display());
              });
          })));
    }
    add_file_templates_setting() {
      new L.Setting(this.containerEl)
        .setName('File regex templates')
        .setHeading();
      let e = document.createDocumentFragment();
      (e.append(
        'File regex templates are triggered when a new ',
        e.createEl('strong', { text: 'empty' }),
        ' file is created that matches one of them. Templater will fill the empty file with the specified template.',
        e.createEl('br'),
        'The first match from the top is used, so the order of the rules is important.',
        e.createEl('br'),
        'Use ',
        e.createEl('code', { text: '.*' }),
        ' as a final catch-all, if you need it.'
      ),
        new L.Setting(this.containerEl).setDesc(e));
      let t = document.createDocumentFragment();
      (t.append(
        'When enabled, Templater will make use of the file regex templates defined below. This option is mutually exclusive with folder templates above, so enabling one will disable the other.'
      ),
        new L.Setting(this.containerEl)
          .setName('Enable file regex templates')
          .setDesc(t)
          .addToggle(r => {
            r.setValue(this.plugin.settings.enable_file_templates).onChange(
              i => {
                ((this.plugin.settings.enable_file_templates = i),
                  i && (this.plugin.settings.enable_folder_templates = !1),
                  this.plugin.save_settings(),
                  this.display());
              }
            );
          }),
        !!this.plugin.settings.enable_file_templates &&
          (this.plugin.settings.file_templates.forEach((r, i) => {
            new L.Setting(this.containerEl)
              .addText(a => {
                (a
                  .setPlaceholder('File regex')
                  .setValue(r.regex)
                  .onChange(l => {
                    ((this.plugin.settings.file_templates[i].regex = l),
                      this.plugin.save_settings());
                  }),
                  a.inputEl.addClass('templater_search'));
              })
              .addSearch(a => {
                (new nn(a.inputEl, this.plugin, Le.TemplateFiles),
                  a
                    .setPlaceholder('Template')
                    .setValue(r.template)
                    .onChange(l => {
                      ((this.plugin.settings.file_templates[i].template = l),
                        this.plugin.save_settings());
                    }),
                  a.containerEl.addClass('templater_search'));
              })
              .addExtraButton(a => {
                a.setIcon('up-chevron-glyph')
                  .setTooltip('Move up')
                  .onClick(() => {
                    (Pt(this.plugin.settings.file_templates, i, i - 1),
                      this.plugin.save_settings(),
                      this.display());
                  });
              })
              .addExtraButton(a => {
                a.setIcon('down-chevron-glyph')
                  .setTooltip('Move down')
                  .onClick(() => {
                    (Pt(this.plugin.settings.file_templates, i, i + 1),
                      this.plugin.save_settings(),
                      this.display());
                  });
              })
              .addExtraButton(a => {
                a.setIcon('cross')
                  .setTooltip('Delete')
                  .onClick(() => {
                    (this.plugin.settings.file_templates.splice(i, 1),
                      this.plugin.save_settings(),
                      this.display());
                  });
              })
              .infoEl.remove();
          }),
          new L.Setting(this.containerEl).addButton(r => {
            r.setButtonText('Add new file regex')
              .setTooltip('Add additional file regex')
              .setCta()
              .onClick(() => {
                (this.plugin.settings.file_templates.push({
                  regex: '',
                  template: '',
                }),
                  this.plugin.save_settings(),
                  this.display());
              });
          })));
    }
    add_startup_templates_setting() {
      new L.Setting(this.containerEl).setName('Startup templates').setHeading();
      let e = document.createDocumentFragment();
      (e.append(
        'Startup templates are templates that will get executed once when Templater starts.',
        e.createEl('br'),
        "These templates won't output anything.",
        e.createEl('br'),
        'This can be useful to set up templates adding hooks to Obsidian events for example.'
      ),
        new L.Setting(this.containerEl).setDesc(e),
        this.plugin.settings.startup_templates.forEach((t, r) => {
          new L.Setting(this.containerEl)
            .addSearch(o => {
              (new nn(o.inputEl, this.plugin, Le.TemplateFiles),
                o
                  .setPlaceholder('Example: folder1/template_file')
                  .setValue(t)
                  .onChange(a => {
                    if (
                      a &&
                      this.plugin.settings.startup_templates.contains(a)
                    ) {
                      oe(new O('This startup template already exist'));
                      return;
                    }
                    ((this.plugin.settings.startup_templates[r] = a),
                      this.plugin.save_settings());
                  }),
                o.containerEl.addClass('templater_search'));
            })
            .addExtraButton(o => {
              o.setIcon('cross')
                .setTooltip('Delete')
                .onClick(() => {
                  (this.plugin.settings.startup_templates.splice(r, 1),
                    this.plugin.save_settings(),
                    this.display());
                });
            })
            .infoEl.remove();
        }),
        new L.Setting(this.containerEl).addButton(t => {
          t.setButtonText('Add new startup template')
            .setCta()
            .onClick(() => {
              (this.plugin.settings.startup_templates.push(''),
                this.plugin.save_settings(),
                this.display());
            });
        }));
    }
    add_user_script_functions_setting() {
      new L.Setting(this.containerEl)
        .setName('User script functions')
        .setHeading();
      let e = document.createDocumentFragment();
      (e.append(
        'All JavaScript files in this folder will be loaded as CommonJS modules, to import custom user functions.',
        e.createEl('br'),
        'The folder needs to be accessible from the vault.',
        e.createEl('br'),
        'Check the ',
        e.createEl('a', {
          href: 'https://silentvoid13.github.io/Templater/',
          text: 'documentation',
        }),
        ' for more information.'
      ),
        new L.Setting(this.containerEl)
          .setName('Script files folder location')
          .setDesc(e)
          .addSearch(r => {
            (new bn(this.app, r.inputEl),
              r
                .setPlaceholder('Example: folder1/folder2')
                .setValue(this.plugin.settings.user_scripts_folder)
                .onChange(i => {
                  ((this.plugin.settings.user_scripts_folder = i),
                    this.plugin.save_settings());
                }),
              r.containerEl.addClass('templater_search'));
          }),
        new L.Setting(this.containerEl)
          .setName('User script intellisense')
          .setDesc(
            "Determine how you'd like to have user script intellisense render. Note values will not render if not in the script."
          )
          .addDropdown(r => {
            r.addOption('0', 'Turn off intellisense')
              .addOption(
                '1',
                'Render method description, parameters list, and return'
              )
              .addOption('2', 'Render method description and parameters list')
              .addOption('3', 'Render method description and return')
              .addOption('4', 'Render method description')
              .setValue(this.plugin.settings.intellisense_render.toString())
              .onChange(i => {
                ((this.plugin.settings.intellisense_render = parseInt(i)),
                  this.plugin.save_settings());
              });
          }),
        (e = document.createDocumentFragment()));
      let t;
      if (!this.plugin.settings.user_scripts_folder)
        t = 'No user scripts folder set';
      else {
        let r = ke(
          () => ze(this.app, this.plugin.settings.user_scripts_folder),
          "User scripts folder doesn't exist"
        );
        if (!r || r.length === 0) t = 'No user scripts detected';
        else {
          let i = 0;
          for (let o of r)
            o.extension === 'js' &&
              (i++,
              e.append(e.createEl('li', { text: `tp.user.${o.basename}` })));
          t = `Detected ${i} User Script(s)`;
        }
      }
      new L.Setting(this.containerEl)
        .setName(t)
        .setDesc(e)
        .addExtraButton(r => {
          r.setIcon('sync')
            .setTooltip('Refresh')
            .onClick(() => {
              this.display();
            });
        });
    }
    add_user_system_command_functions_setting() {
      let e = document.createDocumentFragment();
      if (
        (e.append(
          'Allows you to create user functions linked to system commands.',
          e.createEl('br'),
          e.createEl('b', { text: 'Warning: ' }),
          'It can be dangerous to execute arbitrary system commands from untrusted sources. Only run system commands that you understand, from trusted sources.'
        ),
        new L.Setting(this.containerEl)
          .setName('User system command functions')
          .setHeading(),
        new L.Setting(this.containerEl)
          .setName('Enable user system command functions')
          .setDesc(e)
          .addToggle(t => {
            t.setValue(this.plugin.settings.enable_system_commands).onChange(
              r => {
                ((this.plugin.settings.enable_system_commands = r),
                  this.plugin.save_settings(),
                  this.display());
              }
            );
          }),
        this.plugin.settings.enable_system_commands)
      ) {
        (new L.Setting(this.containerEl)
          .setName('Timeout')
          .setDesc('Maximum timeout in seconds for a system command.')
          .addText(o => {
            o.setPlaceholder('Timeout')
              .setValue(this.plugin.settings.command_timeout.toString())
              .onChange(a => {
                let l = Number(a);
                if (isNaN(l)) {
                  oe(new O('Timeout must be a number'));
                  return;
                }
                ((this.plugin.settings.command_timeout = l),
                  this.plugin.save_settings());
              });
          }),
          (e = document.createDocumentFragment()),
          e.append(
            'Full path to the shell binary to execute the command with.',
            e.createEl('br'),
            "This setting is optional and will default to the system's default shell if not specified.",
            e.createEl('br'),
            "You can use forward slashes ('/') as path separators on all platforms if in doubt."
          ),
          new L.Setting(this.containerEl)
            .setName('Shell binary location')
            .setDesc(e)
            .addText(o => {
              o.setPlaceholder('Example: /bin/bash, ...')
                .setValue(this.plugin.settings.shell_path)
                .onChange(a => {
                  ((this.plugin.settings.shell_path = a),
                    this.plugin.save_settings());
                });
            }));
        let t = 1;
        this.plugin.settings.templates_pairs.forEach(o => {
          let a = this.containerEl.createEl('div');
          a.addClass('templater_div');
          let l = this.containerEl.createEl('h4', {
            text: 'User function n\xB0' + t,
          });
          (l.addClass('templater_title'),
            new L.Setting(this.containerEl)
              .addExtraButton(d => {
                d.setIcon('cross')
                  .setTooltip('Delete')
                  .onClick(() => {
                    let m = this.plugin.settings.templates_pairs.indexOf(o);
                    m > -1 &&
                      (this.plugin.settings.templates_pairs.splice(m, 1),
                      this.plugin.save_settings(),
                      this.display());
                  });
              })
              .addText(d => {
                let m = d
                  .setPlaceholder('Function name')
                  .setValue(o[0])
                  .onChange(y => {
                    let b = this.plugin.settings.templates_pairs.indexOf(o);
                    b > -1 &&
                      ((this.plugin.settings.templates_pairs[b][0] = y),
                      this.plugin.save_settings());
                  });
                return (m.inputEl.addClass('templater_template'), m);
              })
              .addTextArea(d => {
                let m = d
                  .setPlaceholder('System command')
                  .setValue(o[1])
                  .onChange(y => {
                    let b = this.plugin.settings.templates_pairs.indexOf(o);
                    b > -1 &&
                      ((this.plugin.settings.templates_pairs[b][1] = y),
                      this.plugin.save_settings());
                  });
                return (
                  m.inputEl.setAttr('rows', 2),
                  m.inputEl.addClass('templater_cmd'),
                  m
                );
              })
              .infoEl.remove(),
            a.appendChild(l),
            a.appendChild(this.containerEl.lastChild),
            (t += 1));
        });
        let r = this.containerEl.createEl('div');
        (r.addClass('templater_div2'),
          new L.Setting(this.containerEl)
            .addButton(o => {
              o.setButtonText('Add new user function')
                .setCta()
                .onClick(() => {
                  (this.plugin.settings.templates_pairs.push(['', '']),
                    this.plugin.save_settings(),
                    this.display());
                });
            })
            .infoEl.remove(),
          r.appendChild(this.containerEl.lastChild));
      }
    }
    add_donating_setting() {
      let e = new L.Setting(this.containerEl)
          .setName('Donate')
          .setDesc(
            'If you like this Plugin, consider donating to support continued development.'
          ),
        t = document.createElement('a');
      (t.setAttribute('href', 'https://github.com/sponsors/silentvoid13'),
        t.addClass('templater_donating'));
      let r = document.createElement('img');
      ((r.src =
        'https://img.shields.io/static/v1?label=Sponsor&message=%E2%9D%A4&logo=GitHub&color=%23fe8e86'),
        t.appendChild(r));
      let i = document.createElement('a');
      (i.setAttribute(
        'href',
        'https://www.paypal.com/donate?hosted_button_id=U2SRGAFYXT32Q'
      ),
        i.addClass('templater_donating'));
      let o = document.createElement('img');
      ((o.src =
        'https://img.shields.io/badge/paypal-silentvoid13-yellow?style=social&logo=paypal'),
        i.appendChild(o),
        e.settingEl.appendChild(t),
        e.settingEl.appendChild(i));
    }
  };
var dr = X(require('obsidian'));
var rn;
(function (t) {
  ((t[(t.InsertTemplate = 0)] = 'InsertTemplate'),
    (t[(t.CreateNoteTemplate = 1)] = 'CreateNoteTemplate'));
})(rn || (rn = {}));
var ii = class extends dr.FuzzySuggestModal {
  constructor(e) {
    super(e.app);
    ((this.plugin = e), this.setPlaceholder('Type name of a template...'));
  }
  getItems() {
    if (!this.plugin.settings.templates_folder)
      return this.app.vault.getMarkdownFiles();
    let e = ke(
      () => ze(this.plugin.app, this.plugin.settings.templates_folder),
      `Couldn't retrieve template files from templates folder ${this.plugin.settings.templates_folder}`
    );
    return e || [];
  }
  getItemText(e) {
    let t = e.path;
    if (
      e.path.startsWith(this.plugin.settings.templates_folder) &&
      (0, dr.normalizePath)(this.plugin.settings.templates_folder) != '/'
    ) {
      let r = this.plugin.settings.templates_folder.length,
        i = this.plugin.settings.templates_folder.endsWith('/') ? r : r + 1;
      t = e.path.slice(i);
    }
    return t.split('.').slice(0, -1).join('.');
  }
  onChooseItem(e) {
    switch (this.open_mode) {
      case 0:
        this.plugin.templater.append_template_to_active_file(e);
        break;
      case 1:
        this.plugin.templater.create_new_note_from_template(
          e,
          this.creation_folder
        );
        break;
    }
  }
  start() {
    try {
      this.open();
    } catch (e) {
      oe(e);
    }
  }
  insert_template() {
    ((this.open_mode = 0), this.start());
  }
  create_new_note_from_template(e) {
    ((this.creation_folder = e), (this.open_mode = 1), this.start());
  }
};
var Io = 'Error_MobileUnsupportedTemplate',
  qo =
    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 51.1328 28.7"><path d="M0 15.14 0 10.15 18.67 1.51 18.67 6.03 4.72 12.33 4.72 12.76 18.67 19.22 18.67 23.74 0 15.14ZM33.6928 1.84C33.6928 1.84 33.9761 2.1467 34.5428 2.76C35.1094 3.38 35.3928 4.56 35.3928 6.3C35.3928 8.0466 34.8195 9.54 33.6728 10.78C32.5261 12.02 31.0995 12.64 29.3928 12.64C27.6862 12.64 26.2661 12.0267 25.1328 10.8C23.9928 9.5733 23.4228 8.0867 23.4228 6.34C23.4228 4.6 23.9995 3.1066 25.1528 1.86C26.2994.62 27.7261 0 29.4328 0C31.1395 0 32.5594.6133 33.6928 1.84M49.8228.67 29.5328 28.38 24.4128 28.38 44.7128.67 49.8228.67M31.0328 8.38C31.0328 8.38 31.1395 8.2467 31.3528 7.98C31.5662 7.7067 31.6728 7.1733 31.6728 6.38C31.6728 5.5867 31.4461 4.92 30.9928 4.38C30.5461 3.84 29.9995 3.57 29.3528 3.57C28.7061 3.57 28.1695 3.84 27.7428 4.38C27.3228 4.92 27.1128 5.5867 27.1128 6.38C27.1128 7.1733 27.3361 7.84 27.7828 8.38C28.2361 8.9267 28.7861 9.2 29.4328 9.2C30.0795 9.2 30.6128 8.9267 31.0328 8.38M49.4328 17.9C49.4328 17.9 49.7161 18.2067 50.2828 18.82C50.8495 19.4333 51.1328 20.6133 51.1328 22.36C51.1328 24.1 50.5594 25.59 49.4128 26.83C48.2595 28.0766 46.8295 28.7 45.1228 28.7C43.4228 28.7 42.0028 28.0833 40.8628 26.85C39.7295 25.6233 39.1628 24.1366 39.1628 22.39C39.1628 20.65 39.7361 19.16 40.8828 17.92C42.0361 16.6733 43.4628 16.05 45.1628 16.05C46.8694 16.05 48.2928 16.6667 49.4328 17.9M46.8528 24.52C46.8528 24.52 46.9595 24.3833 47.1728 24.11C47.3795 23.8367 47.4828 23.3033 47.4828 22.51C47.4828 21.7167 47.2595 21.05 46.8128 20.51C46.3661 19.97 45.8162 19.7 45.1628 19.7C44.5161 19.7 43.9828 19.97 43.5628 20.51C43.1428 21.05 42.9328 21.7167 42.9328 22.51C42.9328 23.3033 43.1561 23.9733 43.6028 24.52C44.0494 25.06 44.5961 25.33 45.2428 25.33C45.8895 25.33 46.4261 25.06 46.8528 24.52Z" fill="currentColor"/></svg>';
var Oe = X(require('obsidian'));
var dt = X(require('obsidian'));
var we = class {
  constructor(e) {
    this.plugin = e;
    this.static_functions = new Map();
    this.dynamic_functions = new Map();
  }
  getName() {
    return this.name;
  }
  async init() {
    (await this.create_static_templates(),
      (this.static_object = Object.fromEntries(this.static_functions)));
  }
  async generate_object(e) {
    return (
      (this.config = e),
      await this.create_dynamic_templates(),
      { ...this.static_object, ...Object.fromEntries(this.dynamic_functions) }
    );
  }
};
var oi = class extends we {
  constructor() {
    super(...arguments);
    this.name = 'date';
  }
  async create_static_templates() {
    (this.static_functions.set('now', this.generate_now()),
      this.static_functions.set('tomorrow', this.generate_tomorrow()),
      this.static_functions.set('weekday', this.generate_weekday()),
      this.static_functions.set('yesterday', this.generate_yesterday()));
  }
  async create_dynamic_templates() {}
  async teardown() {}
  generate_now() {
    return (e = 'YYYY-MM-DD', t, r, i) => {
      if (r && !(0, dt.moment)(r, i).isValid())
        throw new O(
          "Invalid reference date format, try specifying one with the argument 'reference_format'"
        );
      let o;
      return (
        typeof t == 'string'
          ? (o = dt.moment.duration(t))
          : typeof t == 'number' && (o = dt.moment.duration(t, 'days')),
        (0, dt.moment)(r, i).add(o).format(e)
      );
    };
  }
  generate_tomorrow() {
    return (e = 'YYYY-MM-DD') => (0, dt.moment)().add(1, 'days').format(e);
  }
  generate_weekday() {
    return (e = 'YYYY-MM-DD', t, r, i) => {
      if (r && !(0, dt.moment)(r, i).isValid())
        throw new O(
          "Invalid reference date format, try specifying one with the argument 'reference_format'"
        );
      return (0, dt.moment)(r, i).weekday(t).format(e);
    };
  }
  generate_yesterday() {
    return (e = 'YYYY-MM-DD') => (0, dt.moment)().add(-1, 'days').format(e);
  }
};
var le = X(require('obsidian'));
var Lo = 10,
  ai = class extends we {
    constructor() {
      super(...arguments);
      this.name = 'file';
      this.include_depth = 0;
      this.create_new_depth = 0;
      this.linkpath_regex = new RegExp('^\\[\\[(.*)\\]\\]$');
    }
    async create_static_templates() {
      (this.static_functions.set(
        'creation_date',
        this.generate_creation_date()
      ),
        this.static_functions.set('create_new', this.generate_create_new()),
        this.static_functions.set('cursor', this.generate_cursor()),
        this.static_functions.set(
          'cursor_append',
          this.generate_cursor_append()
        ),
        this.static_functions.set('exists', this.generate_exists()),
        this.static_functions.set('find_tfile', this.generate_find_tfile()),
        this.static_functions.set('folder', this.generate_folder()),
        this.static_functions.set('include', this.generate_include()),
        this.static_functions.set(
          'last_modified_date',
          this.generate_last_modified_date()
        ),
        this.static_functions.set('move', this.generate_move()),
        this.static_functions.set('path', this.generate_path()),
        this.static_functions.set('rename', this.generate_rename()),
        this.static_functions.set('selection', this.generate_selection()));
    }
    async create_dynamic_templates() {
      (this.dynamic_functions.set('content', await this.generate_content()),
        this.dynamic_functions.set('tags', this.generate_tags()),
        this.dynamic_functions.set('title', this.generate_title()));
    }
    async teardown() {}
    async generate_content() {
      return await this.plugin.app.vault.read(this.config.target_file);
    }
    generate_create_new() {
      return async (e, t, r = !1, i) => {
        if (((this.create_new_depth += 1), this.create_new_depth > Lo))
          throw (
            (this.create_new_depth = 0),
            new O('Reached create_new depth limit (max = 10)')
          );
        let o = await this.plugin.templater.create_new_note_from_template(
          e,
          i,
          t,
          r
        );
        return ((this.create_new_depth -= 1), o);
      };
    }
    generate_creation_date() {
      return (e = 'YYYY-MM-DD HH:mm') =>
        (0, le.moment)(this.config.target_file.stat.ctime).format(e);
    }
    generate_cursor() {
      return e => `<% tp.file.cursor(${e ?? ''}) %>`;
    }
    generate_cursor_append() {
      return e => {
        let t = this.plugin.app.workspace.activeEditor;
        if (!t || !t.editor) {
          oe(new O("No active editor, can't append to cursor."));
          return;
        }
        return (t.editor.getDoc().replaceSelection(e), '');
      };
    }
    generate_exists() {
      return async e => {
        let t = (0, le.normalizePath)(e);
        return await this.plugin.app.vault.exists(t);
      };
    }
    generate_find_tfile() {
      return e => {
        let t = (0, le.normalizePath)(e);
        return this.plugin.app.metadataCache.getFirstLinkpathDest(t, '');
      };
    }
    generate_folder() {
      return (e = !1) => {
        let t = this.config.target_file.parent,
          r;
        return (e ? (r = t.path) : (r = t.name), r);
      };
    }
    generate_include() {
      return async e => {
        if (((this.include_depth += 1), this.include_depth > Lo))
          throw (
            (this.include_depth -= 1),
            new O('Reached inclusion depth limit (max = 10)')
          );
        let t;
        if (e instanceof le.TFile) t = await this.plugin.app.vault.read(e);
        else {
          let r;
          if ((r = this.linkpath_regex.exec(e)) === null)
            throw (
              (this.include_depth -= 1),
              new O(
                'Invalid file format, provide an obsidian link between quotes.'
              )
            );
          let { path: i, subpath: o } = (0, le.parseLinktext)(r[1]),
            a = this.plugin.app.metadataCache.getFirstLinkpathDest(i, '');
          if (!a)
            throw ((this.include_depth -= 1), new O(`File ${e} doesn't exist`));
          if (((t = await this.plugin.app.vault.read(a)), o)) {
            let l = this.plugin.app.metadataCache.getFileCache(a);
            if (l) {
              let c = (0, le.resolveSubpath)(l, o);
              c && (t = t.slice(c.start.offset, c.end?.offset));
            }
          }
        }
        try {
          let r = await this.plugin.templater.parser.parse_commands(
            t,
            this.plugin.templater.current_functions_object
          );
          return ((this.include_depth -= 1), r);
        } catch (r) {
          throw ((this.include_depth -= 1), r);
        }
      };
    }
    generate_last_modified_date() {
      return (e = 'YYYY-MM-DD HH:mm') =>
        (0, le.moment)(this.config.target_file.stat.mtime).format(e);
    }
    generate_move() {
      return async (e, t) => {
        let r = t || this.config.target_file,
          i = (0, le.normalizePath)(`${e}.${r.extension}`),
          o = i.replace(/\\/g, '/').split('/');
        if ((o.pop(), o.length)) {
          let a = o.join('/');
          this.plugin.app.vault.getAbstractFileByPath(a) ||
            (await this.plugin.app.vault.createFolder(a));
        }
        return (await this.plugin.app.fileManager.renameFile(r, i), '');
      };
    }
    generate_path() {
      return (e = !1) => {
        let t = '';
        if (le.Platform.isMobile) {
          let r = this.plugin.app.vault.adapter.fs.uri,
            i = this.plugin.app.vault.adapter.basePath;
          t = `${r}/${i}`;
        } else if (
          this.plugin.app.vault.adapter instanceof le.FileSystemAdapter
        )
          t = this.plugin.app.vault.adapter.getBasePath();
        else throw new O('app.vault is not a FileSystemAdapter instance');
        return e
          ? this.config.target_file.path
          : `${t}/${this.config.target_file.path}`;
      };
    }
    generate_rename() {
      return async e => {
        if (e.match(/[\\/:]+/g))
          throw new O(
            'File name cannot contain any of these characters: \\ / :'
          );
        let t = (0, le.normalizePath)(
          `${this.config.target_file.parent.path}/${e}.${this.config.target_file.extension}`
        );
        return (
          await this.plugin.app.fileManager.renameFile(
            this.config.target_file,
            t
          ),
          ''
        );
      };
    }
    generate_selection() {
      return () => {
        let e = this.plugin.app.workspace.activeEditor;
        if (!e || !e.editor)
          throw new O("Active editor is null, can't read selection.");
        return e.editor.getSelection();
      };
    }
    generate_tags() {
      let e = this.plugin.app.metadataCache.getFileCache(
        this.config.target_file
      );
      return e ? (0, le.getAllTags)(e) : null;
    }
    generate_title() {
      return this.config.target_file.basename;
    }
  };
var Ho = X(require('obsidian'));
var si = class extends we {
  constructor() {
    super(...arguments);
    this.name = 'web';
  }
  async create_static_templates() {
    (this.static_functions.set('daily_quote', this.generate_daily_quote()),
      this.static_functions.set('request', this.generate_request()),
      this.static_functions.set(
        'random_picture',
        this.generate_random_picture()
      ));
  }
  async create_dynamic_templates() {}
  async teardown() {}
  async getRequest(e) {
    try {
      let t = await (0, Ho.requestUrl)(e);
      if (t.status < 200 && t.status >= 300)
        throw new O('Error performing GET request');
      return t;
    } catch {
      throw new O('Error performing GET request');
    }
  }
  generate_daily_quote() {
    return async () => {
      try {
        let t = (
            await this.getRequest(
              'https://raw.githubusercontent.com/Zachatoo/quotes-database/refs/heads/main/quotes.json'
            )
          ).json,
          r = t[Math.floor(Math.random() * t.length)],
          { quote: i, author: o } = r;
        return `> [!quote] ${i}
> \u2014 ${o}`;
      } catch {
        return (
          new O('Error generating daily quote'),
          'Error generating daily quote'
        );
      }
    };
  }
  generate_random_picture() {
    return async (e, t, r = !1) => {
      try {
        let i = await this.getRequest(
            `https://templater-unsplash-2.fly.dev/${t ? '?q=' + t : ''}`
          ).then(a => a.json),
          o = i.full;
        if (e && !r)
          if (e.includes('x')) {
            let [a, l] = e.split('x');
            o = o.concat(`&w=${a}&h=${l}`);
          } else o = o.concat(`&w=${e}`);
        return r
          ? `![photo by ${i.photog}(${i.photogUrl}) on Unsplash|${e}](${o})`
          : `![photo by ${i.photog}(${i.photogUrl}) on Unsplash](${o})`;
      } catch {
        return (
          new O('Error generating random picture'),
          'Error generating random picture'
        );
      }
    };
  }
  generate_request() {
    return async (e, t) => {
      try {
        let i = await (await this.getRequest(e)).json;
        return t && i
          ? t.split('.').reduce((o, a) => {
              if (o && o.hasOwnProperty(a)) return o[a];
              throw new Error(`Path ${t} not found in the JSON response`);
            }, i)
          : i;
      } catch (r) {
        throw (console.error(r), new O('Error fetching and extracting value'));
      }
    };
  }
};
var ci = class extends we {
  constructor() {
    super(...arguments);
    this.name = 'hooks';
    this.event_refs = [];
  }
  async create_static_templates() {
    this.static_functions.set(
      'on_all_templates_executed',
      this.generate_on_all_templates_executed()
    );
  }
  async create_dynamic_templates() {}
  async teardown() {
    (this.event_refs.forEach(e => {
      e.e.offref(e);
    }),
      (this.event_refs = []));
  }
  generate_on_all_templates_executed() {
    return e => {
      let t = this.plugin.app.workspace.on(
        'templater:all-templates-executed',
        async () => {
          (await ar(1), e());
        }
      );
      t && this.event_refs.push(t);
    };
  }
};
var li = class extends we {
  constructor() {
    super(...arguments);
    this.name = 'frontmatter';
  }
  async create_static_templates() {}
  async create_dynamic_templates() {
    let e = this.plugin.app.metadataCache.getFileCache(this.config.target_file);
    this.dynamic_functions = new Map(Object.entries(e?.frontmatter || {}));
  }
  async teardown() {}
};
var Je = X(require('obsidian'));
var pi = class extends Je.Modal {
  constructor(e, t, r, i) {
    super(e);
    this.prompt_text = t;
    this.default_value = r;
    this.multi_line = i;
    this.submitted = !1;
  }
  onOpen() {
    (this.titleEl.setText(this.prompt_text), this.createForm());
  }
  onClose() {
    (this.contentEl.empty(),
      this.submitted || this.reject(new O('Cancelled prompt')));
  }
  createForm() {
    let e = this.contentEl.createDiv();
    e.addClass('templater-prompt-div');
    let t;
    if (this.multi_line) {
      t = new Je.TextAreaComponent(e);
      let r = this.contentEl.createDiv();
      r.addClass('templater-button-div');
      let i = new Je.ButtonComponent(r);
      (i.buttonEl.addClass('mod-cta'),
        i.setButtonText('Submit').onClick(o => {
          this.resolveAndClose(o);
        }));
    } else t = new Je.TextComponent(e);
    ((this.value = this.default_value ?? ''),
      t.inputEl.addClass('templater-prompt-input'),
      t.setPlaceholder('Type text here'),
      t.setValue(this.value),
      t.onChange(r => (this.value = r)),
      t.inputEl.focus(),
      t.inputEl.addEventListener('keydown', r => this.enterCallback(r)));
  }
  enterCallback(e) {
    e.isComposing ||
      e.keyCode === 229 ||
      (this.multi_line
        ? Je.Platform.isDesktop &&
          e.key === 'Enter' &&
          !e.shiftKey &&
          this.resolveAndClose(e)
        : e.key === 'Enter' && this.resolveAndClose(e));
  }
  resolveAndClose(e) {
    ((this.submitted = !0),
      e.preventDefault(),
      this.resolve(this.value),
      this.close());
  }
  async openAndGetValue(e, t) {
    ((this.resolve = e), (this.reject = t), this.open());
  }
};
var $o = X(require('obsidian')),
  ui = class extends $o.FuzzySuggestModal {
    constructor(e, t, r, i, o) {
      super(e);
      this.text_items = t;
      this.items = r;
      this.submitted = !1;
      (this.setPlaceholder(i), o && (this.limit = o));
    }
    getItems() {
      return this.items;
    }
    onClose() {
      this.submitted || this.reject(new O('Cancelled prompt'));
    }
    selectSuggestion(e, t) {
      ((this.submitted = !0), this.close(), this.onChooseSuggestion(e, t));
    }
    getItemText(e) {
      return this.text_items instanceof Function
        ? this.text_items(e)
        : this.text_items[this.items.indexOf(e)] || 'Undefined Text Item';
    }
    onChooseItem(e) {
      this.resolve(e);
    }
    async openAndGetValue(e, t) {
      ((this.resolve = e), (this.reject = t), this.open());
    }
  };
var fi = class extends we {
  constructor() {
    super(...arguments);
    this.name = 'system';
  }
  async create_static_templates() {
    (this.static_functions.set('clipboard', this.generate_clipboard()),
      this.static_functions.set('prompt', this.generate_prompt()),
      this.static_functions.set('suggester', this.generate_suggester()));
  }
  async create_dynamic_templates() {}
  async teardown() {}
  generate_clipboard() {
    return async () => await navigator.clipboard.readText();
  }
  generate_prompt() {
    return async (e, t, r = !1, i = !1) => {
      let o = new pi(this.plugin.app, e, t, i),
        a = new Promise((l, c) => o.openAndGetValue(l, c));
      try {
        return await a;
      } catch (l) {
        if (r) throw l;
        return null;
      }
    };
  }
  generate_suggester() {
    return async (e, t, r = !1, i = '', o) => {
      let a = new ui(this.plugin.app, e, t, i, o),
        l = new Promise((c, d) => a.openAndGetValue(c, d));
      try {
        return await l;
      } catch (c) {
        if (r) throw c;
        return null;
      }
    };
  }
};
var di = class extends we {
  constructor() {
    super(...arguments);
    this.name = 'config';
  }
  async create_static_templates() {}
  async create_dynamic_templates() {}
  async teardown() {}
  async generate_object(e) {
    return e;
  }
};
var mi = class {
  constructor(e) {
    this.plugin = e;
    this.modules_array = [];
    (this.modules_array.push(new oi(this.plugin)),
      this.modules_array.push(new ai(this.plugin)),
      this.modules_array.push(new si(this.plugin)),
      this.modules_array.push(new li(this.plugin)),
      this.modules_array.push(new ci(this.plugin)),
      this.modules_array.push(new fi(this.plugin)),
      this.modules_array.push(new di(this.plugin)));
  }
  async init() {
    for (let e of this.modules_array) await e.init();
  }
  async teardown() {
    for (let e of this.modules_array) await e.teardown();
  }
  async generate_object(e) {
    let t = {};
    for (let r of this.modules_array)
      t[r.getName()] = await r.generate_object(e);
    return t;
  }
};
var En = X(require('obsidian'));
var gi = class {
  constructor(e) {
    this.plugin = e;
    if (
      En.Platform.isMobile ||
      !(this.plugin.app.vault.adapter instanceof En.FileSystemAdapter)
    )
      this.cwd = '';
    else {
      this.cwd = this.plugin.app.vault.adapter.getBasePath();
      let { promisify: t } = require('util'),
        { exec: r } = require('child_process');
      this.exec_promise = t(r);
    }
  }
  async generate_system_functions(e) {
    let t = new Map(),
      r = await this.plugin.templater.functions_generator.generate_object(
        e,
        Qe.INTERNAL
      );
    for (let i of this.plugin.settings.templates_pairs) {
      let o = i[0],
        a = i[1];
      !o ||
        !a ||
        (En.Platform.isMobile
          ? t.set(o, () => new Promise(l => l(Io)))
          : ((a = await this.plugin.templater.parser.parse_commands(a, r)),
            t.set(o, async l => {
              let c = { ...process.env, ...l },
                d = {
                  timeout: this.plugin.settings.command_timeout * 1e3,
                  cwd: this.cwd,
                  env: c,
                  ...(this.plugin.settings.shell_path && {
                    shell: this.plugin.settings.shell_path,
                  }),
                };
              try {
                let { stdout: m } = await this.exec_promise(a, d);
                return m.trimRight();
              } catch (m) {
                throw new O(`Error with User Template ${o}`, m);
              }
            })));
    }
    return t;
  }
  async generate_object(e) {
    let t = await this.generate_system_functions(e);
    return Object.fromEntries(t);
  }
};
var hi = class {
  constructor(e) {
    this.plugin = e;
  }
  async generate_user_script_functions() {
    let e = new Map(),
      t = ke(
        () => ze(this.plugin.app, this.plugin.settings.user_scripts_folder),
        `Couldn't find user script folder "${this.plugin.settings.user_scripts_folder}"`
      );
    if (!t) return new Map();
    for (let r of t)
      r.extension.toLowerCase() === 'js' &&
        (await this.load_user_script_function(r, e));
    return e;
  }
  async load_user_script_function(e, t) {
    let r = c => window.require && window.require(c),
      i = {},
      o = { exports: i },
      a = await this.plugin.app.vault.read(e);
    try {
      window.eval(
        '(function anonymous(require, module, exports){' +
          a +
          `
})`
      )(r, o, i);
    } catch (c) {
      throw new O(`Failed to load user script at "${e.path}".`, c.message);
    }
    let l = i.default || o.exports;
    if (!l)
      throw new O(
        `Failed to load user script at "${e.path}". No exports detected.`
      );
    if (!(l instanceof Function))
      throw new O(
        `Failed to load user script at "${e.path}". Default export is not a function.`
      );
    t.set(`${e.basename}`, l);
  }
  async generate_object() {
    let e = await this.generate_user_script_functions();
    return Object.fromEntries(e);
  }
};
var Ai = class {
  constructor(e) {
    this.plugin = e;
    ((this.user_system_functions = new gi(e)),
      (this.user_script_functions = new hi(e)));
  }
  async generate_object(e) {
    let t = {},
      r = {};
    return (
      this.plugin.settings.enable_system_commands &&
        (t = await this.user_system_functions.generate_object(e)),
      this.plugin.settings.user_scripts_folder &&
        (r = await this.user_script_functions.generate_object()),
      { ...t, ...r }
    );
  }
};
var qs = X(require('obsidian')),
  Qe;
(function (t) {
  ((t[(t.INTERNAL = 0)] = 'INTERNAL'),
    (t[(t.USER_INTERNAL = 1)] = 'USER_INTERNAL'));
})(Qe || (Qe = {}));
var _i = class {
  constructor(e) {
    this.plugin = e;
    ((this.internal_functions = new mi(this.plugin)),
      (this.user_functions = new Ai(this.plugin)));
  }
  async init() {
    await this.internal_functions.init();
  }
  async teardown() {
    await this.internal_functions.teardown();
  }
  additional_functions() {
    return { app: this.plugin.app, obsidian: qs };
  }
  async generate_object(e, t = 1) {
    let r = {},
      i = this.additional_functions(),
      o = await this.internal_functions.generate_object(e),
      a = {};
    switch ((Object.assign(r, i), t)) {
      case 0:
        Object.assign(r, o);
        break;
      case 1:
        ((a = await this.user_functions.generate_object(e)),
          Object.assign(r, { ...o, user: a }));
        break;
    }
    return r;
  }
};
var Vs = {},
  N,
  He = new Array(32).fill(void 0);
He.push(void 0, null, !0, !1);
function Se(n) {
  return He[n];
}
var Tn = He.length;
function Ls(n) {
  n < 36 || ((He[n] = Tn), (Tn = n));
}
function xi(n) {
  let e = Se(n);
  return (Ls(n), e);
}
var Ko = new TextDecoder('utf-8', { ignoreBOM: !0, fatal: !0 });
Ko.decode();
var mr = new Uint8Array();
function gr() {
  return (mr.byteLength === 0 && (mr = new Uint8Array(N.memory.buffer)), mr);
}
function vt(n, e) {
  return Ko.decode(gr().subarray(n, n + e));
}
function wt(n) {
  Tn === He.length && He.push(He.length + 1);
  let e = Tn;
  return ((Tn = He[e]), (He[e] = n), e);
}
var $e = 0,
  hr = new TextEncoder('utf-8'),
  Hs =
    typeof hr.encodeInto == 'function'
      ? function (n, e) {
          return hr.encodeInto(n, e);
        }
      : function (n, e) {
          let t = hr.encode(n);
          return (e.set(t), { read: n.length, written: t.length });
        };
function mt(n, e, t) {
  if (t === void 0) {
    let l = hr.encode(n),
      c = e(l.length);
    return (
      gr()
        .subarray(c, c + l.length)
        .set(l),
      ($e = l.length),
      c
    );
  }
  let r = n.length,
    i = e(r),
    o = gr(),
    a = 0;
  for (; a < r; a++) {
    let l = n.charCodeAt(a);
    if (l > 127) break;
    o[i + a] = l;
  }
  if (a !== r) {
    (a !== 0 && (n = n.slice(a)), (i = t(i, r, (r = a + n.length * 3))));
    let l = gr().subarray(i + a, i + r);
    a += Hs(n, l).written;
  }
  return (($e = a), i);
}
function $s(n) {
  return n == null;
}
var Ar = new Int32Array();
function Ce() {
  return (Ar.byteLength === 0 && (Ar = new Int32Array(N.memory.buffer)), Ar);
}
function yi(n) {
  let e = typeof n;
  if (e == 'number' || e == 'boolean' || n == null) return `${n}`;
  if (e == 'string') return `"${n}"`;
  if (e == 'symbol') {
    let i = n.description;
    return i == null ? 'Symbol' : `Symbol(${i})`;
  }
  if (e == 'function') {
    let i = n.name;
    return typeof i == 'string' && i.length > 0 ? `Function(${i})` : 'Function';
  }
  if (Array.isArray(n)) {
    let i = n.length,
      o = '[';
    i > 0 && (o += yi(n[0]));
    for (let a = 1; a < i; a++) o += ', ' + yi(n[a]);
    return ((o += ']'), o);
  }
  let t = /\[object ([^\]]+)\]/.exec(toString.call(n)),
    r;
  if (t.length > 1) r = t[1];
  else return toString.call(n);
  if (r == 'Object')
    try {
      return 'Object(' + JSON.stringify(n) + ')';
    } catch {
      return 'Object';
    }
  return n instanceof Error
    ? `${n.name}: ${n.message}
${n.stack}`
    : r;
}
function Ks(n, e) {
  if (!(n instanceof e)) throw new Error(`expected instance of ${e.name}`);
  return n.ptr;
}
var _r = 32;
function Rs(n) {
  if (_r == 1) throw new Error('out of js stack');
  return ((He[--_r] = n), _r);
}
function ji(n, e) {
  try {
    return n.apply(this, e);
  } catch (t) {
    N.__wbindgen_exn_store(wt(t));
  }
}
var Ht = class {
    static __wrap(e) {
      let t = Object.create(Ht.prototype);
      return ((t.ptr = e), t);
    }
    __destroy_into_raw() {
      let e = this.ptr;
      return ((this.ptr = 0), e);
    }
    free() {
      let e = this.__destroy_into_raw();
      N.__wbg_parserconfig_free(e);
    }
    get interpolate() {
      let e = N.__wbg_get_parserconfig_interpolate(this.ptr);
      return String.fromCodePoint(e);
    }
    set interpolate(e) {
      N.__wbg_set_parserconfig_interpolate(this.ptr, e.codePointAt(0));
    }
    get execution() {
      let e = N.__wbg_get_parserconfig_execution(this.ptr);
      return String.fromCodePoint(e);
    }
    set execution(e) {
      N.__wbg_set_parserconfig_execution(this.ptr, e.codePointAt(0));
    }
    get single_whitespace() {
      let e = N.__wbg_get_parserconfig_single_whitespace(this.ptr);
      return String.fromCodePoint(e);
    }
    set single_whitespace(e) {
      N.__wbg_set_parserconfig_single_whitespace(this.ptr, e.codePointAt(0));
    }
    get multiple_whitespace() {
      let e = N.__wbg_get_parserconfig_multiple_whitespace(this.ptr);
      return String.fromCodePoint(e);
    }
    set multiple_whitespace(e) {
      N.__wbg_set_parserconfig_multiple_whitespace(this.ptr, e.codePointAt(0));
    }
    constructor(e, t, r, i, o, a, l) {
      let c = mt(e, N.__wbindgen_malloc, N.__wbindgen_realloc),
        d = $e,
        m = mt(t, N.__wbindgen_malloc, N.__wbindgen_realloc),
        y = $e,
        b = mt(l, N.__wbindgen_malloc, N.__wbindgen_realloc),
        E = $e,
        P = N.parserconfig_new(
          c,
          d,
          m,
          y,
          r.codePointAt(0),
          i.codePointAt(0),
          o.codePointAt(0),
          a.codePointAt(0),
          b,
          E
        );
      return Ht.__wrap(P);
    }
    get opening_tag() {
      try {
        let r = N.__wbindgen_add_to_stack_pointer(-16);
        N.parserconfig_opening_tag(r, this.ptr);
        var e = Ce()[r / 4 + 0],
          t = Ce()[r / 4 + 1];
        return vt(e, t);
      } finally {
        (N.__wbindgen_add_to_stack_pointer(16), N.__wbindgen_free(e, t));
      }
    }
    set opening_tag(e) {
      let t = mt(e, N.__wbindgen_malloc, N.__wbindgen_realloc),
        r = $e;
      N.parserconfig_set_opening_tag(this.ptr, t, r);
    }
    get closing_tag() {
      try {
        let r = N.__wbindgen_add_to_stack_pointer(-16);
        N.parserconfig_closing_tag(r, this.ptr);
        var e = Ce()[r / 4 + 0],
          t = Ce()[r / 4 + 1];
        return vt(e, t);
      } finally {
        (N.__wbindgen_add_to_stack_pointer(16), N.__wbindgen_free(e, t));
      }
    }
    set closing_tag(e) {
      let t = mt(e, N.__wbindgen_malloc, N.__wbindgen_realloc),
        r = $e;
      N.parserconfig_set_closing_tag(this.ptr, t, r);
    }
    get global_var() {
      try {
        let r = N.__wbindgen_add_to_stack_pointer(-16);
        N.parserconfig_global_var(r, this.ptr);
        var e = Ce()[r / 4 + 0],
          t = Ce()[r / 4 + 1];
        return vt(e, t);
      } finally {
        (N.__wbindgen_add_to_stack_pointer(16), N.__wbindgen_free(e, t));
      }
    }
    set global_var(e) {
      let t = mt(e, N.__wbindgen_malloc, N.__wbindgen_realloc),
        r = $e;
      N.parserconfig_set_global_var(this.ptr, t, r);
    }
  },
  on = class {
    static __wrap(e) {
      let t = Object.create(on.prototype);
      return ((t.ptr = e), t);
    }
    __destroy_into_raw() {
      let e = this.ptr;
      return ((this.ptr = 0), e);
    }
    free() {
      let e = this.__destroy_into_raw();
      N.__wbg_renderer_free(e);
    }
    constructor(e) {
      Ks(e, Ht);
      var t = e.ptr;
      e.ptr = 0;
      let r = N.renderer_new(t);
      return on.__wrap(r);
    }
    render_content(e, t) {
      try {
        let a = N.__wbindgen_add_to_stack_pointer(-16),
          l = mt(e, N.__wbindgen_malloc, N.__wbindgen_realloc),
          c = $e;
        N.renderer_render_content(a, this.ptr, l, c, Rs(t));
        var r = Ce()[a / 4 + 0],
          i = Ce()[a / 4 + 1],
          o = Ce()[a / 4 + 2];
        if (o) throw xi(i);
        return xi(r);
      } finally {
        (N.__wbindgen_add_to_stack_pointer(16), (He[_r++] = void 0));
      }
    }
  };
async function Ys(n, e) {
  if (typeof Response == 'function' && n instanceof Response) {
    if (typeof WebAssembly.instantiateStreaming == 'function')
      try {
        return await WebAssembly.instantiateStreaming(n, e);
      } catch (r) {
        if (n.headers.get('Content-Type') != 'application/wasm')
          console.warn(
            '`WebAssembly.instantiateStreaming` failed because your server does not serve wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\n',
            r
          );
        else throw r;
      }
    let t = await n.arrayBuffer();
    return await WebAssembly.instantiate(t, e);
  } else {
    let t = await WebAssembly.instantiate(n, e);
    return t instanceof WebAssembly.Instance ? { instance: t, module: n } : t;
  }
}
function Us() {
  let n = {};
  return (
    (n.wbg = {}),
    (n.wbg.__wbindgen_object_drop_ref = function (e) {
      xi(e);
    }),
    (n.wbg.__wbindgen_string_new = function (e, t) {
      let r = vt(e, t);
      return wt(r);
    }),
    (n.wbg.__wbindgen_string_get = function (e, t) {
      let r = Se(t),
        i = typeof r == 'string' ? r : void 0;
      var o = $s(i) ? 0 : mt(i, N.__wbindgen_malloc, N.__wbindgen_realloc),
        a = $e;
      ((Ce()[e / 4 + 1] = a), (Ce()[e / 4 + 0] = o));
    }),
    (n.wbg.__wbg_call_97ae9d8645dc388b = function () {
      return ji(function (e, t) {
        let r = Se(e).call(Se(t));
        return wt(r);
      }, arguments);
    }),
    (n.wbg.__wbg_new_8d2af00bc1e329ee = function (e, t) {
      let r = new Error(vt(e, t));
      return wt(r);
    }),
    (n.wbg.__wbg_message_fe2af63ccc8985bc = function (e) {
      let t = Se(e).message;
      return wt(t);
    }),
    (n.wbg.__wbg_newwithargs_8fe23e3842840c8e = function (e, t, r, i) {
      let o = new Function(vt(e, t), vt(r, i));
      return wt(o);
    }),
    (n.wbg.__wbg_call_168da88779e35f61 = function () {
      return ji(function (e, t, r) {
        let i = Se(e).call(Se(t), Se(r));
        return wt(i);
      }, arguments);
    }),
    (n.wbg.__wbg_call_3999bee59e9f7719 = function () {
      return ji(function (e, t, r, i) {
        let o = Se(e).call(Se(t), Se(r), Se(i));
        return wt(o);
      }, arguments);
    }),
    (n.wbg.__wbindgen_debug_string = function (e, t) {
      let r = yi(Se(t)),
        i = mt(r, N.__wbindgen_malloc, N.__wbindgen_realloc),
        o = $e;
      ((Ce()[e / 4 + 1] = o), (Ce()[e / 4 + 0] = i));
    }),
    (n.wbg.__wbindgen_throw = function (e, t) {
      throw new Error(vt(e, t));
    }),
    n
  );
}
function Gs(n, e) {}
function Ws(n, e) {
  return (
    (N = n.exports),
    (Ro.__wbindgen_wasm_module = e),
    (Ar = new Int32Array()),
    (mr = new Uint8Array()),
    N
  );
}
async function Ro(n) {
  typeof n == 'undefined' && (n = new URL('rusty_engine_bg.wasm', Vs.url));
  let e = Us();
  ((typeof n == 'string' ||
    (typeof Request == 'function' && n instanceof Request) ||
    (typeof URL == 'function' && n instanceof URL)) &&
    (n = fetch(n)),
    Gs(e));
  let { instance: t, module: r } = await Ys(await n, e);
  return Ws(t, r);
}
var Yo = Ro;
var Uo = Ui(
  '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'
);
var vi = class {
  async init() {
    await Yo(Uo);
    let e = new Ht('<%', '%>', '\0', '*', '-', '_', 'tR');
    this.renderer = new on(e);
  }
  async parse_commands(e, t) {
    return this.renderer.render_content(e, t);
  }
};
var Xe;
(function (a) {
  ((a[(a.CreateNewFromTemplate = 0)] = 'CreateNewFromTemplate'),
    (a[(a.AppendActiveFile = 1)] = 'AppendActiveFile'),
    (a[(a.OverwriteFile = 2)] = 'OverwriteFile'),
    (a[(a.OverwriteActiveFile = 3)] = 'OverwriteActiveFile'),
    (a[(a.DynamicProcessor = 4)] = 'DynamicProcessor'),
    (a[(a.StartupTemplate = 5)] = 'StartupTemplate'));
})(Xe || (Xe = {}));
var kn = class {
  constructor(e) {
    this.plugin = e;
    ((this.functions_generator = new _i(this.plugin)),
      (this.parser = new vi()));
  }
  async setup() {
    ((this.files_with_pending_templates = new Set()),
      await this.parser.init(),
      await this.functions_generator.init(),
      this.plugin.registerMarkdownPostProcessor((e, t) =>
        this.process_dynamic_templates(e, t)
      ));
  }
  create_running_config(e, t, r) {
    let i = Jt(this.plugin.app);
    return { template_file: e, target_file: t, run_mode: r, active_file: i };
  }
  async read_and_parse_template(e) {
    let t = await this.plugin.app.vault.read(e.template_file);
    return this.parse_template(e, t);
  }
  async parse_template(e, t) {
    let r = await this.functions_generator.generate_object(e, Qe.USER_INTERNAL);
    return (
      (this.current_functions_object = r),
      await this.parser.parse_commands(t, r)
    );
  }
  start_templater_task(e) {
    this.files_with_pending_templates.add(e);
  }
  async end_templater_task(e) {
    (this.files_with_pending_templates.delete(e),
      this.files_with_pending_templates.size === 0 &&
        (this.plugin.app.workspace.trigger('templater:all-templates-executed'),
        await this.functions_generator.teardown()));
  }
  async create_new_note_from_template(e, t, r, i = !0) {
    if (!t)
      switch (this.plugin.app.vault.getConfig('newFileLocation')) {
        case 'current': {
          let y = Jt(this.plugin.app);
          y && (t = y.parent);
          break;
        }
        case 'folder':
          t = this.plugin.app.fileManager.getNewFileParent('');
          break;
        case 'root':
          t = this.plugin.app.vault.getRoot();
          break;
        default:
          break;
      }
    let o = (e instanceof Oe.TFile && e.extension) || 'md',
      a = await Te(async () => {
        let m = t instanceof Oe.TFolder ? t.path : t,
          y = this.plugin.app.vault.getAvailablePath(
            (0, Oe.normalizePath)(`${m ?? ''}/${r || 'Untitled'}`),
            o
          ),
          b = oo(y);
        return (
          b &&
            !this.plugin.app.vault.getAbstractFileByPathInsensitive(b) &&
            (await this.plugin.app.vault.createFolder(b)),
          this.plugin.app.vault.create(y, '')
        );
      }, `Couldn't create ${o} file.`);
    if (a == null) return;
    let { path: l } = a;
    this.start_templater_task(l);
    let c, d;
    if (
      (e instanceof Oe.TFile
        ? ((c = this.create_running_config(e, a, 0)),
          (d = await Te(
            async () => this.read_and_parse_template(c),
            'Template parsing error, aborting.'
          )))
        : ((c = this.create_running_config(void 0, a, 0)),
          (d = await Te(
            async () => this.parse_template(c, e),
            'Template parsing error, aborting.'
          ))),
      d == null)
    ) {
      (await this.plugin.app.vault.delete(a), await this.end_templater_task(l));
      return;
    }
    if (
      (await this.plugin.app.vault.modify(a, d),
      this.plugin.app.workspace.trigger('templater:new-note-from-template', {
        file: a,
        content: d,
      }),
      i)
    ) {
      let m = this.plugin.app.workspace.getLeaf(!1);
      if (!m) {
        oe(new O('No active leaf'));
        return;
      }
      (await m.openFile(a, { state: { mode: 'source' } }),
        await this.plugin.editor_handler.jump_to_next_cursor_location(a, !0),
        m.setEphemeralState({ rename: 'all' }));
    }
    return (await this.end_templater_task(l), a);
  }
  async append_template_to_active_file(e) {
    let t = this.plugin.app.workspace.getActiveViewOfType(Oe.MarkdownView),
      r = this.plugin.app.workspace.activeEditor;
    if (!r || !r.file || !r.editor) {
      oe(new O("No active editor, can't append templates."));
      return;
    }
    let { path: i } = r.file;
    this.start_templater_task(i);
    let o = this.create_running_config(e, r.file, 1),
      a = await Te(
        async () => this.read_and_parse_template(o),
        'Template parsing error, aborting.'
      );
    if (a == null) {
      await this.end_templater_task(i);
      return;
    }
    let c = r.editor.getDoc(),
      d = c.listSelections();
    (c.replaceSelection(a),
      r.file && (await this.plugin.app.vault.append(r.file, '')),
      this.plugin.app.workspace.trigger('templater:template-appended', {
        view: t,
        editor: r,
        content: a,
        oldSelections: d,
        newSelections: c.listSelections(),
      }),
      await this.plugin.editor_handler.jump_to_next_cursor_location(r.file, !0),
      await this.end_templater_task(i));
  }
  async write_template_to_file(e, t) {
    let { path: r } = t;
    this.start_templater_task(r);
    let i = this.plugin.app.workspace.activeEditor,
      o = Jt(this.plugin.app),
      a = this.create_running_config(e, t, 2),
      l = await Te(
        async () => this.read_and_parse_template(a),
        'Template parsing error, aborting.'
      );
    if (l == null) {
      await this.end_templater_task(r);
      return;
    }
    (await this.plugin.app.vault.modify(t, l),
      o?.path === t.path &&
        i &&
        i.editor &&
        i.editor.setSelection({ line: 0, ch: 0 }, { line: 0, ch: 0 }),
      this.plugin.app.workspace.trigger('templater:new-note-from-template', {
        file: t,
        content: l,
      }),
      await this.plugin.editor_handler.jump_to_next_cursor_location(t, !0),
      await this.end_templater_task(r));
  }
  overwrite_active_file_commands() {
    let e = this.plugin.app.workspace.activeEditor;
    if (!e || !e.file) {
      oe(new O("Active editor is null, can't overwrite content"));
      return;
    }
    this.overwrite_file_commands(e.file, !0);
  }
  async overwrite_file_commands(e, t = !1) {
    let { path: r } = e;
    this.start_templater_task(r);
    let i = this.create_running_config(e, e, t ? 3 : 2),
      o = await Te(
        async () => this.read_and_parse_template(i),
        'Template parsing error, aborting.'
      );
    if (o == null) {
      await this.end_templater_task(r);
      return;
    }
    (await this.plugin.app.vault.modify(e, o),
      this.plugin.app.workspace.trigger('templater:overwrite-file', {
        file: e,
        content: o,
      }),
      await this.plugin.editor_handler.jump_to_next_cursor_location(e, !0),
      await this.end_templater_task(r));
  }
  async process_dynamic_templates(e, t) {
    let r = ro(),
      i = document.createNodeIterator(e, NodeFilter.SHOW_TEXT),
      o,
      a = !1,
      l;
    for (; (o = i.nextNode()); ) {
      let c = o.nodeValue;
      if (c !== null) {
        let d = r.exec(c);
        if (d !== null) {
          let m = this.plugin.app.metadataCache.getFirstLinkpathDest(
            '',
            t.sourcePath
          );
          if (!m || !(m instanceof Oe.TFile)) return;
          if (!a) {
            a = !0;
            let y = this.create_running_config(m, m, 4);
            ((l = await this.functions_generator.generate_object(
              y,
              Qe.USER_INTERNAL
            )),
              (this.current_functions_object = l));
          }
        }
        for (; d != null; ) {
          let m = d[1] + d[2],
            y = await Te(
              async () => await this.parser.parse_commands(m, l),
              `Command Parsing error in dynamic command '${m}'`
            );
          if (y == null) return;
          let b = r.lastIndex - d[0].length,
            E = r.lastIndex;
          ((c = c.substring(0, b) + y + c.substring(E)),
            (r.lastIndex += y.length - d[0].length),
            (d = r.exec(c)));
        }
        o.nodeValue = c;
      }
    }
  }
  get_new_file_template_for_folder(e) {
    do {
      let t = this.plugin.settings.folder_templates.find(
        r => r.folder == e.path
      );
      if (t && t.template) return t.template;
      e = e.parent;
    } while (e);
  }
  get_new_file_template_for_file(e) {
    let t = this.plugin.settings.file_templates.find(r =>
      new RegExp(r.regex).test(e.path)
    );
    if (t && t.template) return t.template;
  }
  static async on_file_creation(e, t, r) {
    if (!(r instanceof Oe.TFile) || r.extension !== 'md') return;
    let i = (0, Oe.normalizePath)(e.plugin.settings.templates_folder);
    if (
      !(r.path.includes(i) && i !== '/') &&
      (await ar(300), !e.files_with_pending_templates.has(r.path))
    )
      if (r.stat.size == 0 && e.plugin.settings.enable_folder_templates) {
        let o = e.get_new_file_template_for_folder(r.parent);
        if (!o) return;
        let a = await Te(async () => Dt(t, o), `Couldn't find template ${o}`);
        if (a == null) return;
        await e.write_template_to_file(a, r);
      } else if (r.stat.size == 0 && e.plugin.settings.enable_file_templates) {
        let o = e.get_new_file_template_for_file(r);
        if (!o) return;
        let a = await Te(async () => Dt(t, o), `Couldn't find template ${o}`);
        if (a == null) return;
        await e.write_template_to_file(a, r);
      } else
        r.stat.size <= 1e5
          ? await e.overwrite_file_commands(r)
          : console.log(
              `Templater skipped parsing ${r.path} because file size exceeds 10000`
            );
  }
  async execute_startup_scripts() {
    for (let e of this.plugin.settings.startup_templates) {
      if (!e) continue;
      let t = ke(
        () => Dt(this.plugin.app, e),
        `Couldn't find startup template "${e}"`
      );
      if (!t) continue;
      let { path: r } = t;
      this.start_templater_task(r);
      let i = this.create_running_config(t, t, 5);
      (await Te(
        async () => this.read_and_parse_template(i),
        'Startup Template parsing error, aborting.'
      ),
        await this.end_templater_task(r));
    }
  }
};
var Go = X(require('obsidian')),
  xr = class {
    constructor(e, t, r) {
      this.plugin = e;
      this.templater = t;
      this.settings = r;
    }
    setup() {
      (Array.isArray(this.plugin.app.workspace.onLayoutReadyCallbacks)
        ? this.plugin.app.workspace.onLayoutReadyCallbacks.push({
            pluginId: this.plugin.manifest.id,
            callback: () => {
              this.update_trigger_file_on_creation();
            },
          })
        : this.plugin.app.workspace.onLayoutReady(() => {
            this.update_trigger_file_on_creation();
          }),
        this.update_syntax_highlighting(),
        this.update_file_menu());
    }
    update_syntax_highlighting() {
      let e = this.plugin.editor_handler.desktopShouldHighlight(),
        t = this.plugin.editor_handler.mobileShouldHighlight();
      e || t
        ? this.plugin.editor_handler.enable_highlighter()
        : this.plugin.editor_handler.disable_highlighter();
    }
    update_trigger_file_on_creation() {
      this.settings.trigger_on_file_creation
        ? ((this.trigger_on_file_creation_event = this.plugin.app.vault.on(
            'create',
            e => kn.on_file_creation(this.templater, this.plugin.app, e)
          )),
          this.plugin.registerEvent(this.trigger_on_file_creation_event))
        : this.trigger_on_file_creation_event &&
          (this.plugin.app.vault.offref(this.trigger_on_file_creation_event),
          (this.trigger_on_file_creation_event = void 0));
    }
    update_file_menu() {
      this.plugin.registerEvent(
        this.plugin.app.workspace.on('file-menu', (e, t) => {
          t instanceof Go.TFolder &&
            e.addItem(r => {
              r.setTitle('Create new note from template')
                .setIcon('templater-icon')
                .onClick(() => {
                  this.plugin.fuzzy_suggester.create_new_note_from_template(t);
                });
            });
        })
      );
    }
  };
var yr = X(require('obsidian'));
var wi = class {
  constructor(e) {
    this.plugin = e;
  }
  setup() {
    (this.plugin.addCommand({
      id: 'insert-templater',
      name: 'Open insert template modal',
      icon: 'templater-icon',
      hotkeys: yr.Platform.isMacOS
        ? void 0
        : [{ modifiers: ['Alt'], key: 'e' }],
      callback: () => {
        this.plugin.fuzzy_suggester.insert_template();
      },
    }),
      this.plugin.addCommand({
        id: 'replace-in-file-templater',
        name: 'Replace templates in the active file',
        icon: 'templater-icon',
        hotkeys: yr.Platform.isMacOS
          ? void 0
          : [{ modifiers: ['Alt'], key: 'r' }],
        callback: () => {
          this.plugin.templater.overwrite_active_file_commands();
        },
      }),
      this.plugin.addCommand({
        id: 'jump-to-next-cursor-location',
        name: 'Jump to next cursor location',
        icon: 'text-cursor',
        hotkeys: [{ modifiers: ['Alt'], key: 'Tab' }],
        callback: () => {
          this.plugin.editor_handler.jump_to_next_cursor_location();
        },
      }),
      this.plugin.addCommand({
        id: 'create-new-note-from-template',
        name: 'Create new note from template',
        icon: 'templater-icon',
        hotkeys: yr.Platform.isMacOS
          ? void 0
          : [{ modifiers: ['Alt'], key: 'n' }],
        callback: () => {
          this.plugin.fuzzy_suggester.create_new_note_from_template();
        },
      }),
      this.register_templates_hotkeys());
  }
  register_templates_hotkeys() {
    this.plugin.settings.enabled_templates_hotkeys.forEach(e => {
      e && this.add_template_hotkey(null, e);
    });
  }
  add_template_hotkey(e, t) {
    (this.remove_template_hotkey(e),
      t &&
        (this.plugin.addCommand({
          id: t,
          name: `Insert ${t}`,
          icon: 'templater-icon',
          callback: () => {
            let r = ke(
              () => Dt(this.plugin.app, t),
              "Couldn't find the template file associated with this hotkey"
            );
            !r || this.plugin.templater.append_template_to_active_file(r);
          },
        }),
        this.plugin.addCommand({
          id: `create-${t}`,
          name: `Create ${t}`,
          icon: 'templater-icon',
          callback: () => {
            let r = ke(
              () => Dt(this.plugin.app, t),
              "Couldn't find the template file associated with this hotkey"
            );
            !r || this.plugin.templater.create_new_note_from_template(r);
          },
        })));
  }
  remove_template_hotkey(e) {
    e &&
      (this.plugin.removeCommand(`${e}`),
      this.plugin.removeCommand(`create-${e}`));
  }
};
var Ci = X(require('obsidian'));
var bi = X(require('obsidian'));
var Ei = class {
  constructor(e) {
    this.app = e;
  }
  async jump_to_next_cursor_location() {
    let e = this.app.workspace.activeEditor;
    if (!e || !e.editor) return;
    let t = e.editor.getValue(),
      { new_content: r, positions: i } =
        this.replace_and_get_cursor_positions(t);
    if (i) {
      let o = e instanceof bi.MarkdownView ? e.currentMode.getFoldInfo() : null;
      (e.editor.setValue(r),
        o &&
          Array.isArray(o.folds) &&
          (i.forEach(a => {
            o.folds = o.folds.filter(l => l.from > a.line || l.to < a.line);
          }),
          e instanceof bi.MarkdownView && e.currentMode.applyFoldInfo(o)),
        this.set_cursor_location(i));
    }
    if (this.app.vault.getConfig('vimMode')) {
      let o = e.editor.cm.cm;
      window.CodeMirrorAdapter.Vim.handleKey(o, 'i', 'mapping');
    }
  }
  get_editor_position_from_index(e, t) {
    let r = e.slice(0, t),
      i = 0,
      o = -1,
      a = -1;
    for (
      ;
      (a = r.indexOf(
        `
`,
        a + 1
      )) !== -1;
      i++, o = a
    );
    o += 1;
    let l = e.slice(o, t).length;
    return { line: i, ch: l };
  }
  replace_and_get_cursor_positions(e) {
    let t = [],
      r,
      i = new RegExp('<%\\s*tp.file.cursor\\((?<order>[0-9]*)\\)\\s*%>', 'g');
    for (; (r = i.exec(e)) != null; ) t.push(r);
    if (t.length === 0) return {};
    t.sort(
      (c, d) =>
        Number(c.groups && c.groups.order) - Number(d.groups && d.groups.order)
    );
    let o = t[0][0];
    t = t.filter(c => c[0] === o);
    let a = [],
      l = 0;
    for (let c of t) {
      let d = c.index - l;
      if (
        (a.push(this.get_editor_position_from_index(e, d)),
        (e = e.replace(new RegExp(no(c[0])), '')),
        (l += c[0].length),
        c[1] === '')
      )
        break;
    }
    return { new_content: e, positions: a };
  }
  set_cursor_location(e) {
    let t = this.app.workspace.activeEditor;
    if (!t || !t.editor) return;
    let r = t.editor,
      i = [];
    for (let a of e) i.push({ from: a });
    let o = { selections: i };
    r.transaction(o);
  }
};
var Jo = X(require('obsidian'));
var Js = {
    app: {
      name: 'app',
      description:
        'This module exposes the app instance. Prefer to use this over the global app instance.',
    },
    user: {
      name: 'user',
      description:
        'This module exposes custom made scripts, written by yourself within the script file folder location',
    },
    config: {
      name: 'config',
      description: `This module exposes Templater's running configuration.

This is mostly useful when writing scripts requiring some context information.
`,
      functions: {
        template_file: {
          name: 'template_file',
          description: 'The `TFile` object representing the template file.',
          definition: 'tp.config.template_file',
        },
        target_file: {
          name: 'target_file',
          description:
            'The `TFile` object representing the target file where the template will be inserted.',
          definition: 'tp.config.target_file',
        },
        run_mode: {
          name: 'run_mode',
          description:
            'The `RunMode`, representing the way Templater was launched (Create new from template, Append to active file, ...).',
          definition: 'tp.config.run_mode',
        },
        active_file: {
          name: 'active_file',
          description:
            'The active file (if existing) when launching Templater.',
          definition: 'tp.config.active_file?',
        },
      },
    },
    date: {
      name: 'date',
      description:
        'This module contains every internal function related to dates.',
      functions: {
        now: {
          name: 'now',
          description: 'Retrieves the date.',
          definition:
            'tp.date.now(format: string = "YYYY-MM-DD", offset?: number\u23AEstring, reference?: string, reference_format?: string)',
          args: [
            {
              name: 'format',
              description:
                'The format for the date. Defaults to `"YYYY-MM-DD"`. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/).',
            },
            {
              name: 'offset',
              description:
                'Duration to offset the date from. If a number is provided, duration will be added to the date in days. You can also specify the offset as a string using the ISO 8601 format.',
            },
            {
              name: 'reference',
              description:
                "The date referential, e.g. set this to the note's title.",
            },
            {
              name: 'reference_format',
              description:
                'The format for the reference date. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/).',
            },
          ],
          examples: [
            { name: 'Date now', example: '<% tp.date.now() %>' },
            {
              name: 'Date now with format',
              example: '<% tp.date.now("Do MMMM YYYY") %>',
            },
            {
              name: 'Last week',
              example: '<% tp.date.now("YYYY-MM-DD", -7) %>',
            },
            {
              name: 'Next week',
              example: '<% tp.date.now("YYYY-MM-DD", 7) %>',
            },
            {
              name: 'Last month',
              example: '<% tp.date.now("YYYY-MM-DD", "P-1M") %>',
            },
            {
              name: 'Next year',
              example: '<% tp.date.now("YYYY-MM-DD", "P1Y") %>',
            },
            {
              name: "File's title date + 1 day (tomorrow)",
              example:
                '<% tp.date.now("YYYY-MM-DD", 1, tp.file.title, "YYYY-MM-DD") %>',
            },
            {
              name: "File's title date - 1 day (yesterday)",
              example:
                '<% tp.date.now("YYYY-MM-DD", -1, tp.file.title, "YYYY-MM-DD") %>',
            },
          ],
        },
        tomorrow: {
          name: 'tomorrow',
          description: "Retrieves tomorrow's date.",
          definition: 'tp.date.tomorrow(format: string = "YYYY-MM-DD")',
          args: [
            {
              name: 'format',
              description:
                'The format for the date. Defaults to `"YYYY-MM-DD"`. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/).',
            },
          ],
          examples: [
            { name: 'Date tomorrow', example: '<% tp.date.tomorrow() %>' },
            {
              name: 'Date tomorrow with format',
              example: '<% tp.date.tomorrow("Do MMMM YYYY") %>',
            },
          ],
        },
        yesterday: {
          name: 'yesterday',
          description: "Retrieves yesterday's date.",
          definition: 'tp.date.yesterday(format: string = "YYYY-MM-DD")',
          args: [
            {
              name: 'format',
              description:
                'The format for the date. Defaults to `"YYYY-MM-DD"`. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/).',
            },
          ],
          examples: [
            { name: 'Date yesterday', example: '<% tp.date.yesterday() %>' },
            {
              name: 'Date yesterday with format',
              example: '<% tp.date.yesterday("Do MMMM YYYY") %>',
            },
          ],
        },
        weekday: {
          name: 'weekday',
          description: '',
          definition:
            'tp.date.weekday(format: string = "YYYY-MM-DD", weekday: number, reference?: string, reference_format?: string)',
          args: [
            {
              name: 'format',
              description:
                'The format for the date. Defaults to `"YYYY-MM-DD"`. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/).',
            },
            {
              name: 'weekday',
              description:
                "Week day number. If the locale assigns Monday as the first day of the week, `0` will be Monday, `-7` will be last week's day.",
            },
            {
              name: 'reference',
              description:
                "The date referential, e.g. set this to the note's title.",
            },
            {
              name: 'reference_format',
              description:
                'The format for the reference date. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/).',
            },
          ],
          examples: [
            {
              name: "This week's Monday",
              example: '<% tp.date.weekday("YYYY-MM-DD", 0) %>',
            },
            {
              name: 'Next Monday',
              example: '<% tp.date.weekday("YYYY-MM-DD", 7) %>',
            },
            {
              name: "File's title Monday",
              example:
                '<% tp.date.weekday("YYYY-MM-DD", 0, tp.file.title, "YYYY-MM-DD") %>',
            },
            {
              name: "File's title previous Monday",
              example:
                '<% tp.date.weekday("YYYY-MM-DD", -7, tp.file.title, "YYYY-MM-DD") %>',
            },
          ],
        },
      },
      momentjs: {
        examples: [
          {
            name: 'Date now',
            example:
              '<% moment(tp.file.title, "YYYY-MM-DD").format("YYYY-MM-DD") %>',
          },
          {
            name: 'Get start of month from note title',
            example:
              '<% moment(tp.file.title, "YYYY-MM-DD").startOf("month").format("YYYY-MM-DD") %>',
          },
          {
            name: 'Get end of month from note title',
            example:
              '<% moment(tp.file.title, "YYYY-MM-DD").endOf("month").format("YYYY-MM-DD") %>',
          },
        ],
      },
    },
    file: {
      name: 'file',
      description:
        'This module contains every internal function related to files.',
      functions: {
        content: {
          name: 'content',
          description:
            'The string contents of the file at the time that Templater was executed. Manipulating this string will *not* update the current file.',
          definition: 'tp.file.content',
          examples: [
            { name: 'Retrieve file content', example: '<% tp.file.content %>' },
          ],
        },
        create_new: {
          name: 'create_new',
          description:
            'Creates a new file using a specified template or with a specified content.',
          definition:
            'tp.file.create_new(template: TFile \u23AE string, filename?: string, open_new: boolean = false, folder?: TFolder | string)',
          args: [
            {
              name: 'template',
              description:
                'Either the template used for the new file content, or the file content as a string. If it is the template to use, you retrieve it with `tp.file.find_tfile(TEMPLATENAME)`.',
            },
            {
              name: 'filename',
              description:
                'The filename of the new file, defaults to "Untitled".',
            },
            {
              name: 'open_new',
              description:
                'Whether to open or not the newly created file. Warning: if you use this option, since commands are executed asynchronously, the file can be opened first and then other commands are appended to that new file and not the previous file.',
            },
            {
              name: 'folder',
              description:
                'The folder to put the new file in, defaults to Obsidian\'s default location. If you want the file to appear in a different folder, specify it with `"PATH/TO/FOLDERNAME"` or `app.vault.getAbstractFileByPath("PATH/TO/FOLDERNAME")`.',
            },
          ],
          examples: [
            {
              name: 'File creation',
              example:
                '<%* await tp.file.create_new("MyFileContent", "MyFilename") %>',
            },
            {
              name: 'File creation with template',
              example:
                '<%* await tp.file.create_new(tp.file.find_tfile("MyTemplate"), "MyFilename") %>',
            },
            {
              name: 'File creation and open created note',
              example:
                '<%* await tp.file.create_new("MyFileContent", "MyFilename", true) %>',
            },
            {
              name: 'File creation in current folder',
              example:
                '<%* await tp.file.create_new("MyFileContent", "MyFilename", false, tp.file.folder(true)) %>',
            },
            {
              name: 'File creation in specified folder with string path',
              example:
                '<%* await tp.file.create_new("MyFileContent", "MyFilename", false, "Path/To/MyFolder") %>',
            },
            {
              name: 'File creation in specified folder with TFolder',
              example:
                '<%* await tp.file.create_new("MyFileContent", "MyFilename", false, app.vault.getAbstractFileByPath("MyFolder")) %>',
            },
            {
              name: 'File creation and append link to current note',
              example:
                '[[<% (await tp.file.create_new("MyFileContent", "MyFilename")).basename %>]]',
            },
          ],
        },
        creation_date: {
          name: 'creation_date',
          description: "Retrieves the file's creation date.",
          definition:
            'tp.file.creation_date(format: string = "YYYY-MM-DD HH:mm")',
          args: [
            {
              name: 'format',
              description:
                'The format for the date. Defaults to `"YYYY-MM-DD HH:mm"`. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/).',
            },
          ],
          examples: [
            {
              name: 'File creation date',
              example: '<% tp.file.creation_date() %>',
            },
            {
              name: 'File creation date with format',
              example: '<% tp.file.creation_date("dddd Do MMMM YYYY HH:mm") %>',
            },
          ],
        },
        cursor: {
          name: 'cursor',
          description: `Sets the cursor to this location after the template has been inserted. 

You can navigate between the different cursors using the configured hotkey in Obsidian settings.
`,
          definition: 'tp.file.cursor(order?: number)',
          args: [
            {
              name: 'order',
              description: `The order of the different cursors jump, e.g. it will jump from 1 to 2 to 3, and so on.
If you specify multiple tp.file.cursor with the same order, the editor will switch to multi-cursor.
`,
            },
          ],
          examples: [
            { name: 'File cursor', example: '<% tp.file.cursor() %>' },
            {
              name: 'File multi-cursor',
              example: '<% tp.file.cursor(1) %>Content<% tp.file.cursor(1) %>',
            },
          ],
        },
        cursor_append: {
          name: 'cursor_append',
          description:
            'Appends some content after the active cursor in the file.',
          definition: 'tp.file.cursor_append(content: string)',
          args: [
            {
              name: 'content',
              description: 'The content to append after the active cursor.',
            },
          ],
          examples: [
            {
              name: 'File cursor append',
              example: '<% tp.file.cursor_append("Some text") %>',
            },
          ],
        },
        exists: {
          name: 'exists',
          description:
            "Check to see if a file exists by it's file path. The full path to the file, relative to the Vault and containing the extension, must be provided.",
          definition: 'tp.file.exists(filepath: string)',
          args: [
            {
              name: 'filepath',
              description:
                'The full file path of the file we want to check existence for.',
            },
          ],
          examples: [
            {
              name: 'File existence',
              example: '<% await tp.file.exists("MyFolder/MyFile.md") %>',
            },
            {
              name: 'File existence of current file',
              example:
                '<% await tp.file.exists(tp.file.folder(true) + "/" + tp.file.title + ".md") %>',
            },
          ],
        },
        find_tfile: {
          name: 'find_tfile',
          description: 'Search for a file and returns its `TFile` instance.',
          definition: 'tp.file.find_tfile(filename: string)',
          args: [
            {
              name: 'filename',
              description:
                'The filename we want to search and resolve as a `TFile`.',
            },
          ],
          examples: [
            {
              name: 'File find TFile',
              example: '<% tp.file.find_tfile("MyFile").basename %>',
            },
          ],
        },
        folder: {
          name: 'folder',
          description: "Retrieves the file's folder name.",
          definition: 'tp.file.folder(absolute: boolean = false)',
          args: [
            {
              name: 'absolute',
              description:
                'If set to `true`, returns the vault-absolute path of the folder. If `false`, only returns the basename of the folder (the last part). Defaults to `false`.',
            },
          ],
          examples: [
            { name: 'File folder (Folder)', example: '<% tp.file.folder() %>' },
            {
              name: 'File folder with vault-absolute path (Path/To/Folder)',
              example: '<% tp.file.folder(true) %>',
            },
          ],
        },
        include: {
          name: 'include',
          description:
            "Includes the file's link content. Templates in the included content will be resolved.",
          definition: 'tp.file.include(include_link: string \u23AE TFile)',
          args: [
            {
              name: 'include_link',
              description:
                'The link to the file to include, e.g. `"[[MyFile]]"`, or a TFile object. Also supports sections or blocks inclusions.',
            },
          ],
          examples: [
            {
              name: 'File include',
              example: '<% tp.file.include("[[Template1]]") %>',
            },
            {
              name: 'File include TFile',
              example: '<% tp.file.include(tp.file.find_tfile("MyFile")) %>',
            },
            {
              name: 'File include section',
              example: '<% tp.file.include("[[MyFile#Section1]]") %>',
            },
            {
              name: 'File include block',
              example: '<% tp.file.include("[[MyFile#^block1]]") %>',
            },
          ],
        },
        last_modified_date: {
          name: 'last_modified_date',
          description: "Retrieves the file's last modification date.",
          definition:
            'tp.file.last_modified_date(format: string = "YYYY-MM-DD HH:mm")',
          args: [
            {
              name: 'format',
              description:
                'The format for the date. Defaults to `"YYYY-MM-DD HH:mm"`. Refer to [format reference](https://momentjs.com/docs/#/displaying/format/).',
            },
          ],
          examples: [
            {
              name: 'File last modified date',
              example: '<% tp.file.last_modified_date() %>',
            },
            {
              name: 'File last modified date with format',
              example:
                '<% tp.file.last_modified_date("dddd Do MMMM YYYY HH:mm") %>',
            },
          ],
        },
        move: {
          name: 'move',
          description: 'Moves the file to the desired vault location.',
          definition: 'tp.file.move(new_path: string, file_to_move?: TFile)',
          args: [
            {
              name: 'new_path',
              description:
                'The new vault relative path of the file, without the file extension. Note: the new path needs to include the folder and the filename, e.g. `"/Notes/MyNote"`.',
            },
            {
              name: 'file_to_move',
              description: 'The file to move, defaults to the current file.',
            },
          ],
          examples: [
            {
              name: 'File move',
              example: '<% await tp.file.move("/A/B/" + tp.file.title) %>',
            },
            {
              name: 'File move and rename',
              example: '<% await tp.file.move("/A/B/NewTitle") %>',
            },
          ],
        },
        path: {
          name: 'path',
          description: "Retrieves the file's absolute path on the system.",
          definition: 'tp.file.path(relative: boolean = false)',
          args: [
            {
              name: 'relative',
              description:
                "If set to `true`, only retrieves the vault's relative path.",
            },
          ],
          examples: [
            { name: 'File path', example: '<% tp.file.path() %>' },
            {
              name: 'File relative path (relative to vault root)',
              example: '<% tp.file.path(true) %>',
            },
          ],
        },
        rename: {
          name: 'rename',
          description: 'Renames the file (keeps the same file extension).',
          definition: 'tp.file.rename(new_title: string)',
          args: [{ name: 'new_title', description: 'The new file title.' }],
          examples: [
            {
              name: 'File rename',
              example: '<% await tp.file.rename("MyNewName") %>',
            },
            {
              name: 'File append a 2 to the file name',
              example: '<% await tp.file.rename(tp.file.title + "2") %>',
            },
          ],
        },
        selection: {
          name: 'selection',
          description: "Retrieves the active file's text selection.",
          definition: 'tp.file.selection()',
          examples: [
            { name: 'File selection', example: '<% tp.file.selection() %>' },
          ],
        },
        tags: {
          name: 'tags',
          description: "Retrieves the file's tags (array of string).",
          definition: 'tp.file.tags',
          examples: [{ name: 'File tags', example: '<% tp.file.tags %>' }],
        },
        title: {
          name: 'title',
          definition: 'tp.file.title',
          description: "Retrieves the file's title.",
          examples: [
            { name: 'File title', example: '<% tp.file.title %>' },
            {
              name: 'Strip the Zettelkasten ID of title (if space separated)',
              example: '<% tp.file.title.split(" ")[1] %>',
            },
          ],
        },
      },
    },
    frontmatter: {
      name: 'frontmatter',
      description:
        'This modules exposes all the frontmatter variables of a file as variables.',
    },
    hooks: {
      name: 'hooks',
      description:
        'This module exposes hooks that allow you to execute code when a Templater event occurs.',
      functions: {
        on_all_templates_executed: {
          name: 'on_all_templates_executed',
          description:
            'Hooks into when all actively running templates have finished executing. Most of the time this will be a single template, unless you are using `tp.file.include` or `tp.file.create_new`.\n\nMultiple invokations of this method will have their callback functions run in parallel.',
          definition:
            'tp.hooks.on_all_templates_executed(callback_function: () => any)',
          args: [
            {
              name: 'callback_function',
              description:
                'Callback function that will be executed when all actively running templates have finished executing.',
            },
          ],
        },
      },
    },
    obsidian: {
      name: 'obsidian',
      description:
        'This module exposes all the functions and classes from the Obsidian API.',
    },
    system: {
      name: 'system',
      description: 'This module contains system related functions.',
      functions: {
        clipboard: {
          name: 'clipboard',
          description: "Retrieves the clipboard's content.",
          definition: 'tp.system.clipboard()',
          examples: [
            { name: 'Clipboard', example: '<% tp.system.clipboard() %>' },
          ],
        },
        prompt: {
          name: 'prompt',
          description: "Spawns a prompt modal and returns the user's input.",
          definition:
            'tp.system.prompt(prompt_text?: string, default_value?: string, throw_on_cancel: boolean = false, multiline?: boolean = false)',
          args: [
            {
              name: 'prompt_text',
              description: 'Text placed above the input field.',
            },
            {
              name: 'default_value',
              description: 'A default value for the input field.',
            },
            {
              name: 'throw_on_cancel',
              description:
                'Throws an error if the prompt is canceled, instead of returning a `null` value.',
            },
            {
              name: 'multiline',
              description:
                'If set to `true`, the input field will be a multiline textarea. Defaults to `false`.',
            },
          ],
          examples: [
            {
              name: 'Prompt',
              example: '<% tp.system.prompt("Please enter a value") %>',
            },
            {
              name: 'Prompt with default value',
              example:
                '<% tp.system.prompt("What is your mood today?", "happy") %>',
            },
            {
              name: 'Multiline prompt',
              example:
                '<% tp.system.prompt("What is your mood today?", null, false, true) %>',
            },
          ],
        },
        suggester: {
          name: 'suggester',
          description:
            "Spawns a suggester prompt and returns the user's chosen item.",
          definition:
            'tp.system.suggester(text_items: string[] \u23AE ((item: T) => string), items: T[], throw_on_cancel: boolean = false, placeholder: string = "", limit?: number = undefined)',
          args: [
            {
              name: 'text_items',
              description:
                'Array of strings representing the text that will be displayed for each item in the suggester prompt. This can also be a function that maps an item to its text representation.',
            },
            {
              name: 'items',
              description:
                'Array containing the values of each item in the correct order.',
            },
            {
              name: 'throw_on_cancel',
              description:
                'Throws an error if the prompt is canceled, instead of returning a `null` value.',
            },
            {
              name: 'placeholder',
              description: 'Placeholder string of the prompt.',
            },
            {
              name: 'limit',
              description:
                'Limit the number of items rendered at once (useful to improve performance when displaying large lists).',
            },
          ],
          examples: [
            {
              name: 'Suggester',
              example:
                '<% tp.system.suggester(["Happy", "Sad", "Confused"], ["Happy", "Sad", "Confused"]) %>',
            },
            {
              name: 'Suggester with mapping function (same as above example)',
              example:
                '<% tp.system.suggester((item) => item, ["Happy", "Sad", "Confused"]) %>',
            },
            {
              name: 'Suggester for files',
              example:
                '[[<% (await tp.system.suggester((item) => item.basename, app.vault.getMarkdownFiles())).basename %>]]',
            },
            {
              name: 'Suggester for tags',
              example:
                '<% tp.system.suggester(item => item, Object.keys(app.metadataCache.getTags()).map(x => x.replace("#", ""))) %>',
            },
          ],
        },
      },
    },
    web: {
      name: 'web',
      description:
        'This modules contains every internal function related to the web (making web requests).',
      functions: {
        daily_quote: {
          name: 'daily_quote',
          description:
            'Retrieves and parses the daily quote from `https://github.com/Zachatoo/quotes-database` as a callout.',
          definition: 'tp.web.daily_quote()',
          examples: [
            { name: 'Daily quote', example: '<% tp.web.daily_quote() %>' },
          ],
        },
        random_picture: {
          name: 'random_picture',
          description: 'Gets a random image from `https://unsplash.com/`.',
          definition:
            'tp.web.random_picture(size?: string, query?: string, include_size?: boolean)',
          args: [
            {
              name: 'size',
              description: 'Image size in the format `<width>x<height>`.',
            },
            {
              name: 'query',
              description:
                'Limits selection to photos matching a search term. Multiple search terms can be passed separated by a comma.',
            },
            {
              name: 'include_size',
              description:
                'Optional argument to include the specified size in the image link markdown. Defaults to false.',
            },
          ],
          examples: [
            {
              name: 'Random picture',
              example: '<% tp.web.random_picture() %>',
            },
            {
              name: 'Random picture with size',
              example: '<% tp.web.random_picture("200x200") %>',
            },
            {
              name: 'Random picture with size and query',
              example:
                '<% tp.web.random_picture("200x200", "landscape,water") %>',
            },
          ],
        },
        request: {
          name: 'request',
          description:
            'Makes a HTTP request to the specified URL. Optionally, you can specify a path to extract specific data from the response.',
          definition: 'tp.web.request(url: string, path?: string)',
          args: [
            {
              name: 'url',
              description: 'The URL to which the HTTP request will be made.',
            },
            {
              name: 'path',
              description:
                'A path within the response JSON to extract specific data.',
            },
          ],
          examples: [
            {
              name: 'Simple request',
              example:
                '<% tp.web.request("https://jsonplaceholder.typicode.com/todos/1") %>',
            },
            {
              name: 'Request with path',
              example:
                '<% tp.web.request("https://jsonplaceholder.typicode.com/todos", "0.title") %>',
            },
          ],
        },
      },
    },
  },
  Vo = { tp: Js };
var Qs = [
    'app',
    'config',
    'date',
    'file',
    'frontmatter',
    'hooks',
    'obsidian',
    'system',
    'user',
    'web',
  ],
  Xs = new Set(Qs);
function zo(n) {
  return typeof n == 'string' && Xs.has(n);
}
function Ti(n) {
  return !!(n.definition || n.returns || n.args);
}
var ki = class {
  constructor(e) {
    this.plugin = e;
    this.documentation = Vo;
  }
  get_all_modules_documentation() {
    let e = this.documentation.tp;
    return (
      (!this.plugin.settings || !this.plugin.settings.user_scripts_folder) &&
        (e = Object.values(e).filter(t => t.name !== 'user')),
      Object.values(e).map(t => ((t.queryKey = t.name), t))
    );
  }
  async get_all_functions_documentation(e, t) {
    if (e === 'app')
      return this.get_app_functions_documentation(this.plugin.app, t);
    if (e === 'user') {
      if (!this.plugin.settings || !this.plugin.settings.user_scripts_folder)
        return;
      let r = await Te(async () => {
        let i = ze(
          this.plugin.app,
          this.plugin.settings.user_scripts_folder
        ).filter(a => a.extension == 'js');
        return await io(this.plugin.app, i);
      }, "User Scripts folder doesn't exist");
      return !r || r.length === 0
        ? void 0
        : r.reduce(
            (i, o) =>
              o.extension !== 'js'
                ? i
                : [
                    ...i,
                    {
                      name: o.basename,
                      queryKey: o.basename,
                      definition: '',
                      description: o.description,
                      returns: o.returns,
                      args: o.arguments.reduce(
                        (l, c) => (
                          (l[c.name] = {
                            name: c.name,
                            description: c.description,
                          }),
                          l
                        ),
                        {}
                      ),
                      example: '',
                    },
                  ],
            []
          );
    }
    if (!!this.documentation.tp[e].functions)
      return Object.values(this.documentation.tp[e].functions).map(
        r => ((r.queryKey = r.name), r)
      );
  }
  get_app_functions_documentation(e, t) {
    if (!$r(e)) return [];
    let r = t.split('.');
    if (r.length === 0) return [];
    let i = e;
    for (let c = 0; c < r.length - 1; c++) {
      let d = r[c];
      if (d in i) {
        if (!$r(i[d])) return [];
        i = i[d];
      }
    }
    let o = ['tp', 'app', ...r.slice(0, r.length - 1)].join('.'),
      a = r.slice(0, r.length - 1).join('.'),
      l = [];
    for (let c in i) {
      let d = `${o}.${c}`,
        m = a ? `${a}.${c}` : c;
      l.push({
        name: c,
        queryKey: m,
        definition: typeof i[c] == 'function' ? `${d}(${ao(i[c])})` : d,
        description: '',
        returns: '',
        example: '',
      });
    }
    return l;
  }
  get_module_documentation(e) {
    return this.documentation.tp[e];
  }
  get_function_documentation(e, t) {
    return this.documentation.tp[e].functions[t];
  }
  get_argument_documentation(e, t, r) {
    let i = this.get_function_documentation(e, t);
    return !i || !i.args ? null : i.args[r];
  }
};
var Si = class extends Jo.EditorSuggest {
  constructor(e) {
    super(e.app);
    this.tp_keyword_regex =
      /tp\.(?<module>[a-z]*)?(?<fn_trigger>\.(?<fn>[a-zA-Z_.]*)?)?$/;
    ((this.documentation = new ki(e)),
      (this.intellisense_render_setting = e.settings.intellisense_render));
  }
  onTrigger(e, t, r) {
    let i = t.getRange({ line: e.line, ch: 0 }, { line: e.line, ch: e.ch }),
      o = this.tp_keyword_regex.exec(i);
    if (!o) return null;
    let a,
      l = (o.groups && o.groups.module) || '';
    if (((this.module_name = l), o.groups && o.groups.fn_trigger)) {
      if (l == '' || !zo(l)) return null;
      ((this.function_trigger = !0),
        (this.function_name = o.groups.fn || ''),
        (a = this.function_name));
    } else ((this.function_trigger = !1), (a = this.module_name));
    let c = {
      start: { line: e.line, ch: e.ch - a.length },
      end: { line: e.line, ch: e.ch },
      query: a,
    };
    return ((this.latest_trigger_info = c), c);
  }
  async getSuggestions(e) {
    let t;
    return (
      this.module_name && this.function_trigger
        ? (t = await this.documentation.get_all_functions_documentation(
            this.module_name,
            this.function_name
          ))
        : (t = this.documentation.get_all_modules_documentation()),
      t
        ? t.filter(r =>
            r.queryKey.toLowerCase().startsWith(e.query.toLowerCase())
          )
        : []
    );
  }
  renderSuggestion(e, t) {
    if ((t.createEl('b', { text: e.name }), Ti(e))) {
      if (
        e.args &&
        this.getNumberOfArguments(e.args) > 0 &&
        Mo(this.intellisense_render_setting)
      ) {
        t.createEl('p', { text: 'Parameter list:' });
        let r = t.createEl('ol');
        for (let [i, o] of Object.entries(e.args)) Kr(r, i, o.description);
      }
      e.returns &&
        Oo(this.intellisense_render_setting) &&
        Kr(t, 'Returns', e.returns);
    }
    (this.function_trigger &&
      Ti(e) &&
      t.createEl('code', { text: e.definition }),
      e.description &&
        Bo(this.intellisense_render_setting) &&
        t.createEl('div', { text: e.description }));
  }
  selectSuggestion(e, t) {
    let r = this.app.workspace.activeEditor;
    if (
      !(!r || !r.editor) &&
      (r.editor.replaceRange(
        e.queryKey,
        this.latest_trigger_info.start,
        this.latest_trigger_info.end
      ),
      this.latest_trigger_info.start.ch == this.latest_trigger_info.end.ch)
    ) {
      let i = this.latest_trigger_info.end;
      ((i.ch += e.queryKey.length), r.editor.setCursor(i));
    }
  }
  getNumberOfArguments(e) {
    try {
      return new Map(Object.entries(e)).size;
    } catch {
      return 0;
    }
  }
  updateAutocompleteIntellisenseSetting(e) {
    this.intellisense_render_setting = e;
  }
};
(function (n) {
  n(window.CodeMirror);
})(function (n) {
  'use strict';
  (n.defineMode('javascript', function (e, t) {
    var r = e.indentUnit,
      i = t.statementIndent,
      o = t.jsonld,
      a = t.json || o,
      l = t.trackScope !== !1,
      c = t.typescript,
      d = t.wordCharacters || /[\w$\xa1-\uffff]/,
      m = (function () {
        function s(je) {
          return { type: je, style: 'keyword' };
        }
        var p = s('keyword a'),
          A = s('keyword b'),
          _ = s('keyword c'),
          F = s('keyword d'),
          Y = s('operator'),
          z = { type: 'atom', style: 'atom' };
        return {
          if: s('if'),
          while: p,
          with: p,
          else: A,
          do: A,
          try: A,
          finally: A,
          return: F,
          break: F,
          continue: F,
          new: s('new'),
          delete: _,
          void: _,
          throw: _,
          debugger: s('debugger'),
          var: s('var'),
          const: s('var'),
          let: s('var'),
          function: s('function'),
          catch: s('catch'),
          for: s('for'),
          switch: s('switch'),
          case: s('case'),
          default: s('default'),
          in: Y,
          typeof: Y,
          instanceof: Y,
          true: z,
          false: z,
          null: z,
          undefined: z,
          NaN: z,
          Infinity: z,
          this: s('this'),
          class: s('class'),
          super: s('atom'),
          yield: _,
          export: s('export'),
          import: s('import'),
          extends: _,
          await: _,
        };
      })(),
      y = /[+\-*&%=<>!?|~^@]/,
      b =
        /^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;
    function E(s) {
      for (var p = !1, A, _ = !1; (A = s.next()) != null; ) {
        if (!p) {
          if (A == '/' && !_) return;
          A == '[' ? (_ = !0) : _ && A == ']' && (_ = !1);
        }
        p = !p && A == '\\';
      }
    }
    var P, k;
    function w(s, p, A) {
      return ((P = s), (k = A), p);
    }
    function M(s, p) {
      var A = s.next();
      if (A == '"' || A == "'") return ((p.tokenize = $(A)), p.tokenize(s, p));
      if (A == '.' && s.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))
        return w('number', 'number');
      if (A == '.' && s.match('..')) return w('spread', 'meta');
      if (/[\[\]{}\(\),;\:\.]/.test(A)) return w(A);
      if (A == '=' && s.eat('>')) return w('=>', 'operator');
      if (A == '0' && s.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))
        return w('number', 'number');
      if (/\d/.test(A))
        return (
          s.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),
          w('number', 'number')
        );
      if (A == '/')
        return s.eat('*')
          ? ((p.tokenize = K), K(s, p))
          : s.eat('/')
            ? (s.skipToEnd(), w('comment', 'comment'))
            : Ri(s, p, 1)
              ? (E(s),
                s.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),
                w('regexp', 'string-2'))
              : (s.eat('='), w('operator', 'operator', s.current()));
      if (A == '`') return ((p.tokenize = C), C(s, p));
      if (A == '#' && s.peek() == '!')
        return (s.skipToEnd(), w('meta', 'meta'));
      if (A == '#' && s.eatWhile(d)) return w('variable', 'property');
      if (
        (A == '<' && s.match('!--')) ||
        (A == '-' && s.match('->') && !/\S/.test(s.string.slice(0, s.start)))
      )
        return (s.skipToEnd(), w('comment', 'comment'));
      if (y.test(A))
        return (
          (A != '>' || !p.lexical || p.lexical.type != '>') &&
            (s.eat('=')
              ? (A == '!' || A == '=') && s.eat('=')
              : /[<>*+\-|&?]/.test(A) && (s.eat(A), A == '>' && s.eat(A))),
          A == '?' && s.eat('.')
            ? w('.')
            : w('operator', 'operator', s.current())
        );
      if (d.test(A)) {
        s.eatWhile(d);
        var _ = s.current();
        if (p.lastType != '.') {
          if (m.propertyIsEnumerable(_)) {
            var F = m[_];
            return w(F.type, F.style, _);
          }
          if (
            _ == 'async' &&
            s.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/, !1)
          )
            return w('async', 'keyword', _);
        }
        return w('variable', 'variable', _);
      }
    }
    function $(s) {
      return function (p, A) {
        var _ = !1,
          F;
        if (o && p.peek() == '@' && p.match(b))
          return ((A.tokenize = M), w('jsonld-keyword', 'meta'));
        for (; (F = p.next()) != null && !(F == s && !_); ) _ = !_ && F == '\\';
        return (_ || (A.tokenize = M), w('string', 'string'));
      };
    }
    function K(s, p) {
      for (var A = !1, _; (_ = s.next()); ) {
        if (_ == '/' && A) {
          p.tokenize = M;
          break;
        }
        A = _ == '*';
      }
      return w('comment', 'comment');
    }
    function C(s, p) {
      for (var A = !1, _; (_ = s.next()) != null; ) {
        if (!A && (_ == '`' || (_ == '$' && s.eat('{')))) {
          p.tokenize = M;
          break;
        }
        A = !A && _ == '\\';
      }
      return w('quasi', 'string-2', s.current());
    }
    var H = '([{}])';
    function I(s, p) {
      p.fatArrowAt && (p.fatArrowAt = null);
      var A = s.string.indexOf('=>', s.start);
      if (!(A < 0)) {
        if (c) {
          var _ = /:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(
            s.string.slice(s.start, A)
          );
          _ && (A = _.index);
        }
        for (var F = 0, Y = !1, z = A - 1; z >= 0; --z) {
          var je = s.string.charAt(z),
            Ue = H.indexOf(je);
          if (Ue >= 0 && Ue < 3) {
            if (!F) {
              ++z;
              break;
            }
            if (--F == 0) {
              je == '(' && (Y = !0);
              break;
            }
          } else if (Ue >= 3 && Ue < 6) ++F;
          else if (d.test(je)) Y = !0;
          else if (/["'\/`]/.test(je))
            for (; ; --z) {
              if (z == 0) return;
              var ha = s.string.charAt(z - 1);
              if (ha == je && s.string.charAt(z - 2) != '\\') {
                z--;
                break;
              }
            }
          else if (Y && !F) {
            ++z;
            break;
          }
        }
        Y && !F && (p.fatArrowAt = z);
      }
    }
    var J = {
      atom: !0,
      number: !0,
      variable: !0,
      string: !0,
      regexp: !0,
      this: !0,
      import: !0,
      'jsonld-keyword': !0,
    };
    function te(s, p, A, _, F, Y) {
      ((this.indented = s),
        (this.column = p),
        (this.type = A),
        (this.prev = F),
        (this.info = Y),
        _ != null && (this.align = _));
    }
    function ne(s, p) {
      if (!l) return !1;
      for (var A = s.localVars; A; A = A.next) if (A.name == p) return !0;
      for (var _ = s.context; _; _ = _.prev)
        for (var A = _.vars; A; A = A.next) if (A.name == p) return !0;
    }
    function Q(s, p, A, _, F) {
      var Y = s.cc;
      for (
        h.state = s,
          h.stream = F,
          h.marked = null,
          h.cc = Y,
          h.style = p,
          s.lexical.hasOwnProperty('align') || (s.lexical.align = !0);
        ;

      ) {
        var z = Y.length ? Y.pop() : a ? W : ee;
        if (z(A, _)) {
          for (; Y.length && Y[Y.length - 1].lex; ) Y.pop()();
          return h.marked
            ? h.marked
            : A == 'variable' && ne(s, _)
              ? 'variable-2'
              : p;
        }
      }
    }
    var h = { state: null, column: null, marked: null, cc: null };
    function S() {
      for (var s = arguments.length - 1; s >= 0; s--) h.cc.push(arguments[s]);
    }
    function f() {
      return (S.apply(null, arguments), !0);
    }
    function Me(s, p) {
      for (var A = p; A; A = A.next) if (A.name == s) return !0;
      return !1;
    }
    function be(s) {
      var p = h.state;
      if (((h.marked = 'def'), !!l)) {
        if (p.context) {
          if (p.lexical.info == 'var' && p.context && p.context.block) {
            var A = Ae(s, p.context);
            if (A != null) {
              p.context = A;
              return;
            }
          } else if (!Me(s, p.localVars)) {
            p.localVars = new Ee(s, p.localVars);
            return;
          }
        }
        t.globalVars &&
          !Me(s, p.globalVars) &&
          (p.globalVars = new Ee(s, p.globalVars));
      }
    }
    function Ae(s, p) {
      if (p)
        if (p.block) {
          var A = Ae(s, p.prev);
          return A ? (A == p.prev ? p : new Ke(A, p.vars, !0)) : null;
        } else return Me(s, p.vars) ? p : new Ke(p.prev, new Ee(s, p.vars), !1);
      else return null;
    }
    function _e(s) {
      return (
        s == 'public' ||
        s == 'private' ||
        s == 'protected' ||
        s == 'abstract' ||
        s == 'readonly'
      );
    }
    function Ke(s, p, A) {
      ((this.prev = s), (this.vars = p), (this.block = A));
    }
    function Ee(s, p) {
      ((this.name = s), (this.next = p));
    }
    var $t = new Ee('this', new Ee('arguments', null));
    function Re() {
      ((h.state.context = new Ke(h.state.context, h.state.localVars, !1)),
        (h.state.localVars = $t));
    }
    function Ze() {
      ((h.state.context = new Ke(h.state.context, h.state.localVars, !0)),
        (h.state.localVars = null));
    }
    function xe() {
      ((h.state.localVars = h.state.context.vars),
        (h.state.context = h.state.context.prev));
    }
    xe.lex = !0;
    function B(s, p) {
      var A = function () {
        var _ = h.state,
          F = _.indented;
        if (_.lexical.type == 'stat') F = _.lexical.indented;
        else
          for (var Y = _.lexical; Y && Y.type == ')' && Y.align; Y = Y.prev)
            F = Y.indented;
        _.lexical = new te(F, h.stream.column(), s, null, _.lexical, p);
      };
      return ((A.lex = !0), A);
    }
    function D() {
      var s = h.state;
      s.lexical.prev &&
        (s.lexical.type == ')' && (s.indented = s.lexical.indented),
        (s.lexical = s.lexical.prev));
    }
    D.lex = !0;
    function q(s) {
      function p(A) {
        return A == s
          ? f()
          : s == ';' || A == '}' || A == ')' || A == ']'
            ? S()
            : f(p);
      }
      return p;
    }
    function ee(s, p) {
      return s == 'var'
        ? f(B('vardef', p), Er, q(';'), D)
        : s == 'keyword a'
          ? f(B('form'), bt, ee, D)
          : s == 'keyword b'
            ? f(B('form'), ee, D)
            : s == 'keyword d'
              ? h.stream.match(/^\s*$/, !1)
                ? f()
                : f(B('stat'), tt, q(';'), D)
              : s == 'debugger'
                ? f(q(';'))
                : s == '{'
                  ? f(B('}'), Ze, Bn, D, xe)
                  : s == ';'
                    ? f()
                    : s == 'if'
                      ? (h.state.lexical.info == 'else' &&
                          h.state.cc[h.state.cc.length - 1] == D &&
                          h.state.cc.pop()(),
                        f(B('form'), bt, ee, D, Fi))
                      : s == 'function'
                        ? f(At)
                        : s == 'for'
                          ? f(B('form'), Ze, Ii, ee, xe, D)
                          : s == 'class' || (c && p == 'interface')
                            ? ((h.marked = 'keyword'),
                              f(B('form', s == 'class' ? s : p), Li, D))
                            : s == 'variable'
                              ? c && p == 'declare'
                                ? ((h.marked = 'keyword'), f(ee))
                                : c &&
                                    (p == 'module' ||
                                      p == 'enum' ||
                                      p == 'type') &&
                                    h.stream.match(/^\s*\w/, !1)
                                  ? ((h.marked = 'keyword'),
                                    p == 'enum'
                                      ? f(Ki)
                                      : p == 'type'
                                        ? f(qi, q('operator'), V, q(';'))
                                        : f(
                                            B('form'),
                                            Be,
                                            q('{'),
                                            B('}'),
                                            Bn,
                                            D,
                                            D
                                          ))
                                  : c && p == 'namespace'
                                    ? ((h.marked = 'keyword'),
                                      f(B('form'), W, ee, D))
                                    : c && p == 'abstract'
                                      ? ((h.marked = 'keyword'), f(ee))
                                      : f(B('stat'), On)
                              : s == 'switch'
                                ? f(
                                    B('form'),
                                    bt,
                                    q('{'),
                                    B('}', 'switch'),
                                    Ze,
                                    Bn,
                                    D,
                                    D,
                                    xe
                                  )
                                : s == 'case'
                                  ? f(W, q(':'))
                                  : s == 'default'
                                    ? f(q(':'))
                                    : s == 'catch'
                                      ? f(B('form'), Re, et, ee, D, xe)
                                      : s == 'export'
                                        ? f(B('stat'), pa, D)
                                        : s == 'import'
                                          ? f(B('stat'), ua, D)
                                          : s == 'async'
                                            ? f(ee)
                                            : p == '@'
                                              ? f(W, ee)
                                              : S(B('stat'), W, q(';'), D);
    }
    function et(s) {
      if (s == '(') return f(kt, q(')'));
    }
    function W(s, p) {
      return Sn(s, p, !1);
    }
    function ye(s, p) {
      return Sn(s, p, !0);
    }
    function bt(s) {
      return s != '(' ? S() : f(B(')'), tt, q(')'), D);
    }
    function Sn(s, p, A) {
      if (h.state.fatArrowAt == h.stream.start) {
        var _ = A ? Pn : Dn;
        if (s == '(') return f(Re, B(')'), se(kt, ')'), D, q('=>'), _, xe);
        if (s == 'variable') return S(Re, Be, q('=>'), _, xe);
      }
      var F = A ? gt : nt;
      return J.hasOwnProperty(s)
        ? f(F)
        : s == 'function'
          ? f(At, F)
          : s == 'class' || (c && p == 'interface')
            ? ((h.marked = 'keyword'), f(B('form'), la, D))
            : s == 'keyword c' || s == 'async'
              ? f(A ? ye : W)
              : s == '('
                ? f(B(')'), tt, q(')'), D, F)
                : s == 'operator' || s == 'spread'
                  ? f(A ? ye : W)
                  : s == '['
                    ? f(B(']'), da, D, F)
                    : s == '{'
                      ? sn(Tt, '}', null, F)
                      : s == 'quasi'
                        ? S(Et, F)
                        : s == 'new'
                          ? f(rt(A))
                          : f();
    }
    function tt(s) {
      return s.match(/[;\}\)\],]/) ? S() : S(W);
    }
    function nt(s, p) {
      return s == ',' ? f(tt) : gt(s, p, !1);
    }
    function gt(s, p, A) {
      var _ = A == !1 ? nt : gt,
        F = A == !1 ? W : ye;
      if (s == '=>') return f(Re, A ? Pn : Dn, xe);
      if (s == 'operator')
        return /\+\+|--/.test(p) || (c && p == '!')
          ? f(_)
          : c && p == '<' && h.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/, !1)
            ? f(B('>'), se(V, '>'), D, _)
            : p == '?'
              ? f(W, q(':'), F)
              : f(F);
      if (s == 'quasi') return S(Et, _);
      if (s != ';') {
        if (s == '(') return sn(ye, ')', 'call', _);
        if (s == '.') return f(an, _);
        if (s == '[') return f(B(']'), tt, q(']'), D, _);
        if (c && p == 'as') return ((h.marked = 'keyword'), f(V, _));
        if (s == 'regexp')
          return (
            (h.state.lastType = h.marked = 'operator'),
            h.stream.backUp(h.stream.pos - h.stream.start - 1),
            f(F)
          );
      }
    }
    function Et(s, p) {
      return s != 'quasi'
        ? S()
        : p.slice(p.length - 2) != '${'
          ? f(Et)
          : f(tt, Cn);
    }
    function Cn(s) {
      if (s == '}')
        return ((h.marked = 'string-2'), (h.state.tokenize = C), f(Et));
    }
    function Dn(s) {
      return (I(h.stream, h.state), S(s == '{' ? ee : W));
    }
    function Pn(s) {
      return (I(h.stream, h.state), S(s == '{' ? ee : ye));
    }
    function rt(s) {
      return function (p) {
        return p == '.'
          ? f(s ? Nn : Kt)
          : p == 'variable' && c
            ? f(ia, s ? gt : nt)
            : S(s ? ye : W);
      };
    }
    function Kt(s, p) {
      if (p == 'target') return ((h.marked = 'keyword'), f(nt));
    }
    function Nn(s, p) {
      if (p == 'target') return ((h.marked = 'keyword'), f(gt));
    }
    function On(s) {
      return s == ':' ? f(D, ee) : S(nt, q(';'), D);
    }
    function an(s) {
      if (s == 'variable') return ((h.marked = 'property'), f());
    }
    function Tt(s, p) {
      if (s == 'async') return ((h.marked = 'property'), f(Tt));
      if (s == 'variable' || h.style == 'keyword') {
        if (((h.marked = 'property'), p == 'get' || p == 'set')) return f(Mn);
        var A;
        return (
          c &&
            h.state.fatArrowAt == h.stream.start &&
            (A = h.stream.match(/^\s*:\s*/, !1)) &&
            (h.state.fatArrowAt = h.stream.pos + A[0].length),
          f(Ye)
        );
      } else {
        if (s == 'number' || s == 'string')
          return ((h.marked = o ? 'property' : h.style + ' property'), f(Ye));
        if (s == 'jsonld-keyword') return f(Ye);
        if (c && _e(p)) return ((h.marked = 'keyword'), f(Tt));
        if (s == '[') return f(W, Rt, q(']'), Ye);
        if (s == 'spread') return f(ye, Ye);
        if (p == '*') return ((h.marked = 'keyword'), f(Tt));
        if (s == ':') return S(Ye);
      }
    }
    function Mn(s) {
      return s != 'variable' ? S(Ye) : ((h.marked = 'property'), f(At));
    }
    function Ye(s) {
      if (s == ':') return f(ye);
      if (s == '(') return S(At);
    }
    function se(s, p, A) {
      function _(F, Y) {
        if (A ? A.indexOf(F) > -1 : F == ',') {
          var z = h.state.lexical;
          return (
            z.info == 'call' && (z.pos = (z.pos || 0) + 1),
            f(function (je, Ue) {
              return je == p || Ue == p ? S() : S(s);
            }, _)
          );
        }
        return F == p || Y == p
          ? f()
          : A && A.indexOf(';') > -1
            ? S(s)
            : f(q(p));
      }
      return function (F, Y) {
        return F == p || Y == p ? f() : S(s, _);
      };
    }
    function sn(s, p, A) {
      for (var _ = 3; _ < arguments.length; _++) h.cc.push(arguments[_]);
      return f(B(p, A), se(s, p), D);
    }
    function Bn(s) {
      return s == '}' ? f() : S(ee, Bn);
    }
    function Rt(s, p) {
      if (c) {
        if (s == ':') return f(V);
        if (p == '?') return f(Rt);
      }
    }
    function ea(s, p) {
      if (c && (s == ':' || p == 'in')) return f(V);
    }
    function Mi(s) {
      if (c && s == ':')
        return h.stream.match(/^\s*\w+\s+is\b/, !1) ? f(W, ta, V) : f(V);
    }
    function ta(s, p) {
      if (p == 'is') return ((h.marked = 'keyword'), f());
    }
    function V(s, p) {
      if (p == 'keyof' || p == 'typeof' || p == 'infer' || p == 'readonly')
        return ((h.marked = 'keyword'), f(p == 'typeof' ? ye : V));
      if (s == 'variable' || p == 'void') return ((h.marked = 'type'), f(it));
      if (p == '|' || p == '&') return f(V);
      if (s == 'string' || s == 'number' || s == 'atom') return f(it);
      if (s == '[') return f(B(']'), se(V, ']', ','), D, it);
      if (s == '{') return f(B('}'), vr, D, it);
      if (s == '(') return f(se(br, ')'), na, it);
      if (s == '<') return f(se(V, '>'), V);
      if (s == 'quasi') return S(wr, it);
    }
    function na(s) {
      if (s == '=>') return f(V);
    }
    function vr(s) {
      return s.match(/[\}\)\]]/)
        ? f()
        : s == ',' || s == ';'
          ? f(vr)
          : S(cn, vr);
    }
    function cn(s, p) {
      if (s == 'variable' || h.style == 'keyword')
        return ((h.marked = 'property'), f(cn));
      if (p == '?' || s == 'number' || s == 'string') return f(cn);
      if (s == ':') return f(V);
      if (s == '[') return f(q('variable'), ea, q(']'), cn);
      if (s == '(') return S(Ut, cn);
      if (!s.match(/[;\}\)\],]/)) return f();
    }
    function wr(s, p) {
      return s != 'quasi'
        ? S()
        : p.slice(p.length - 2) != '${'
          ? f(wr)
          : f(V, ra);
    }
    function ra(s) {
      if (s == '}')
        return ((h.marked = 'string-2'), (h.state.tokenize = C), f(wr));
    }
    function br(s, p) {
      return (s == 'variable' && h.stream.match(/^\s*[?:]/, !1)) || p == '?'
        ? f(br)
        : s == ':'
          ? f(V)
          : s == 'spread'
            ? f(br)
            : S(V);
    }
    function it(s, p) {
      if (p == '<') return f(B('>'), se(V, '>'), D, it);
      if (p == '|' || s == '.' || p == '&') return f(V);
      if (s == '[') return f(V, q(']'), it);
      if (p == 'extends' || p == 'implements')
        return ((h.marked = 'keyword'), f(V));
      if (p == '?') return f(V, q(':'), V);
    }
    function ia(s, p) {
      if (p == '<') return f(B('>'), se(V, '>'), D, it);
    }
    function Fn() {
      return S(V, oa);
    }
    function oa(s, p) {
      if (p == '=') return f(V);
    }
    function Er(s, p) {
      return p == 'enum' ? ((h.marked = 'keyword'), f(Ki)) : S(Be, Rt, ht, sa);
    }
    function Be(s, p) {
      if (c && _e(p)) return ((h.marked = 'keyword'), f(Be));
      if (s == 'variable') return (be(p), f());
      if (s == 'spread') return f(Be);
      if (s == '[') return sn(aa, ']');
      if (s == '{') return sn(Bi, '}');
    }
    function Bi(s, p) {
      return s == 'variable' && !h.stream.match(/^\s*:/, !1)
        ? (be(p), f(ht))
        : (s == 'variable' && (h.marked = 'property'),
          s == 'spread'
            ? f(Be)
            : s == '}'
              ? S()
              : s == '['
                ? f(W, q(']'), q(':'), Bi)
                : f(q(':'), Be, ht));
    }
    function aa() {
      return S(Be, ht);
    }
    function ht(s, p) {
      if (p == '=') return f(ye);
    }
    function sa(s) {
      if (s == ',') return f(Er);
    }
    function Fi(s, p) {
      if (s == 'keyword b' && p == 'else') return f(B('form', 'else'), ee, D);
    }
    function Ii(s, p) {
      if (p == 'await') return f(Ii);
      if (s == '(') return f(B(')'), ca, D);
    }
    function ca(s) {
      return s == 'var' ? f(Er, Yt) : s == 'variable' ? f(Yt) : S(Yt);
    }
    function Yt(s, p) {
      return s == ')'
        ? f()
        : s == ';'
          ? f(Yt)
          : p == 'in' || p == 'of'
            ? ((h.marked = 'keyword'), f(W, Yt))
            : S(W, Yt);
    }
    function At(s, p) {
      if (p == '*') return ((h.marked = 'keyword'), f(At));
      if (s == 'variable') return (be(p), f(At));
      if (s == '(') return f(Re, B(')'), se(kt, ')'), D, Mi, ee, xe);
      if (c && p == '<') return f(B('>'), se(Fn, '>'), D, At);
    }
    function Ut(s, p) {
      if (p == '*') return ((h.marked = 'keyword'), f(Ut));
      if (s == 'variable') return (be(p), f(Ut));
      if (s == '(') return f(Re, B(')'), se(kt, ')'), D, Mi, xe);
      if (c && p == '<') return f(B('>'), se(Fn, '>'), D, Ut);
    }
    function qi(s, p) {
      if (s == 'keyword' || s == 'variable')
        return ((h.marked = 'type'), f(qi));
      if (p == '<') return f(B('>'), se(Fn, '>'), D);
    }
    function kt(s, p) {
      return (
        p == '@' && f(W, kt),
        s == 'spread'
          ? f(kt)
          : c && _e(p)
            ? ((h.marked = 'keyword'), f(kt))
            : c && s == 'this'
              ? f(Rt, ht)
              : S(Be, Rt, ht)
      );
    }
    function la(s, p) {
      return s == 'variable' ? Li(s, p) : In(s, p);
    }
    function Li(s, p) {
      if (s == 'variable') return (be(p), f(In));
    }
    function In(s, p) {
      if (p == '<') return f(B('>'), se(Fn, '>'), D, In);
      if (p == 'extends' || p == 'implements' || (c && s == ','))
        return (p == 'implements' && (h.marked = 'keyword'), f(c ? V : W, In));
      if (s == '{') return f(B('}'), ot, D);
    }
    function ot(s, p) {
      if (
        s == 'async' ||
        (s == 'variable' &&
          (p == 'static' || p == 'get' || p == 'set' || (c && _e(p))) &&
          h.stream.match(/^\s+[\w$\xa1-\uffff]/, !1))
      )
        return ((h.marked = 'keyword'), f(ot));
      if (s == 'variable' || h.style == 'keyword')
        return ((h.marked = 'property'), f(ln, ot));
      if (s == 'number' || s == 'string') return f(ln, ot);
      if (s == '[') return f(W, Rt, q(']'), ln, ot);
      if (p == '*') return ((h.marked = 'keyword'), f(ot));
      if (c && s == '(') return S(Ut, ot);
      if (s == ';' || s == ',') return f(ot);
      if (s == '}') return f();
      if (p == '@') return f(W, ot);
    }
    function ln(s, p) {
      if (p == '!' || p == '?') return f(ln);
      if (s == ':') return f(V, ht);
      if (p == '=') return f(ye);
      var A = h.state.lexical.prev,
        _ = A && A.info == 'interface';
      return S(_ ? Ut : At);
    }
    function pa(s, p) {
      return p == '*'
        ? ((h.marked = 'keyword'), f(Tr, q(';')))
        : p == 'default'
          ? ((h.marked = 'keyword'), f(W, q(';')))
          : s == '{'
            ? f(se(Hi, '}'), Tr, q(';'))
            : S(ee);
    }
    function Hi(s, p) {
      if (p == 'as') return ((h.marked = 'keyword'), f(q('variable')));
      if (s == 'variable') return S(ye, Hi);
    }
    function ua(s) {
      return s == 'string'
        ? f()
        : s == '('
          ? S(W)
          : s == '.'
            ? S(nt)
            : S(qn, $i, Tr);
    }
    function qn(s, p) {
      return s == '{'
        ? sn(qn, '}')
        : (s == 'variable' && be(p), p == '*' && (h.marked = 'keyword'), f(fa));
    }
    function $i(s) {
      if (s == ',') return f(qn, $i);
    }
    function fa(s, p) {
      if (p == 'as') return ((h.marked = 'keyword'), f(qn));
    }
    function Tr(s, p) {
      if (p == 'from') return ((h.marked = 'keyword'), f(W));
    }
    function da(s) {
      return s == ']' ? f() : S(se(ye, ']'));
    }
    function Ki() {
      return S(B('form'), Be, q('{'), B('}'), se(ma, '}'), D, D);
    }
    function ma() {
      return S(Be, ht);
    }
    function ga(s, p) {
      return (
        s.lastType == 'operator' ||
        s.lastType == ',' ||
        y.test(p.charAt(0)) ||
        /[,.]/.test(p.charAt(0))
      );
    }
    function Ri(s, p, A) {
      return (
        (p.tokenize == M &&
          /^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(
            p.lastType
          )) ||
        (p.lastType == 'quasi' &&
          /\{\s*$/.test(s.string.slice(0, s.pos - (A || 0))))
      );
    }
    return {
      startState: function (s) {
        var p = {
          tokenize: M,
          lastType: 'sof',
          cc: [],
          lexical: new te((s || 0) - r, 0, 'block', !1),
          localVars: t.localVars,
          context: t.localVars && new Ke(null, null, !1),
          indented: s || 0,
        };
        return (
          t.globalVars &&
            typeof t.globalVars == 'object' &&
            (p.globalVars = t.globalVars),
          p
        );
      },
      token: function (s, p) {
        if (
          (s.sol() &&
            (p.lexical.hasOwnProperty('align') || (p.lexical.align = !1),
            (p.indented = s.indentation()),
            I(s, p)),
          p.tokenize != K && s.eatSpace())
        )
          return null;
        var A = p.tokenize(s, p);
        return P == 'comment'
          ? A
          : ((p.lastType =
              P == 'operator' && (k == '++' || k == '--') ? 'incdec' : P),
            Q(p, A, P, k, s));
      },
      indent: function (s, p) {
        if (s.tokenize == K || s.tokenize == C) return n.Pass;
        if (s.tokenize != M) return 0;
        var A = p && p.charAt(0),
          _ = s.lexical,
          F;
        if (!/^\s*else\b/.test(p))
          for (var Y = s.cc.length - 1; Y >= 0; --Y) {
            var z = s.cc[Y];
            if (z == D) _ = _.prev;
            else if (z != Fi && z != xe) break;
          }
        for (
          ;
          (_.type == 'stat' || _.type == 'form') &&
          (A == '}' ||
            ((F = s.cc[s.cc.length - 1]) &&
              (F == nt || F == gt) &&
              !/^[,\.=+\-*:?[\(]/.test(p)));

        )
          _ = _.prev;
        i && _.type == ')' && _.prev.type == 'stat' && (_ = _.prev);
        var je = _.type,
          Ue = A == je;
        return je == 'vardef'
          ? _.indented +
              (s.lastType == 'operator' || s.lastType == ','
                ? _.info.length + 1
                : 0)
          : je == 'form' && A == '{'
            ? _.indented
            : je == 'form'
              ? _.indented + r
              : je == 'stat'
                ? _.indented + (ga(s, p) ? i || r : 0)
                : _.info == 'switch' && !Ue && t.doubleIndentSwitch != !1
                  ? _.indented + (/^(?:case|default)\b/.test(p) ? r : 2 * r)
                  : _.align
                    ? _.column + (Ue ? 0 : 1)
                    : _.indented + (Ue ? 0 : r);
      },
      electricInput: /^\s*(?:case .*?:|default:|\{|\})$/,
      blockCommentStart: a ? null : '/*',
      blockCommentEnd: a ? null : '*/',
      blockCommentContinue: a ? null : ' * ',
      lineComment: a ? null : '//',
      fold: 'brace',
      closeBrackets: '()[]{}\'\'""``',
      helperType: a ? 'json' : 'javascript',
      jsonldMode: o,
      jsonMode: a,
      expressionAllowed: Ri,
      skipExpression: function (s) {
        Q(s, 'atom', 'atom', 'true', new n.StringStream('', 2, null));
      },
    };
  }),
    n.registerHelper('wordChars', 'javascript', /[\w$]/),
    n.defineMIME('text/javascript', 'javascript'),
    n.defineMIME('text/ecmascript', 'javascript'),
    n.defineMIME('application/javascript', 'javascript'),
    n.defineMIME('application/x-javascript', 'javascript'),
    n.defineMIME('application/ecmascript', 'javascript'),
    n.defineMIME('application/json', { name: 'javascript', json: !0 }),
    n.defineMIME('application/x-json', { name: 'javascript', json: !0 }),
    n.defineMIME('application/manifest+json', { name: 'javascript', json: !0 }),
    n.defineMIME('application/ld+json', { name: 'javascript', jsonld: !0 }),
    n.defineMIME('text/typescript', { name: 'javascript', typescript: !0 }),
    n.defineMIME('application/typescript', {
      name: 'javascript',
      typescript: !0,
    }));
});
(function (n) {
  n(window.CodeMirror);
})(function (n) {
  'use strict';
  n.customOverlayMode = function (e, t, r) {
    return {
      startState: function () {
        return {
          base: n.startState(e),
          overlay: n.startState(t),
          basePos: 0,
          baseCur: null,
          overlayPos: 0,
          overlayCur: null,
          streamSeen: null,
        };
      },
      copyState: function (i) {
        return {
          base: n.copyState(e, i.base),
          overlay: n.copyState(t, i.overlay),
          basePos: i.basePos,
          baseCur: null,
          overlayPos: i.overlayPos,
          overlayCur: null,
        };
      },
      token: function (i, o) {
        return (
          (i != o.streamSeen || Math.min(o.basePos, o.overlayPos) < i.start) &&
            ((o.streamSeen = i), (o.basePos = o.overlayPos = i.start)),
          i.start == o.basePos &&
            ((o.baseCur = e.token(i, o.base)), (o.basePos = i.pos)),
          i.start == o.overlayPos &&
            ((i.pos = i.start),
            (o.overlayCur = t.token(i, o.overlay)),
            (o.overlayPos = i.pos)),
          (i.pos = Math.min(o.basePos, o.overlayPos)),
          o.baseCur &&
            o.overlayCur &&
            o.baseCur.contains('line-HyperMD-codeblock') &&
            ((o.overlayCur = o.overlayCur.replace('line-templater-inline', '')),
            (o.overlayCur += ' line-background-HyperMD-codeblock-bg')),
          o.overlayCur == null
            ? o.baseCur
            : (o.baseCur != null && o.overlay.combineTokens) ||
                (r && o.overlay.combineTokens == null)
              ? o.baseCur + ' ' + o.overlayCur
              : o.overlayCur
        );
      },
      indent:
        e.indent &&
        function (i, o, a) {
          return e.indent(i.base, o, a);
        },
      electricChars: e.electricChars,
      innerMode: function (i) {
        return { state: i.base, mode: e };
      },
      blankLine: function (i) {
        let o, a;
        return (
          e.blankLine && (o = e.blankLine(i.base)),
          t.blankLine && (a = t.blankLine(i.overlay)),
          a == null ? o : r && o != null ? o + ' ' + a : a
        );
      },
    };
  };
});
var Qo = X(require('@codemirror/language')),
  Xo = X(require('@codemirror/state')),
  Zo = 'templater',
  Di = 'templater-command',
  Pi = 'templater-inline',
  Zs = 'templater-opening-tag',
  ec = 'templater-closing-tag',
  tc = 'templater-interpolation-tag',
  nc = 'templater-execution-tag',
  Ni = class {
    constructor(e) {
      this.plugin = e;
      ((this.cursor_jumper = new Ei(e.app)),
        (this.activeEditorExtensions = []));
    }
    desktopShouldHighlight() {
      return (
        Ci.Platform.isDesktopApp && this.plugin.settings.syntax_highlighting
      );
    }
    mobileShouldHighlight() {
      return (
        Ci.Platform.isMobile && this.plugin.settings.syntax_highlighting_mobile
      );
    }
    async setup() {
      ((this.autocomplete = new Si(this.plugin)),
        this.plugin.registerEditorSuggest(this.autocomplete),
        await this.registerCodeMirrorMode(),
        (this.templaterLanguage = Xo.Prec.high(
          Qo.StreamLanguage.define(window.CodeMirror.getMode({}, Zo))
        )),
        this.templaterLanguage === void 0 &&
          oe(
            new O(
              'Unable to enable syntax highlighting. Could not define language.'
            )
          ),
        this.plugin.registerEditorExtension(this.activeEditorExtensions),
        (this.desktopShouldHighlight() || this.mobileShouldHighlight()) &&
          (await this.enable_highlighter()));
    }
    async enable_highlighter() {
      this.activeEditorExtensions.length === 0 &&
        this.templaterLanguage &&
        (this.activeEditorExtensions.push(this.templaterLanguage),
        this.plugin.app.workspace.updateOptions());
    }
    async disable_highlighter() {
      this.activeEditorExtensions.length > 0 &&
        (this.activeEditorExtensions.pop(),
        this.plugin.app.workspace.updateOptions());
    }
    async jump_to_next_cursor_location(e = null, t = !1) {
      (t && !this.plugin.settings.auto_jump_to_cursor) ||
        (e && Jt(this.plugin.app) !== e) ||
        (await this.cursor_jumper.jump_to_next_cursor_location());
    }
    async registerCodeMirrorMode() {
      if (!this.desktopShouldHighlight() && !this.mobileShouldHighlight())
        return;
      let e = window.CodeMirror.getMode({}, 'javascript');
      if (e.name === 'null') {
        oe(
          new O(
            "Javascript syntax mode couldn't be found, can't enable syntax highlighting."
          )
        );
        return;
      }
      let t = window.CodeMirror.customOverlayMode;
      if (t == null) {
        oe(
          new O(
            "Couldn't find customOverlayMode, can't enable syntax highlighting."
          )
        );
        return;
      }
      window.CodeMirror.defineMode(Zo, function (r) {
        let i = {
          startState: function () {
            return {
              ...window.CodeMirror.startState(e),
              inCommand: !1,
              tag_class: '',
              freeLine: !1,
            };
          },
          copyState: function (o) {
            return {
              ...window.CodeMirror.startState(e),
              inCommand: o.inCommand,
              tag_class: o.tag_class,
              freeLine: o.freeLine,
            };
          },
          blankLine: function (o) {
            return o.inCommand ? 'line-background-templater-command-bg' : null;
          },
          token: function (o, a) {
            if ((o.sol() && a.inCommand && (a.freeLine = !0), a.inCommand)) {
              let c = '';
              if (o.match(/[-_]{0,1}%>/, !0)) {
                ((a.inCommand = !1), (a.freeLine = !1));
                let m = a.tag_class;
                return ((a.tag_class = ''), `line-${Pi} ${Di} ${ec} ${m}`);
              }
              let d = e.token && e.token(o, a);
              return (
                o.peek() == null &&
                  a.freeLine &&
                  (c += ' line-background-templater-command-bg'),
                a.freeLine || (c += ` line-${Pi}`),
                `${c} ${Di} ${d}`
              );
            }
            let l = o.match(/<%[-_]{0,1}\s*([*+]{0,1})/, !0);
            if (l != null) {
              switch (l[1]) {
                case '*':
                  a.tag_class = nc;
                  break;
                default:
                  a.tag_class = tc;
                  break;
              }
              return (
                (a.inCommand = !0),
                `line-${Pi} ${Di} ${Zs} ${a.tag_class}`
              );
            }
            for (; o.next() != null && !o.match(/<%/, !1); );
            return null;
          },
        };
        return t(window.CodeMirror.getMode(r, 'hypermd'), i);
      });
    }
    updateEditorIntellisenseSetting(e) {
      this.autocomplete.updateAutocompleteIntellisenseSetting(e);
    }
  };
var Oi = class extends jr.Plugin {
  async onload() {
    (await this.load_settings(),
      (this.templater = new kn(this)),
      await this.templater.setup(),
      (this.editor_handler = new Ni(this)),
      await this.editor_handler.setup(),
      (this.fuzzy_suggester = new ii(this)),
      (this.event_handler = new xr(this, this.templater, this.settings)),
      this.event_handler.setup(),
      (this.command_handler = new wi(this)),
      this.command_handler.setup(),
      (0, jr.addIcon)('templater-icon', qo),
      this.addRibbonIcon('templater-icon', 'Templater', async () => {
        this.fuzzy_suggester.insert_template();
      }).setAttribute('id', 'rb-templater-icon'),
      this.addSettingTab(new ri(this)),
      this.app.workspace.onLayoutReady(() => {
        this.templater.execute_startup_scripts();
      }));
  }
  onunload() {
    this.templater.functions_generator.teardown();
  }
  async save_settings() {
    (await this.saveData(this.settings),
      this.editor_handler.updateEditorIntellisenseSetting(
        this.settings.intellisense_render
      ));
  }
  async load_settings() {
    this.settings = Object.assign({}, Fo, await this.loadData());
  }
};

/* nosourcemap */

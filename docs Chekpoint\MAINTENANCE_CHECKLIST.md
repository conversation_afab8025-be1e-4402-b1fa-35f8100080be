# 🔧 Checklist de Manutenção - Portfólio Engenharia

## 📋 Checklist Semanal

### ✅ Verificação de Funcionamento
- [ ] **Site principal** carrega corretamente
- [ ] **Páginas de projeto** funcionam (ex: /projetos/casa-gp/)
- [ ] **PDFs** fazem download corretamente
- [ ] **Imagens** carregam na galeria
- [ ] **Formulário de contato** envia mensagens
- [ ] **Links internos** não estão quebrados

### ✅ Performance
- [ ] **Lighthouse Score** > 90 em todas as métricas
- [ ] **Tempo de carregamento** < 3 segundos
- [ ] **Imagens otimizadas** (WebP quando possível)
- [ ] **CSS/JS minificados** em produção

### ✅ SEO
- [ ] **Meta tags** estão corretas
- [ ] **Sitemap** atualizado
- [ ] **Robots.txt** configurado
- [ ] **Schema markup** implementado

## 📋 Checklist Mensal

### ✅ Atualizações de Dependências
```bash
# Verificar dependências desatualizadas
npm outdated

# Atualizar dependências (cuidadosamente)
npm update

# Verificar vulnerabilidades
npm audit
```

### ✅ Backup e Versionamento
- [ ] **Backup** dos arquivos de conteúdo
- [ ] **Commit** das mudanças no Git
- [ ] **Tag** de versão se necessário
- [ ] **Documentação** atualizada

### ✅ Análise de Conteúdo
- [ ] **Novos projetos** adicionados ao `projects.js`
- [ ] **Arquivos markdown** criados para novos projetos
- [ ] **Imagens** otimizadas e adicionadas
- [ ] **PDFs** atualizados e funcionais

## 📋 Checklist Trimestral

### ✅ Auditoria de Segurança
- [ ] **Dependências** sem vulnerabilidades conhecidas
- [ ] **HTTPS** configurado corretamente
- [ ] **Headers de segurança** implementados
- [ ] **Backup** de dados críticos

### ✅ Otimização de Performance
- [ ] **Análise de bundle** (se aplicável)
- [ ] **Otimização de imagens** (compressão)
- [ ] **Cache** configurado adequadamente
- [ ] **CDN** funcionando corretamente

### ✅ Atualização de Conteúdo
- [ ] **Projetos antigos** revisados
- [ ] **Informações de contato** atualizadas
- [ ] **Links externos** verificados
- [ ] **Conteúdo desatualizado** removido

## 🚨 Checklist de Emergência

### ✅ Problemas Críticos
- [ ] **Site fora do ar** → Verificar deploy no Netlify
- [ ] **PDFs não carregam** → Verificar pasta `assets/`
- [ ] **Imagens quebradas** → Verificar caminhos e otimização
- [ ] **Formulário não funciona** → Verificar configuração do Netlify

### ✅ Comandos de Recuperação
```bash
# Limpar cache e reinstalar dependências
rm -rf node_modules package-lock.json
npm install

# Rebuild completo
npm run build

# Verificar build local
npm run dev

# Testar funcionalidades críticas
npm run test:e2e
```

## 📊 Métricas de Monitoramento

### ✅ Métricas Diárias
- **Uptime**: 99.9%+
- **Tempo de resposta**: < 200ms
- **Erros 404**: < 1%
- **Taxa de conversão**: > 2%

### ✅ Métricas Semanais
- **Visitas únicas**: Monitorar tendências
- **Páginas mais visitadas**: Otimizar conteúdo
- **Tempo na página**: > 2 minutos
- **Taxa de rejeição**: < 40%

## 🔄 Processo de Atualização

### ✅ Adicionar Novo Projeto
1. **Dados estruturados** (`projects.js`)
   ```javascript
   'novo-projeto': {
     id: 'novo-projeto',
     title: 'Nome do Projeto',
     // ... outros campos
   }
   ```

2. **Conteúdo rico** (`notes/`)
   ```bash
   # Criar arquivo markdown
   touch src/site/notes/CLIENTE/projeto-eletrico-np_novo-projeto.md
   ```

3. **Assets** (`assets/`)
   ```bash
   # Adicionar imagens
   cp imagens/* src/site/assets/imagens/
   
   # Adicionar PDFs
   cp pdfs/* src/site/assets/pdfs/
   ```

4. **Mapeamento** (`projectPages.js`)
   ```javascript
   'novo-projeto': 'notes/CLIENTE/projeto-eletrico-np_novo-projeto.md'
   ```

5. **Teste e Deploy**
   ```bash
   npm run build
   npm run test:e2e
   git add .
   git commit -m "Add novo projeto"
   git push
   ```

### ✅ Atualizar Projeto Existente
1. **Dados básicos**: Editar `projects.js`
2. **Conteúdo rico**: Editar arquivo markdown
3. **Assets**: Substituir imagens/PDFs se necessário
4. **Teste**: Verificar funcionamento
5. **Deploy**: Push para produção

## 🛠️ Ferramentas de Manutenção

### ✅ Scripts Úteis
```bash
# Verificar links quebrados
npm run check-links

# Otimizar imagens
npm run optimize-images

# Validar HTML
npm run validate-html

# Testar acessibilidade
npm run test-a11y
```

### ✅ Comandos de Diagnóstico
```bash
# Verificar estrutura de arquivos
tree src/site/ -I node_modules

# Verificar tamanho do build
du -sh dist/

# Verificar dependências
npm ls --depth=0

# Verificar configuração do Eleventy
npx @11ty/eleventy --help
```

## 📝 Log de Manutenção

### ✅ Template de Log
```markdown
## [DATA] - Manutenção Realizada

### ✅ Tarefas Concluídas
- [ ] Tarefa 1
- [ ] Tarefa 2

### 🔧 Problemas Encontrados
- Problema 1: Solução aplicada
- Problema 2: Solução aplicada

### 📊 Métricas Atualizadas
- Performance: X/100
- Acessibilidade: X/100
- SEO: X/100

### 📋 Próximas Ações
- [ ] Ação 1
- [ ] Ação 2
```

---

*Este checklist deve ser revisado e atualizado regularmente conforme o projeto evolui.* 
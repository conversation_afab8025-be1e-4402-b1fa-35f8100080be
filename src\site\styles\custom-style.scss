body {
  /***
      ADD YOUR CUSTOM STYLING HERE. (INSIDE THE body {...} section.)
      IT WILL TAKE PRECEDENCE OVER THE STYLING IN THE STYLE.CSS FILE.
   ***/
  //  background-color: white;
  //  .content {
  //   font-size: 14px;
  //  }
  //  h1 {
  //   color: black;
  //  }
}

// Estilos para páginas de projeto
.project-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
  
  .gallery-thumb {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
    }
  }
}

.documentation-links {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 2rem 0;
  
  .doc-link {
    display: inline-flex;
    align-items: center;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
      color: white;
    }
  }
}

.pdf-container {
  margin: 2rem 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  
  // Proteção contra seleção e cópia (mais suave para mobile)
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  
  // Desabilita menu de contexto (apenas em desktop)
  @media (min-width: 768px) {
    -webkit-context-menu: none;
    -moz-context-menu: none;
    -ms-context-menu: none;
    context-menu: none;
  }
  
  object, embed {
    width: 100%;
    height: 500px;
    border: none;
    pointer-events: auto; // Permitir interação em mobile
    
    // Proteção mais suave para mobile
    @media (min-width: 768px) {
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
  }
  
  // Overlay de proteção (apenas em desktop)
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    pointer-events: none;
    
    @media (max-width: 767px) {
      display: none; // Remover overlay em mobile
    }
  }
  
  // Aviso de proteção (apenas em desktop)
  &::after {
    content: '🔒 Visualização Protegida';
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 20;
    opacity: 0;
    transition: opacity 0.3s ease;
    
    @media (max-width: 767px) {
      display: none; // Remover aviso em mobile
    }
  }
  
  &:hover::after {
    opacity: 1;
  }
}

// Estilo para links de PDF (melhorado para mobile)
.pdf-link {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 0.5rem 0;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    color: white;
  }
  
  // Remove target="_blank" para evitar downloads
  &[target="_blank"] {
    target: none;
  }
}

// Melhorias gerais para páginas de projeto
.project-content {
  h1 {
    color: #2d3748;
    margin-bottom: 1rem;
  }
  
  h2 {
    color: #4a5568;
    margin: 2rem 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e2e8f0;
  }
  
  h3 {
    color: #2d3748;
    margin: 1.5rem 0 0.5rem 0;
  }
  
  ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
    
    li {
      margin: 0.5rem 0;
      line-height: 1.6;
    }
  }
  
  p {
    line-height: 1.7;
    margin: 1rem 0;
  }
  
  strong {
    color: #2d3748;
  }
}

// Responsividade melhorada para mobile
@media (max-width: 768px) {
  .project-gallery {
    grid-template-columns: 1fr;
    
    .gallery-thumb {
      height: 150px;
    }
  }
  
  .documentation-links {
    .doc-link {
      padding: 0.75rem 1rem;
      font-size: 0.9rem;
    }
  }
  
  // Altura maior para PDFs em mobile para melhor visualização
  .pdf-container object,
  .pdf-container embed {
    height: 400px; // Aumentado de 300px para 400px
    min-height: 300px; // Altura mínima garantida
  }
  
  // Melhorar interação em mobile
  .pdf-container {
    pointer-events: auto;
    
    object, embed {
      pointer-events: auto;
      -webkit-user-select: auto; // Permitir seleção em mobile
      user-select: auto;
    }
  }
}

// Responsividade para telas muito pequenas
@media (max-width: 480px) {
  .pdf-container object,
  .pdf-container embed {
    height: 350px; // Altura adequada para telas pequenas
  }
}

// Estilos para o carrossel automático de projetos
#projects-carousel {
  display: flex;
  transition: transform 4s ease-in-out; // 4 segundos como solicitado
  will-change: transform;
  min-height: 400px; // Altura mínima para garantir visibilidade
  
  .project-card {
    flex-shrink: 0;
    margin: 0 1rem;
    transition: all 0.3s ease;
    width: 300px; // Largura fixa simples
    min-width: 280px; // Mínimo menor para evitar conflitos
    max-width: none; // Remover máximo fixo que causava conflito
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }
    
    img {
      transition: transform 0.3s ease;
      width: 100%;
      height: 200px;
      object-fit: cover;
      
      &:hover {
        transform: scale(1.05);
      }
    }
  }
}

// Container do carrossel
.carousel-container {
  position: relative;
  overflow: visible; // Forçar visibilidade para debug
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 16px;
  padding: 2rem 0;
  min-height: 500px; // Altura mínima para garantir visibilidade
  border: 3px solid green; // DEBUG: Borda verde para visualizar o container
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.3) 50%, transparent 100%);
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.3) 50%, transparent 100%);
  }
}

// Indicadores de progresso do carrossel
.carousel-progress {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.5rem;
  z-index: 10;
  
  .progress-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(59, 130, 246, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
    
    &.active {
      background: #3b82f6;
      transform: scale(1.2);
    }
    
    &:hover {
      background: #3b82f6;
      transform: scale(1.1);
    }
  }
}

// Controles do carrossel
.carousel-controls {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  
  button {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    
    &:hover {
      background: white;
      transform: scale(1.1);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }
    
    svg {
      width: 20px;
      height: 20px;
      color: #374151;
    }
  }
  
  .prev-btn {
    left: 1rem;
  }
  
  .next-btn {
    right: 1rem;
  }
}

// Animação de entrada para os cards
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.project-card {
  animation: slideInFromRight 0.6s ease-out;
}

// Responsividade do carrossel
@media (max-width: 768px) {
  #projects-carousel {
    .project-card {
      margin: 0 0.5rem;
      width: calc(100% / 2); // 2 cards por view em mobile
      min-width: 250px;
    }
  }
  
  .carousel-controls {
    button {
      width: 40px;
      height: 40px;
      
      svg {
        width: 16px;
        height: 16px;
      }
    }
  }
  
  .carousel-container {
    min-height: 400px;
  }
}

@media (max-width: 480px) {
  #projects-carousel {
    .project-card {
      width: 100%; // 1 card por view em mobile pequeno
      min-width: 200px;
    }
  }
}

---
layout: layouts/base.njk
title: Blog Técnico
description: Artigos técnicos sobre engenharia elétrica, hidrossanitária, BIM e inovações no setor da construção.
---

<!-- Hero Section -->
<section class="relative py-20 bg-gradient-to-br from-primary-50 to-secondary-50">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center" data-aos="fade-up">
      <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
        Blog <span class="text-primary-600">Técnico</span>
      </h1>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Artigos técnicos sobre engenharia elétrica, hidrossanitária, BIM e inovações no setor da construção
      </p>
    </div>
  </div>
</section>

<!-- Categorias Section -->
<section class="py-12 bg-white border-b border-gray-200">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex flex-wrap gap-4 justify-center" data-aos="fade-up">
      <button class="category-btn active px-6 py-3 rounded-lg bg-primary-600 text-white font-medium transition-all duration-200" data-category="all">
        Todos os Artigos
      </button>
      <button class="category-btn px-6 py-3 rounded-lg bg-gray-100 text-gray-700 hover:bg-primary-600 hover:text-white font-medium transition-all duration-200" data-category="eletrica">
        Engenharia Elétrica
      </button>
      <button class="category-btn px-6 py-3 rounded-lg bg-gray-100 text-gray-700 hover:bg-primary-600 hover:text-white font-medium transition-all duration-200" data-category="hidrossanitaria">
        Hidrossanitária
      </button>
      <button class="category-btn px-6 py-3 rounded-lg bg-gray-100 text-gray-700 hover:bg-primary-600 hover:text-white font-medium transition-all duration-200" data-category="bim">
        BIM
      </button>
      <button class="category-btn px-6 py-3 rounded-lg bg-gray-100 text-gray-700 hover:bg-primary-600 hover:text-white font-medium transition-all duration-200" data-category="inovacao">
        Inovação
      </button>
    </div>
  </div>
</section>

<!-- Artigos Section -->
<section class="py-20 bg-gray-50">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="articles-grid">
      <!-- Artigo 1 -->
      <article class="article-item bg-white rounded-xl shadow-lg overflow-hidden cursor-pointer" data-category="eletrica" data-aos="fade-up" data-aos-delay="100">
        <div class="aspect-w-16 aspect-h-9">
          <img src="/assets/imagens/blog-covers/dimensionamento-condutores-cover.png" alt="Dimensionamento de Condutores Elétricos" class="w-full h-48 object-cover">
        </div>
        <div class="p-6">
          <div class="flex items-center justify-between mb-2">
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Engenharia Elétrica
            </span>
            <span class="text-sm text-gray-500">15 Jan 2024</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">Dimensionamento de Condutores Elétricos</h3>
          <p class="text-gray-600 mb-4">Guia completo sobre como dimensionar condutores elétricos seguindo as normas ABNT NBR 5410...</p>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-500">8 min de leitura</span>
            <a href="/blog/dimensionamento-condutores" class="text-primary-600 hover:text-primary-700 font-medium">
              Ler artigo →
            </a>
          </div>
        </div>
      </article>

      <!-- Artigo 2 -->
      <article class="article-item bg-white rounded-xl shadow-lg overflow-hidden cursor-pointer" data-category="hidrossanitaria" data-aos="fade-up" data-aos-delay="200">
        <div class="aspect-w-16 aspect-h-9">
          <img src="/assets/imagens/blog-covers/sistemas-agua-quente-cover.png" alt="Sistemas de Água Quente" class="w-full h-48 object-cover">
        </div>
        <div class="p-6">
          <div class="flex items-center justify-between mb-2">
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Hidrossanitária
            </span>
            <span class="text-sm text-gray-500">10 Jan 2024</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">Sistemas de Água Quente</h3>
          <p class="text-gray-600 mb-4">Como projetar sistemas de água quente eficientes e sustentáveis para diferentes tipos de edificações...</p>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-500">7 min de leitura</span>
            <a href="/blog/sistemas-agua-quente" class="text-primary-600 hover:text-primary-700 font-medium">
              Ler artigo →
            </a>
          </div>
        </div>
      </article>

      <!-- Artigo 3 -->
      <article class="article-item bg-white rounded-xl shadow-lg overflow-hidden cursor-pointer" data-category="bim" data-aos="fade-up" data-aos-delay="300">
        <div class="aspect-w-16 aspect-h-9">
          <img src="/assets/imagens/blog-covers/bim-engenharia-eletrica-cover.png" alt="BIM na Engenharia Elétrica" class="w-full h-48 object-cover">
        </div>
        <div class="p-6">
          <div class="flex items-center justify-between mb-2">
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              BIM
            </span>
            <span class="text-sm text-gray-500">05 Jan 2024</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">BIM na Engenharia Elétrica</h3>
          <p class="text-gray-600 mb-4">Como o Building Information Modeling está revolucionando os projetos elétricos...</p>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-500">8 min de leitura</span>
            <a href="/blog/bim-engenharia-eletrica" class="text-primary-600 hover:text-primary-700 font-medium">
              Ler artigo →
            </a>
          </div>
        </div>
      </article>

      <!-- Artigo 4 -->
      <article class="article-item bg-white rounded-xl shadow-lg overflow-hidden cursor-pointer" data-category="inovacao" data-aos="fade-up" data-aos-delay="400">
        <div class="aspect-w-16 aspect-h-9">
          <img src="/assets/imagens/blog-covers/energia-solar-residencial-cover.png" alt="Energia Solar Residencial" class="w-full h-48 object-cover">
        </div>
        <div class="p-6">
          <div class="flex items-center justify-between mb-2">
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
              Inovação
            </span>
            <span class="text-sm text-gray-500">01 Jan 2024</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">Energia Solar em Projetos Residenciais</h3>
          <p class="text-gray-600 mb-4">Integrando sistemas fotovoltaicos em projetos elétricos residenciais...</p>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-500">6 min de leitura</span>
            <a href="/blog/energia-solar-residencial" class="text-primary-600 hover:text-primary-700 font-medium">
              Ler artigo →
            </a>
          </div>
        </div>
      </article>

      <!-- Artigo 5 -->
      <article class="article-item bg-white rounded-xl shadow-lg overflow-hidden cursor-pointer" data-category="eletrica" data-aos="fade-up" data-aos-delay="500">
        <div class="aspect-w-16 aspect-h-9">
          <img src="/assets/imagens/blog-covers/quadros-distribuicao-cover.png" alt="Quadros de Distribuição" class="w-full h-48 object-cover">
        </div>
        <div class="p-6">
          <div class="flex items-center justify-between mb-2">
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Engenharia Elétrica
            </span>
            <span class="text-sm text-gray-500">28 Dez 2023</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">Quadros de Distribuição</h3>
          <p class="text-gray-600 mb-4">Princípios de dimensionamento e proteção em quadros elétricos...</p>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-500">9 min de leitura</span>
            <a href="/blog/quadros-distribuicao" class="text-primary-600 hover:text-primary-700 font-medium">
              Ler artigo →
            </a>
          </div>
        </div>
      </article>

      <!-- Artigo 6 -->
      <article class="article-item bg-white rounded-xl shadow-lg overflow-hidden cursor-pointer" data-category="hidrossanitaria" data-aos="fade-up" data-aos-delay="600">
        <div class="aspect-w-16 aspect-h-9">
          <img src="/assets/imagens/blog-covers/sistemas-esgoto-sanitario-cover.png" alt="Sistemas de Esgoto Sanitário" class="w-full h-48 object-cover">
        </div>
        <div class="p-6">
          <div class="flex items-center justify-between mb-2">
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Hidrossanitária
            </span>
            <span class="text-sm text-gray-500">25 Dez 2023</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">Sistemas de Esgoto Sanitário</h3>
          <p class="text-gray-600 mb-4">Dimensionamento e especificação de sistemas de esgoto sanitário...</p>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-500">7 min de leitura</span>
            <a href="/blog/sistemas-esgoto-sanitario" class="text-primary-600 hover:text-primary-700 font-medium">
              Ler artigo →
            </a>
          </div>
        </div>
      </article>
    </div>
  </div>
</section>

<!-- Newsletter Section -->
<section class="py-20 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto text-center" data-aos="fade-up">
      <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
        Fique por dentro das novidades
      </h2>
      <p class="text-xl text-gray-600 mb-8">
        Receba nossos artigos técnicos e dicas de engenharia diretamente no seu email
      </p>
      
      <form class="max-w-md mx-auto" data-validate>
        <div class="flex flex-col sm:flex-row gap-4">
          <input 
            type="email" 
            placeholder="Seu email"
            class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            data-validate="required email"
          >
          <button 
            type="submit"
            class="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors duration-200 font-semibold"
          >
            Inscrever-se
          </button>
        </div>
      </form>
      
      <p class="text-sm text-gray-500 mt-4">
        Não enviamos spam. Você pode cancelar a inscrição a qualquer momento.
      </p>
    </div>
  </div>
</section>

<!-- JavaScript para filtros -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  const categoryButtons = document.querySelectorAll('.category-btn');
  const articleItems = document.querySelectorAll('.article-item');

  categoryButtons.forEach(button => {
    button.addEventListener('click', function() {
      // Remove active class from all buttons
      categoryButtons.forEach(btn => {
        btn.classList.remove('active', 'bg-primary-600', 'text-white');
        btn.classList.add('bg-gray-100', 'text-gray-700');
      });

      // Add active class to clicked button
      this.classList.add('active', 'bg-primary-600', 'text-white');
      this.classList.remove('bg-gray-100', 'text-gray-700');

      // Filter articles
      const category = this.getAttribute('data-category');
      
      articleItems.forEach(item => {
        if (category === 'all' || item.getAttribute('data-category') === category) {
          item.style.display = 'block';
          item.style.animation = 'fadeIn 0.5s ease-in-out';
        } else {
          item.style.display = 'none';
        }
      });
    });
  });
});
</script>

<style>
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 
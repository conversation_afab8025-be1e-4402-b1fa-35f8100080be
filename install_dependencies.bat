@echo off
echo Instalando dependencias para o removedor de fundo...
echo.

REM Verifica se o Python está instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: Python nao encontrado!
    echo Por favor, instale o Python primeiro: https://python.org
    pause
    exit /b 1
)

echo Python encontrado. Instalando bibliotecas...
echo.

REM Instala as dependências
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ERRO: Falha ao instalar dependencias!
    echo Tente executar manualmente: pip install rembg pillow
    pause
    exit /b 1
)

echo.
echo ✅ Dependencias instaladas com sucesso!
echo.
echo Agora voce pode executar o script:
echo python remove_background.py
echo.
pause 
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<script async type="module">
    import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
</script>
<script async src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.25.0/prism.min.js" integrity="sha512-hpZ5pDCF2bRCweL5WoA0/N1elet1KYL5mx3LP555Eg/0ZguaHawxNvEjF6O3rufAChs16HVNhEc6blF/rZoowQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script async src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.25.0/plugins/autoloader/prism-autoloader.min.js" integrity="sha512-sv0slik/5O0JIPdLBCR2A3XDg/1U3WuDEheZfI/DI5n8Yqc3h5kjrnr46FGBNiUAJF7rE4LHKwQ/SoSLRKAxEA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

{%include "components/calloutScript.njk"%}

<script async src="https://fastly.jsdelivr.net/npm/force-graph@1.43.0/dist/force-graph.min.js"></script>

<script async src="https://fastly.jsdelivr.net/npm/@alpinejs/persist@3.11.1/dist/cdn.min.js"></script>
<script src="https://fastly.jsdelivr.net/npm/alpinejs@3.11.1/dist/cdn.min.js" async></script>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.25.0/themes/prism-okaidia.min.css" integrity="sha512-mIs9kKbaw6JZFfSuo+MovjU+Ntggfoj8RwAmJbVXQ5mkAX5LlgETQEweFPI18humSPHymTb5iikEOKWF7I8ncQ==" crossorigin="anonymous" referrerpolicy="no-referrer" async/>
<script src="https://fastly.jsdelivr.net/npm/whatwg-fetch@3.6.2/dist/fetch.umd.min.js" crossorigin="anonymous" referrerpolicy="no-referrer" async></script>

<link href="/styles/digital-garden-base.css" rel="stylesheet">
{%-if meta.themeStyle%}
    <link href="/styles/obsidian-base.css" rel="stylesheet">
    <link href="{{meta.themeStyle}}" rel="stylesheet">
{% else %}
    <link href="/styles/style.css" rel="stylesheet">
{%endif%}

<link href="/styles/custom-style.css" rel="stylesheet">
{%- for style in dynamics.styles -%}
<link href="{{style}}" rel="stylesheet">
{%- endfor -%}



{% if metatags %}
    {% for name, content in metatags %}
        <meta name="{{ name }}" content="{{ content }}">
    {% endfor %}
{% endif %}

{% if meta.styleSettingsCss %}
    <style>
        {{ meta.styleSettingsCss | safe }}
    </style>
{% endif %}
<style>
</style>
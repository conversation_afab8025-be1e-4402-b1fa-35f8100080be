/* Tailwind CSS Directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
:root {
  --background-primary: rgb(32, 31, 31);
  --background-secondary: rgb(57, 56, 56);
  --text-normal: #dcddde;
  --text-accent: rgb(97, 186, 245);
  --file-margins: 32px;
  
  /* Enhanced gradients for modern look */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --gradient-dark: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-light: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

/* Dark mode variables */
[data-theme="dark"] {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #404040;
  --text-primary: #ffffff;
  --text-secondary: #e5e5e5;
  --text-tertiary: #a0a0a0;
  --border-color: #404040;
  --shadow-color: rgba(0, 0, 0, 0.5);
}

/* Light mode variables */
[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --text-primary: #111827;
  --text-secondary: #374151;
  --text-tertiary: #6b7280;
  --border-color: #e5e7eb;
  --shadow-color: rgba(0, 0, 0, 0.1);
}

h1 {
  color: #ffef60;
}

h2 {
  color: #f06449;
}

h3 {
  color: #d4fcc3;
}

h4 {
  color: #72dcff;
}

/* Dark mode styles */
[data-theme="dark"] {
  /* Background colors */
  .bg-white {
    background-color: var(--bg-primary);
  }
  
  .bg-gray-50 {
    background-color: var(--bg-secondary);
  }
  
  .bg-gray-100 {
    background-color: var(--bg-tertiary);
  }
  
  /* Text colors */
  .text-gray-900 {
    color: var(--text-primary);
  }
  
  .text-gray-700 {
    color: var(--text-secondary);
  }
  
  .text-gray-600 {
    color: var(--text-tertiary);
  }
  
  .text-gray-500 {
    color: var(--text-tertiary);
  }
  
  /* Border colors */
  .border-gray-200 {
    border-color: var(--border-color);
  }
  
  .border-gray-300 {
    border-color: var(--border-color);
  }
  
  /* Shadow adjustments */
  .shadow-lg {
    box-shadow: 0 10px 15px -3px var(--shadow-color), 0 4px 6px -2px var(--shadow-color);
  }
  
  .shadow-xl {
    box-shadow: 0 20px 25px -5px var(--shadow-color), 0 10px 10px -5px var(--shadow-color);
  }
  
  /* Header adjustments */
  .backdrop-blur-md {
    backdrop-filter: blur(12px);
    background-color: rgba(26, 26, 26, 0.8);
  }
  
  /* Footer adjustments */
  .bg-gray-900 {
    background-color: #111111;
  }
  
  /* Card adjustments */
  .service-card,
  .metric-card,
  .project-card {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
  }
  
  /* Search modal adjustments */
  .bg-black\/50 {
    background-color: rgba(0, 0, 0, 0.7);
  }
  
  /* Notification adjustments */
  .notification {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
  }
}

/* Enhanced gradient utilities */
.bg-gradient-primary {
  background: var(--gradient-primary);
}

.bg-gradient-secondary {
  background: var(--gradient-secondary);
}

.bg-gradient-accent {
  background: var(--gradient-accent);
}

.bg-gradient-success {
  background: var(--gradient-success);
}

.bg-gradient-hero {
  background: var(--gradient-hero);
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced shadows */
.shadow-glow {
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

.shadow-glow-secondary {
  box-shadow: 0 0 20px rgba(240, 147, 251, 0.3);
}

/* Animated backgrounds */
.bg-animated {
  background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Mobile overflow fix */
@media (max-width: 768px) {
  html, body {
    overflow-x: hidden;
    width: 100%;
    max-width: 100%;
  }
  
  .hero-section {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
  }
  
  #hero-video {
    width: 100%;
    max-width: 100vw;
  }
} 
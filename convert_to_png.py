#!/usr/bin/env python3
"""
Script para converter a imagem sem fundo para PNG
PNG é o formato ideal para logos com transparência
"""

import os
from pathlib import Path
from PIL import Image

def convert_to_png(input_path, output_path=None):
    """
    Converte a imagem para PNG mantendo a transparência
    
    Args:
        input_path (str): Caminho para a imagem de entrada
        output_path (str): Caminho para salvar a imagem PNG (opcional)
    """
    
    # Verifica se o arquivo de entrada existe
    if not os.path.exists(input_path):
        print(f"❌ Arquivo não encontrado: {input_path}")
        return False
    
    # Define o caminho de saída se não fornecido
    if output_path is None:
        input_file = Path(input_path)
        output_path = input_file.parent / f"{input_file.stem}.png"
    
    try:
        print(f"🔄 Convertendo para PNG: {input_path}")
        
        # Abre a imagem
        with Image.open(input_path) as img:
            # Converte para RGBA se necessário (para garantir transparência)
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # Salva como PNG
            img.save(output_path, 'PNG', optimize=True)
        
        print(f"✅ Conversão concluída!")
        print(f"📁 PNG salvo em: {output_path}")
        
        # Mostra informações sobre a imagem
        with Image.open(output_path) as img:
            print(f"📏 Dimensões: {img.size[0]}x{img.size[1]} pixels")
            print(f"🎨 Modo: {img.mode}")
            print(f"💾 Tamanho do arquivo: {os.path.getsize(output_path) / 1024:.1f} KB")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na conversão: {str(e)}")
        return False

def main():
    """Função principal do script"""
    
    print("🔄 Conversor para PNG - Logo PVP")
    print("=" * 40)
    
    # Caminho para a imagem sem fundo
    input_image = "src/site/assets/favicon/pvp_sem_fundo.jpg"
    
    # Verifica se o arquivo existe
    if not os.path.exists(input_image):
        print(f"❌ Arquivo não encontrado: {input_image}")
        print("Execute primeiro: python remove_background.py")
        return
    
    # Converte para PNG
    success = convert_to_png(input_image)
    
    if success:
        print("\n🎉 Conversão concluída!")
        print("Agora você tem:")
        print("✅ JPG sem fundo: pvp_sem_fundo.jpg")
        print("✅ PNG com transparência: pvp_sem_fundo.png")
        print("\n💡 Use o arquivo PNG para:")
        print("- Sites web (melhor compatibilidade)")
        print("- Impressão profissional")
        print("- Logos em documentos")
        print("- Qualquer uso que precise de transparência")
    else:
        print("\n❌ Falha na conversão. Verifique os erros acima.")

if __name__ == "__main__":
    main() 
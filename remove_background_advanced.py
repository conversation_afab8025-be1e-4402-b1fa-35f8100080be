#!/usr/bin/env python3
"""
Script avançado para remover o fundo da imagem do logo PVP
Usa técnicas específicas para garantir transparência real
"""

import os
import sys
from pathlib import Path
import numpy as np

try:
    from rembg import remove
    from PIL import Image, ImageFilter
except ImportError as e:
    print(f"Erro: Biblioteca não encontrada. Instalando dependências...")
    print(f"Execute: pip install rembg pillow")
    sys.exit(1)

def remove_background_advanced(input_path, output_path=None):
    """
    Remove o fundo usando técnicas avançadas para garantir transparência
    """
    
    if not os.path.exists(input_path):
        print(f"❌ Arquivo não encontrado: {input_path}")
        return False
    
    if output_path is None:
        input_file = Path(input_path)
        output_path = input_file.parent / f"{input_file.stem}_transparente.png"
    
    try:
        print(f"🎨 Processando com técnicas avançadas: {input_path}")
        
        # Lê a imagem original
        with Image.open(input_path) as img:
            # Converte para RGBA se necessário
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            print(f"📏 Dimensões originais: {img.size}")
            
            # Converte para array numpy para processamento
            img_array = np.array(img)
            
            # Detecta pixels pretos/escuros (fundo)
            # Considera como fundo pixels com baixo valor em todos os canais
            r, g, b, a = img_array[:, :, 0], img_array[:, :, 1], img_array[:, :, 2], img_array[:, :, 3]
            
            # Cria máscara para fundo (pixels escuros)
            background_mask = (r < 50) & (g < 50) & (b < 50)
            
            # Aplica transparência ao fundo
            img_array[background_mask, 3] = 0  # Define alpha como 0 para fundo
            
            # Converte de volta para PIL
            transparent_img = Image.fromarray(img_array, 'RGBA')
            
            # Aplica um leve blur nas bordas para suavizar
            transparent_img = transparent_img.filter(ImageFilter.GaussianBlur(radius=0.5))
            
            # Salva como PNG
            transparent_img.save(output_path, 'PNG', optimize=True)
        
        print(f"✅ Fundo removido com técnicas avançadas!")
        print(f"📁 Imagem salva em: {output_path}")
        
        # Verifica o resultado
        with Image.open(output_path) as img:
            print(f"📏 Dimensões finais: {img.size[0]}x{img.size[1]} pixels")
            print(f"🎨 Modo: {img.mode}")
            
            # Verifica se há transparência
            img_array = np.array(img)
            transparent_pixels = np.sum(img_array[:, :, 3] == 0)
            total_pixels = img_array.shape[0] * img_array.shape[1]
            transparency_percent = (transparent_pixels / total_pixels) * 100
            
            print(f"🔍 Pixels transparentes: {transparency_percent:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao processar: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_alternative_version(input_path, output_path=None):
    """
    Cria uma versão alternativa usando rembg com configurações específicas
    """
    
    if output_path is None:
        input_file = Path(input_path)
        output_path = input_file.parent / f"{input_file.stem}_alt.png"
    
    try:
        print("🔄 Criando versão alternativa...")
        
        # Lê a imagem
        with open(input_path, "rb") as f:
            input_data = f.read()
        
        # Remove fundo com configurações específicas para logos
        output_data = remove(
            input_data,
            model_name="u2net",  # Modelo mais preciso
            post_process_mask=True,
            alpha_matting=True,
            alpha_matting_foreground_threshold=240,
            alpha_matting_background_threshold=10,
            alpha_matting_erode_size=10
        )
        
        # Salva resultado
        with open(output_path, "wb") as f:
            f.write(output_data)
        
        print(f"✅ Versão alternativa criada: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ Erro na versão alternativa: {str(e)}")
        return False

def main():
    """Função principal"""
    
    print("🎨 Removedor Avançado de Fundo - Logo PVP")
    print("=" * 50)
    
    input_image = "src/site/assets/favicon/pvp.jpg"
    
    if not os.path.exists(input_image):
        print(f"❌ Arquivo não encontrado: {input_image}")
        return
    
    # Tenta método avançado
    success = remove_background_advanced(input_image)
    
    if success:
        # Cria também uma versão alternativa
        create_alternative_version(input_image)
        
        print("\n🎉 Processo concluído!")
        print("Criadas duas versões:")
        print("1. pvp_transparente.png - Método avançado")
        print("2. pvp_alt.png - Método alternativo")
        print("\n💡 Teste ambas as versões para ver qual ficou melhor!")
    else:
        print("\n❌ Falha no processamento avançado.")

if __name__ == "__main__":
    main() 
'use strict';
var e = require('obsidian');
class t {
  channel = new MessageChannel();
  channelSetupPromise;
  eventIndex = 0;
  handlers = {};
  constructor(e, t = '*') {
    const n = e.getIframe();
    if (n.hasAttribute('data-event-channel-setup'))
      throw new Error(
        'An embed viewer instance already initialized on the iframe!'
      );
    (n.setAttribute('data-event-channel-setup', 'true'),
      (this.channelSetupPromise = (async () => {
        await new Promise(e => {
          n.addEventListener('load', () => {
            this.channel.port1.start();
            const s = t => {
              const [n] = t.data;
              'channel-ready' === n &&
                (t.preventDefault(),
                this.channel.port1.removeEventListener('message', s),
                this.channel.port1.addEventListener(
                  'message',
                  this.eventDispatcher.bind(this)
                ),
                e(void 0));
            };
            (this.channel.port1.addEventListener('message', s),
              n.contentWindow?.postMessage(
                ['setup-channel', { port: this.channel.port2 }],
                t || '*',
                [this.channel.port2]
              ));
          });
        });
      })()));
  }
  eventDispatcher(e) {
    const [t, n, s] = e.data || [];
    'event' === t &&
      n &&
      this.handlers[n] &&
      this.handlers[n].forEach(e => e(s));
  }
  addEventListener(e, t) {
    ((this.handlers[e] = this.handlers[e] || []),
      this.handlers[e].includes(t) || this.handlers[e].push(t));
  }
  removeEventListener(e, t) {
    if (!this.handlers[e]) return;
    const n = this.handlers[e].findIndex(e => e === t);
    this.handlers[e].splice(n, 1);
  }
  async emit(e, t) {
    await this.channelSetupPromise;
    const n = 'xmind-embed-viewer#' + this.eventIndex++;
    await new Promise(s => {
      const r = e => {
        const [t, i] = e.data;
        t === n && (this.channel.port1.removeEventListener('message', r), s(i));
      };
      (this.channel.port1.addEventListener('message', r),
        this.channel.port1.postMessage([e, t, n]));
    });
  }
}
class n {
  iframe;
  constructor(e, t) {
    let n;
    const s = 'string' == typeof e ? document.querySelector(e) : e;
    if (null === s)
      throw new Error('IFrame or mount element not found by selector ' + e);
    (s instanceof HTMLIFrameElement
      ? (n = s)
      : ((n = document.createElement('iframe')), s.appendChild(n)),
      n.setAttribute('frameborder', '0'),
      n.setAttribute('scrolling', 'no'),
      n.setAttribute('allowfullscreen', 'true'),
      n.setAttribute('allow', 'allowfullscreen'),
      n.setAttribute('crossorigin', 'anonymous'),
      n.setAttribute('src', t),
      (this.iframe = n));
  }
  getIframe() {
    return this.iframe;
  }
  setStyles(e) {
    const t = this.getIframe();
    for (const [n, s] of Object.entries(e)) t.style[n] = s;
  }
}
class s {
  iframeController;
  iframeEventChannelController;
  internalState = { sheets: [], zoomScale: 100, currentSheetId: '' };
  region;
  constructor(e) {
    const {
        file: s,
        el: r,
        region: i = 'global',
        styles: a = { height: '350px', width: '750px' },
        isPitchModeDisabled: l,
      } = e,
      o = 'cn' === i ? 'www.xmind.cn' : 'www.xmind.app',
      h = new n(
        r,
        `https://${o}/embed-viewer${l ? '?pitch-mode=disabled' : ''}`
      ),
      c = new t(h, `https://${o}`);
    ((this.iframeController = h),
      (this.iframeEventChannelController = c),
      (this.region = i),
      c.addEventListener(
        'sheet-switch',
        e => (this.internalState.currentSheetId = e)
      ),
      c.addEventListener(
        'zoom-change',
        e => (this.internalState.zoomScale = e)
      ),
      c.addEventListener('sheets-load', e => (this.internalState.sheets = e)),
      this.iframeController.setStyles(a),
      s && this.load(s));
  }
  addEventListener(e, t) {
    this.iframeEventChannelController.addEventListener(e, t);
  }
  removeEventListener(e, t) {
    this.iframeEventChannelController.removeEventListener(e, t);
  }
  setStyles(e) {
    this.iframeController.setStyles(e);
  }
  load(e) {
    this.iframeEventChannelController.emit('open-file', e);
  }
  setZoomScale(e) {
    this.iframeEventChannelController.emit('zoom', e);
  }
  setFitMap() {
    this.iframeEventChannelController.emit('fit-map');
  }
  switchSheet(e) {
    this.iframeEventChannelController.emit('switch-sheet', e);
  }
  get zoom() {
    return this.internalState.zoomScale;
  }
  get sheets() {
    return JSON.parse(JSON.stringify(this.internalState.sheets));
  }
  get currentSheetId() {
    return this.internalState.currentSheetId;
  }
}
class r extends e.FileView {
  plugin;
  styles;
  constructor(e, t, n) {
    (super(e),
      (this.app = t),
      (this.plugin = n),
      (this.styles = { width: '100%', height: '100%', border: 'none' }));
  }
  getViewType() {
    return 'xmind-viewer';
  }
  getIcon() {
    return 'brain';
  }
  getDisplayText() {
    return this.file ? `XMind view: ${this.file.basename}` : 'No file open';
  }
  async onLoadFile(e) {
    const t = await this.app.vault.readBinary(e);
    new s({
      el: this.contentEl,
      file: t,
      region: 'global',
      styles: this.styles,
    });
  }
}
const i = 'xmind-viewer';
class a extends e.Plugin {
  onload() {
    (this.registerView(i, e => new r(e, this.app, this)),
      this.registerExtensions(['xmind'], i));
  }
}
module.exports = a;

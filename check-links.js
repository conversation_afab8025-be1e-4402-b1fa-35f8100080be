const fs = require('fs');
const path = require('path');

// Função para verificar se um arquivo existe
function fileExists(filePath) {
  return fs.existsSync(filePath);
}

// Função para extrair links de PDFs de um arquivo Markdown
function extractPdfLinks(content) {
  const pdfLinks = [];
  const linkRegex = /\[([^\]]+)\]\(\/assets\/pdfs\/([^)]+)\)/g;
  const objectRegex = /data="\/assets\/pdfs\/([^"]+)"/g;
  
  let match;
  while ((match = linkRegex.exec(content)) !== null) {
    pdfLinks.push({
      type: 'link',
      text: match[1],
      path: match[2]
    });
  }
  
  while ((match = objectRegex.exec(content)) !== null) {
    pdfLinks.push({
      type: 'object',
      text: 'PDF Viewer',
      path: match[1]
    });
  }
  
  return pdfLinks;
}

// Função principal para verificar links
function checkPdfLinks() {
  const notesDir = './src/site/notes';
  const pdfsDir = './src/site/assets/pdfs';
  let totalLinks = 0;
  let brokenLinks = 0;
  
  console.log('🔍 Verificando links de PDFs...\n');
  
  // Percorrer todos os arquivos .md
  function walkDir(dir) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        walkDir(filePath);
      } else if (file.endsWith('.md')) {
        const content = fs.readFileSync(filePath, 'utf8');
        const links = extractPdfLinks(content);
        
        if (links.length > 0) {
          console.log(`📄 ${filePath}:`);
          
          links.forEach(link => {
            totalLinks++;
            const fullPdfPath = path.join(pdfsDir, link.path);
            
            if (fileExists(fullPdfPath)) {
              console.log(`  ✅ ${link.text} -> ${link.path}`);
            } else {
              console.log(`  ❌ ${link.text} -> ${link.path} (ARQUIVO NÃO ENCONTRADO)`);
              brokenLinks++;
            }
          });
          console.log('');
        }
      }
    });
  }
  
  walkDir(notesDir);
  
  console.log(`📊 RESUMO:`);
  console.log(`  Total de links: ${totalLinks}`);
  console.log(`  Links quebrados: ${brokenLinks}`);
  console.log(`  Links funcionando: ${totalLinks - brokenLinks}`);
  console.log(`  Taxa de sucesso: ${((totalLinks - brokenLinks) / totalLinks * 100).toFixed(1)}%`);
  
  if (brokenLinks > 0) {
    console.log(`\n⚠️  ATENÇÃO: ${brokenLinks} links quebrados encontrados!`);
    process.exit(1);
  } else {
    console.log(`\n🎉 Todos os links estão funcionando!`);
  }
}

// Executar verificação
checkPdfLinks(); 
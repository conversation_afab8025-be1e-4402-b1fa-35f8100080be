{"githubRepo": "PVP-Portifolio-main", "githubToken": "*********************************************************************************************", "githubUserName": "pvthemaster2", "gardenBaseUrl": "pvpportifolio.netlify.app/", "prHistory": ["https://github.com/PvTheMaster2/OBSIDIAN-TCC-R00/pull/1"], "baseTheme": "dark", "theme": "{\"name\":\"Zen\",\"author\":\"laughmaker\",\"repo\":\"laughmaker/Zen\",\"screenshot\":\"cover.png\",\"modes\":[\"dark\",\"light\"],\"cssUrl\":\"https://raw.githubusercontent.com/laughmaker/Zen/HEAD/theme.css\"}", "faviconPath": "", "useFullResolutionImages": false, "noteSettingsIsInitialized": true, "siteName": "<PERSON><PERSON><PERSON><PERSON>", "mainLanguage": "en", "slugifyEnabled": false, "noteIconKey": "dg-note-icon", "defaultNoteIcon": "", "showNoteIconOnTitle": false, "showNoteIconInFileTree": true, "showNoteIconOnInternalLink": true, "showNoteIconOnBackLink": false, "showCreatedTimestamp": false, "createdTimestampKey": "", "showUpdatedTimestamp": false, "updatedTimestampKey": "", "timestampFormat": "MMM dd, yyyy h:mm a", "styleSettingsCss": "", "styleSettingsBodyClasses": "", "pathRewriteRules": "", "customFilters": [], "contentClassesKey": "dg-content-classes", "defaultNoteSettings": {"dgHomeLink": true, "dgPassFrontmatter": false, "dgShowBacklinks": true, "dgShowLocalGraph": true, "dgShowInlineTitle": true, "dgShowFileTree": true, "dgEnableSearch": true, "dgShowToc": true, "dgLinkPreview": false, "dgShowTags": true}}
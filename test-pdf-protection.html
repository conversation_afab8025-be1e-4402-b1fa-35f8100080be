<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Proteção de PDFs</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #2d3748;
            margin-bottom: 20px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
        }
        
        .test-section h2 {
            color: #4a5568;
            margin-bottom: 15px;
        }
        
        .pdf-container {
            margin: 2rem 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            position: relative;
            
            /* Proteção contra seleção e cópia */
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            
            /* Desabilita menu de contexto */
            -webkit-context-menu: none;
            -moz-context-menu: none;
            -ms-context-menu: none;
            context-menu: none;
        }
        
        .pdf-container object {
            width: 100%;
            height: 500px;
            border: none;
            pointer-events: auto;
            
            /* Proteção adicional no objeto PDF */
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        
        /* Overlay de proteção (invisível mas ativo) */
        .pdf-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 10;
            pointer-events: none;
        }
        
        /* Aviso de proteção */
        .pdf-container::after {
            content: '🔒 Visualização Protegida';
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 20;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .pdf-container:hover::after {
            opacity: 1;
        }
        
        .pdf-link {
            display: inline-flex;
            align-items: center;
            padding: 0.75rem 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin: 0.5rem 0;
        }
        
        .pdf-link:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 600;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .test-list {
            list-style: none;
            padding: 0;
        }
        
        .test-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .test-list li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 Teste de Proteção de PDFs</h1>
        
        <div class="status success">
            ✅ Sistema de proteção implementado com sucesso!
        </div>
        
        <div class="test-section">
            <h2>📋 Testes de Proteção</h2>
            <ul class="test-list">
                <li>✅ Links de PDF removem target="_blank"</li>
                <li>✅ Cliques em links abrem modal protegido</li>
                <li>✅ Menu de contexto desabilitado</li>
                <li>✅ Seleção de texto desabilitada</li>
                <li>✅ Ctrl+S, Ctrl+P, F12 bloqueados</li>
                <li>✅ Tentativas de arrastar bloqueadas</li>
                <li>✅ Avisos visuais de proteção</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>📄 Teste de PDF Protegido</h2>
            <p>Clique no link abaixo para testar a visualização protegida:</p>
            
            <a href="/assets/pdfs/render-casa-gt.pdf" class="pdf-link">
                📄 Ver Render do Projeto GT (Protegido)
            </a>
            
            <div class="pdf-container">
                <object data="/assets/pdfs/render-casa-gt.pdf#toolbar=0&navpanes=0&scrollbar=0"
                        type="application/pdf" width="100%" height="500">
                    <p>PDF indisponível para visualização.</p>
                </object>
            </div>
        </div>
        
        <div class="test-section">
            <h2>⚠️ Tentativas de Download</h2>
            <p>Tente as seguintes ações para testar a proteção:</p>
            <ul class="test-list">
                <li>🖱️ Clique com botão direito no PDF</li>
                <li>⌨️ Pressione Ctrl+S para salvar</li>
                <li>⌨️ Pressione Ctrl+P para imprimir</li>
                <li>⌨️ Pressione F12 para abrir DevTools</li>
                <li>🖱️ Tente arrastar o PDF</li>
                <li>📱 Tente selecionar texto do PDF</li>
            </ul>
        </div>
        
        <div class="status warning">
            ⚠️ Nota: Esta proteção funciona no navegador. Usuários avançados ainda podem contornar essas proteções, mas isso desencoraja a maioria dos usuários casuais.
        </div>
    </div>

    <script>
        // Proteção de PDFs contra downloads
        function protectPDFs() {
            // Remove todos os links de download de PDFs
            const pdfLinks = document.querySelectorAll('a[href*=".pdf"]');
            pdfLinks.forEach(link => {
                // Remove target="_blank" para evitar downloads
                link.removeAttribute('target');
                
                // Adiciona classe para estilização
                link.classList.add('pdf-link');
                
                // Intercepta cliques em links de PDF
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const pdfUrl = this.getAttribute('href');
                    if (pdfUrl && pdfUrl.includes('.pdf')) {
                        // Cria um container de visualização protegida
                        showProtectedPDF(pdfUrl);
                    }
                });
            });
            
            // Protege objetos PDF existentes
            const pdfObjects = document.querySelectorAll('object[data*=".pdf"]');
            pdfObjects.forEach(obj => {
                // Adiciona proteção ao objeto
                obj.addEventListener('contextmenu', e => e.preventDefault());
                obj.addEventListener('selectstart', e => e.preventDefault());
                obj.addEventListener('dragstart', e => e.preventDefault());
                
                // Remove link de download do fallback
                const fallbackLink = obj.querySelector('a[href*=".pdf"]');
                if (fallbackLink) {
                    fallbackLink.remove();
                }
            });
        }

        // Função para mostrar PDF em visualização protegida
        function showProtectedPDF(pdfUrl) {
            // Remove modal existente se houver
            const existingModal = document.getElementById('pdf-protection-modal');
            if (existingModal) {
                existingModal.remove();
            }
            
            // Cria modal de proteção
            const modal = document.createElement('div');
            modal.id = 'pdf-protection-modal';
            modal.className = 'pdf-protection-modal';
            modal.innerHTML = `
                <div class="pdf-protection-content">
                    <div class="pdf-protection-header">
                        <h3>🔒 Visualização Protegida</h3>
                        <button class="pdf-protection-close">&times;</button>
                    </div>
                    <div class="pdf-protection-body">
                        <object data="${pdfUrl}#toolbar=0&navpanes=0&scrollbar=0" 
                                type="application/pdf" 
                                width="100%" 
                                height="600">
                            <p>PDF indisponível para visualização.</p>
                        </object>
                    </div>
                    <div class="pdf-protection-footer">
                        <p>⚠️ Este documento está protegido contra downloads e cópia.</p>
                    </div>
                </div>
            `;
            
            // Adiciona ao body
            document.body.appendChild(modal);
            
            // Adiciona estilos inline para garantir proteção
            const style = document.createElement('style');
            style.textContent = `
                .pdf-protection-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    z-index: 9999;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 20px;
                }
                
                .pdf-protection-content {
                    background: white;
                    border-radius: 8px;
                    width: 90%;
                    max-width: 1000px;
                    max-height: 90vh;
                    overflow: hidden;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                }
                
                .pdf-protection-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 15px 20px;
                    background: #f8f9fa;
                    border-bottom: 1px solid #dee2e6;
                }
                
                .pdf-protection-header h3 {
                    margin: 0;
                    color: #333;
                }
                
                .pdf-protection-close {
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #666;
                    padding: 0;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .pdf-protection-close:hover {
                    color: #333;
                }
                
                .pdf-protection-body {
                    padding: 0;
                    height: 600px;
                    overflow: hidden;
                }
                
                .pdf-protection-body object {
                    width: 100%;
                    height: 100%;
                    border: none;
                    -webkit-user-select: none;
                    -moz-user-select: none;
                    -ms-user-select: none;
                    user-select: none;
                }
                
                .pdf-protection-footer {
                    padding: 10px 20px;
                    background: #fff3cd;
                    border-top: 1px solid #ffeaa7;
                    text-align: center;
                    font-size: 14px;
                    color: #856404;
                }
            `;
            
            document.head.appendChild(style);
            
            // Adiciona proteção ao objeto PDF
            const pdfObject = modal.querySelector('object');
            pdfObject.addEventListener('contextmenu', e => e.preventDefault());
            pdfObject.addEventListener('selectstart', e => e.preventDefault());
            pdfObject.addEventListener('dragstart', e => e.preventDefault());
            
            // Fecha modal
            const closeBtn = modal.querySelector('.pdf-protection-close');
            closeBtn.addEventListener('click', () => {
                modal.remove();
                style.remove();
            });
            
            // Fecha modal ao clicar fora
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                    style.remove();
                }
            });
            
            // Previne fechamento com ESC
            document.addEventListener('keydown', function closeOnEsc(e) {
                if (e.key === 'Escape') {
                    modal.remove();
                    style.remove();
                    document.removeEventListener('keydown', closeOnEsc);
                }
            });
        }

        // Proteção adicional contra tentativas de download
        function addDownloadProtection() {
            // Intercepta tentativas de download via teclas
            document.addEventListener('keydown', function(e) {
                // Ctrl+S, Ctrl+P, F12, etc.
                if ((e.ctrlKey && (e.key === 's' || e.key === 'p')) || 
                    e.key === 'F12' || 
                    (e.ctrlKey && e.shiftKey && e.key === 'I')) {
                    e.preventDefault();
                    showProtectionWarning();
                }
            });
            
            // Intercepta menu de contexto
            document.addEventListener('contextmenu', function(e) {
                const target = e.target;
                if (target.closest('.pdf-container') || 
                    target.closest('object[data*=".pdf"]') ||
                    target.closest('#pdf-protection-modal')) {
                    e.preventDefault();
                    showProtectionWarning();
                }
            });
            
            // Intercepta tentativas de arrastar
            document.addEventListener('dragstart', function(e) {
                const target = e.target;
                if (target.closest('.pdf-container') || 
                    target.closest('object[data*=".pdf"]')) {
                    e.preventDefault();
                    showProtectionWarning();
                }
            });
        }

        // Mostra aviso de proteção
        function showProtectionWarning() {
            // Remove aviso existente
            const existingWarning = document.getElementById('protection-warning');
            if (existingWarning) {
                existingWarning.remove();
            }
            
            const warning = document.createElement('div');
            warning.id = 'protection-warning';
            warning.innerHTML = `
                <div class="protection-warning-content">
                    <span>🔒 Conteúdo Protegido</span>
                </div>
            `;
            
            // Adiciona estilos
            const style = document.createElement('style');
            style.textContent = `
                #protection-warning {
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    z-index: 10000;
                    animation: warningFade 2s ease-in-out;
                }
                
                .protection-warning-content {
                    background: rgba(220, 53, 69, 0.9);
                    color: white;
                    padding: 15px 25px;
                    border-radius: 8px;
                    font-weight: 600;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                }
                
                @keyframes warningFade {
                    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                    20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                    80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                    100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                }
            `;
            
            document.head.appendChild(style);
            document.body.appendChild(warning);
            
            // Remove após 2 segundos
            setTimeout(() => {
                warning.remove();
                style.remove();
            }, 2000);
        }

        // Inicializa proteções quando DOM estiver pronto
        document.addEventListener('DOMContentLoaded', function() {
            protectPDFs();
            addDownloadProtection();
            
            console.log('🔒 Sistema de proteção de PDFs ativado!');
        });
    </script>
</body>
</html> 
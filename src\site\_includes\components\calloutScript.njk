<script async src="https://cdn.jsdelivr.net/npm/lucide@0.115.0/dist/umd/lucide.min.js"></script>
<script>
    // Create callout icons
    window.addEventListener("load", () => {
        document.querySelectorAll(".callout").forEach((elem) => {
            const icon = getComputedStyle(elem).getPropertyValue('--callout-icon');
            const iconName = icon && icon.trim().replace(/^lucide-/, "");

            if (iconName) {
                const calloutTitle = elem.querySelector(".callout-title");

                if (calloutTitle) {
                    const calloutIconContainer = document.createElement("div");
                    const calloutIcon = document.createElement("i");

                    calloutIconContainer.appendChild(calloutIcon);
                    calloutIcon.setAttribute("icon-name", iconName);
                    calloutIconContainer.setAttribute("class", "callout-icon");
                    calloutTitle.insertBefore(calloutIconContainer, calloutTitle.firstChild);
                }
            }
        });

        lucide.createIcons();

        // Collapse callouts
        Array.from(document.querySelectorAll(".callout.is-collapsible")).forEach((elem) => {
            elem.querySelector('.callout-title').addEventListener("click", (event) => {
                if (elem.classList.contains("is-collapsed")) {
                    elem.classList.remove("is-collapsed");
                } else {
                    elem.classList.add("is-collapsed");
                }
            });
        });
    });
</script>

const projectsData = require('./src/site/_data/projects.js');

// Simular a função projectsByCategory
function getProjectsByCategory() {
  const allProjects = Object.values(projectsData.projects);
  const categories = {};
  
  allProjects.forEach(project => {
    if (!categories[project.category]) {
      categories[project.category] = [];
    }
    categories[project.category].push(project);
  });
  
  return categories;
}

const projectsByCategory = getProjectsByCategory();
const residenciais = projectsByCategory['residencial'];

console.log('=== TESTE TEMPLATE ===');
console.log(`Total de projetos residenciais: ${residenciais.length}`);

residenciais.forEach(project => {
  console.log(`\n- ${project.id}: ${project.title}`);
  console.log(`  coverImage: ${project.coverImage || 'NÃO DEFINIDA'}`);
  console.log(`  images: ${project.images ? project.images.length : 0} imagens`);
  
  // Simular o template
  if (project.coverImage) {
    console.log(`  ✅ Usará coverImage: ${project.coverImage}`);
  } else if (project.images && project.images[0]) {
    console.log(`  ✅ Usará images[0]: ${project.images[0].src}`);
  } else {
    console.log(`  ❌ Usará placeholder`);
  }
}); 
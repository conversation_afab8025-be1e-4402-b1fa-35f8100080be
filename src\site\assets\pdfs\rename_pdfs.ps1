# Script para renomear arquivos PDF seguindo padrão: tipo-categoria-empreendimento

Write-Host "Iniciando renomeacao de arquivos PDF..." -ForegroundColor Yellow

# Mapeamento de renomeação
$renames = @{
    # Comercial - Dom Pedrito
    "fc-d08-wolf-e301-r02-2pavimento_dom-pedrito_recorte_p1.pdf" = "2pavimento-comercial-dom-pedrito-recorte.pdf"
    "fc-d08-wolf-e301-r02-2pavimento_recorte_p1_dom-pedrito.pdf" = "2pavimento-comercial-dom-pedrito-recorte.pdf"
    "fc-d08-wolf-e401-r02-4pavimento_dom-pedrito.pdf" = "4pavimento-comercial-dom-pedrito.pdf"
    "fc-d08-wolf-e501-r02-5pavimento_recorte_p1_dom-pedrito.pdf" = "5pavimento-comercial-dom-pedrito-recorte.pdf"
    
    # Comercial - Zotti
    "fc-d13-e101-r04-terreo_recorte_p1_zotti.pdf" = "terreo-comercial-zotti-recorte.pdf"
    "fc-d13-e201-r05-segundo-pav_recorte_p1_zotti.pdf" = "2pavimento-comercial-zotti-recorte.pdf"
    
    # Comercial - Lojas Remião
    "fc-d30-e101-térreo-r02_lojas-remiao.pdf" = "terreo-comercial-lojas-remiao.pdf"
    "fc-d30-e101-térreo-r02_recorte_p1_lojas-remiao.pdf" = "terreo-comercial-lojas-remiao-recorte.pdf"
    "fc-d30-e201-mezanino-r02_lojas-remiao.pdf" = "mezanino-comercial-lojas-remiao.pdf"
    "fc-d30-e201-mezanino-r02_recorte_p1_lojas-remiao.pdf" = "mezanino-comercial-lojas-remiao-recorte.pdf"
    
    # Comercial - Loja João
    "fc-d33-e001-ent_energia-r00_recorte_p1_loja-joao.pdf" = "entrada-energia-comercial-loja-joao-recorte.pdf"
    "fc-d33-e201-r01_loja-joao.pdf" = "terreo-comercial-loja-joao.pdf"
    "fc-d33-e201-r01_recorte_p1_loja-joao.pdf" = "terreo-comercial-loja-joao-recorte.pdf"
    
    # Comercial - Sicredi
    "fc-d34-sureg-e311-ex-r00-diagramas_sicredi.pdf" = "diagramas-comercial-sicredi.pdf"
    
    # Comercial - Loja Avenida
    "fc-d35-e001-ent_energia-r01_recorte_p1_loja-avenida.pdf" = "entrada-energia-comercial-loja-avenida-recorte.pdf"
    "fc-d35-e102-segundo-pav-r02_loja-avenida.pdf" = "2pavimento-comercial-loja-avenida.pdf"
    "fc-d35-e102-segundo-pav-r02_recorte_p1_loja-avenida.pdf" = "2pavimento-comercial-loja-avenida-recorte.pdf"
    
    # Render - Loja João
    "render-loja-joao.pdf" = "render-comercial-loja-joao.pdf"
    
    # Predial Comercial - Rodrigo Empresa
    "pvp-a02-e003-r01-situação_predio-comercio.pdf" = "situacao-predial-comercio.pdf"
    "pvp-a02-e101-r02-térreo_predio-comercio.pdf" = "terreo-predial-comercio.pdf"
    "pvp-a02-e201-r02-2pav_recorte_p1_rodrigo-empresa.pdf" = "2pavimento-predial-rodrigo-empresa-recorte.pdf"
    "pvp-a02-e201-r02-2pav_rodrigo-empresa.pdf" = "2pavimento-predial-rodrigo-empresa.pdf"
    "pvp-a02-e401-r00-vertical_recorte_p1_rodrigo-empresa.pdf" = "vertical-predial-rodrigo-empresa-recorte.pdf"
    "pvp-a02-h201-r01-água-fria-e-reservatórios_predio-comercio.pdf" = "agua-fria-predial-comercio.pdf"
    
    # Casa Sítio
    "pvp-a03-e101-r02-terreo_casa-sitio.pdf" = "terreo-casa-sitio.pdf"
    "pvp-a03-e101-r02-terreo_recorte_p1_casa-sitio.pdf" = "terreo-casa-sitio-recorte.pdf"
    "pvp-a03-e201-r02-2pav_recorte_p1_casa-sitio.pdf" = "2pavimento-casa-sitio-recorte.pdf"
    "pvp-a03-h100-r02-água-quente-e-fria_recorte_p1_casa-sitio.pdf" = "agua-quente-fria-casa-sitio-recorte.pdf"
    "pvp-a03-h200-r02-pluvial-e-cloal_recorte_p1_casa-sitio.pdf" = "pluvial-cloacal-casa-sitio-recorte.pdf"
    
    # Marista
    "ros_ele_paaa_2-e3-_lev-2022-r02_recorte_p1_marista.pdf" = "2pavimento-comercial-marista-recorte.pdf"
    "ros_ele_paaa_ban4-_lev-2022-r02_marista.pdf" = "terreo-comercial-marista.pdf"
    "ros_ele_paaa_ban4-_lev-2022-r02_recorte_p1_marista.pdf" = "terreo-comercial-marista-recorte.pdf"
}

# Contadores
$renamed = 0
$skipped = 0
$errors = 0

Write-Host "Iniciando processo de renomeacao..." -ForegroundColor Cyan

foreach ($oldName in $renames.Keys) {
    $newName = $renames[$oldName]
    
    if (Test-Path $oldName) {
        try {
            Rename-Item -Path $oldName -NewName $newName -ErrorAction Stop
            Write-Host "OK $oldName -> $newName" -ForegroundColor Green
            $renamed++
        }
        catch {
            Write-Host "Erro ao renomear $oldName" -ForegroundColor Red
            $errors++
        }
    }
    else {
        Write-Host "Arquivo nao encontrado: $oldName" -ForegroundColor Yellow
        $skipped++
    }
}

Write-Host "`nResumo da operacao:" -ForegroundColor Cyan
Write-Host "Renomeados: $renamed arquivos" -ForegroundColor Green
Write-Host "Nao encontrados: $skipped arquivos" -ForegroundColor Yellow
Write-Host "Erros: $errors arquivos" -ForegroundColor Red

Write-Host "`nProcesso concluido!" -ForegroundColor Green 
<!-- Hero Section -->
<section class="bg-gradient-to-br from-primary-50 to-secondary-50 py-20">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center" data-aos="fade-up">
      <h1 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
        Projetos <span class="text-primary-600">{{ categoria | capitalize }}</span>
      </h1>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
        Portfólio de projetos {{ categoria }} da PVP Projects. 
        Cada projeto representa nossa dedicação à excelência técnica e inovação.
      </p>
      
      <!-- Breadcrumb -->
      <nav class="flex justify-center mb-8" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2 text-sm text-gray-600">
          <li><a href="/" class="hover:text-primary-600 transition-colors duration-200">Home</a></li>
          <li class="flex items-center">
            <svg class="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <a href="/projetos" class="hover:text-primary-600 transition-colors duration-200">Projetos</a>
          </li>
          <li class="flex items-center">
            <svg class="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-primary-600 font-medium">{{ categoria | capitalize }}</span>
          </li>
        </ol>
      </nav>
    </div>
  </div>
</section>

<!-- Lista de Projetos Filtrada -->
<section class="py-16 bg-gray-50">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Contador de resultados -->
    <div class="mb-8 text-center">
      <p class="text-gray-600">
        Mostrando <span id="project-count">{{ projectsByCategory[categoria] | length }}</span> projetos {{ categoria }}
      </p>
    </div>

    <!-- Grid de Projetos -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="projects-grid">
      {% for project in projectsByCategory[categoria] %}
      <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
        <!-- Imagem do Projeto -->
        <div class="relative h-48">
          {% if project.coverImage %}
          <img 
            src="{{ project.coverImage }}" 
            alt="{{ project.title }}"
            class="w-full h-full object-cover"
            loading="lazy"
          />
          {% elif project.images and project.images[0] %}
          <img 
            src="{{ project.images[0].src }}" 
            alt="{{ project.title }}"
            class="w-full h-full object-cover"
            loading="lazy"
          />
          {% else %}
          <div class="w-full h-full flex items-center justify-center bg-gray-300">
            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
          </div>
          {% endif %}
          <div class="absolute top-4 right-4">
            <span class="bg-primary-600 text-white px-2 py-1 rounded-full text-xs font-semibold">{{ project.category }}</span>
          </div>
        </div>

        <!-- Conteúdo do Card -->
        <div class="p-6">
          <div class="flex items-center justify-between mb-2">
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              {{ project.category | capitalize }}
            </span>
            <span class="text-sm text-gray-500">{{ project.year }}</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ project.title }}</h3>
          <p class="text-gray-600 mb-4">{{ project.description }}</p>
          
          <!-- Tags do Projeto -->
          {% if project.tags_extra %}
          <div class="flex flex-wrap gap-1 mb-4">
            {% for tag in project.tags_extra %}
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
              {{ tag }}
            </span>
            {% endfor %}
          </div>
          {% endif %}
          
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-500">{{ project.location }}</span>
            <a href="/projetos/{{ project.id }}" class="text-primary-600 hover:text-primary-700 font-medium">
              Ver detalhes →
            </a>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>

    <!-- Mensagem quando não há resultados -->
    {% if projectsByCategory[categoria] | length == 0 %}
    <div id="no-results" class="text-center py-12">
      <div class="text-gray-400 mb-4">
        <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33"></path>
        </svg>
      </div>
      <h3 class="text-xl font-semibold text-gray-900 mb-2">Nenhum projeto encontrado</h3>
      <p class="text-gray-600">Não há projetos {{ categoria }} disponíveis no momento.</p>
      <a href="/projetos" class="inline-block mt-4 bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200">
        Ver Todos os Projetos
      </a>
    </div>
    {% endif %}
  </div>
</section>

<!-- Script para funcionalidades adicionais -->
<script>
function showProjectModal(projectId) {
  // Implementar modal se necessário
  console.log('Mostrar modal para projeto:', projectId);
}
</script> 
---
layout: layouts/base.njk
title: Nossos Projetos
description: Explore nossa galeria completa de projetos de engenharia elétrica e hidrossanitária em todo o território nacional.
---

<!-- Hero Section -->
<section class="relative py-20 bg-gradient-to-br from-primary-50 to-secondary-50">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center" data-aos="fade-up">
      <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
        Nossos <span class="text-primary-600">Projetos</span>
      </h1>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Explore nossa galeria completa de projetos de engenharia elétrica e hidrossanitária
      </p>
    </div>
  </div>
</section>

<!-- Filtros Section -->
<section class="py-12 bg-white border-b border-gray-200">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex flex-wrap gap-4 justify-center" data-aos="fade-up">
      <button class="filter-btn active px-6 py-3 rounded-lg bg-primary-600 text-white font-medium transition-all duration-200" data-filter="all">
        Todos os Projetos
      </button>
      <button class="filter-btn px-6 py-3 rounded-lg bg-gray-100 text-gray-700 hover:bg-primary-600 hover:text-white font-medium transition-all duration-200" data-filter="residencial">
        Residencial
      </button>
      <button class="filter-btn px-6 py-3 rounded-lg bg-gray-100 text-gray-700 hover:bg-primary-600 hover:text-white font-medium transition-all duration-200" data-filter="comercial">
        Comercial
      </button>
      <button class="filter-btn px-6 py-3 rounded-lg bg-gray-100 text-gray-700 hover:bg-primary-600 hover:text-white font-medium transition-all duration-200" data-filter="predial">
        Predial
      </button>
      <button class="filter-btn px-6 py-3 rounded-lg bg-gray-100 text-gray-700 hover:bg-primary-600 hover:text-white font-medium transition-all duration-200" data-filter="educacional">
        Educacional
      </button>
    </div>
  </div>
</section>

<!-- Galeria de Projetos -->
<section class="py-20 bg-gray-50">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="projects-grid">
      {% for project in projects %}
      <div class="project-item project-card bg-white rounded-xl shadow-lg overflow-hidden cursor-pointer" data-category="{{ project.category }}" data-aos="fade-up" data-aos-delay="{{ loop.index * 100 }}">
        <div class="aspect-w-16 aspect-h-9">
          {% if project.coverImage %}
          <img src="{{ project.coverImage }}" alt="{{ project.title }}" class="w-full h-48 object-cover">
          {% elif project.images and project.images[0] %}
          <img src="{{ project.images[0].src }}" alt="{{ project.title }}" class="w-full h-48 object-cover">
          {% else %}
          <div class="w-full h-48 flex items-center justify-center bg-gray-300">
            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
          </div>
          {% endif %}
        </div>
        <div class="p-6">
          <div class="flex items-center justify-between mb-2">
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              {{ project.category | capitalize }}
            </span>
            <span class="text-sm text-gray-500">{{ project.year }}</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ project.title }}</h3>
          <p class="text-gray-600 mb-4">{{ project.description }}</p>
          
          <!-- Tags do Projeto -->
          {% if project.tags_extra %}
          <div class="flex flex-wrap gap-1 mb-4">
            {% for tag in project.tags_extra %}
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
              {{ tag }}
            </span>
            {% endfor %}
          </div>
          {% endif %}
          
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-500">{{ project.location }}</span>
            <a href="/projetos/{{ project.id }}" class="text-primary-600 hover:text-primary-700 font-medium">
              Ver detalhes →
            </a>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
</section>

<!-- Estatísticas Section -->
<section class="py-20 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Nossos Números</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Resultados que demonstram nossa experiência e qualidade
      </p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <div class="text-center" data-aos="fade-up" data-aos-delay="100">
        <div class="text-4xl font-bold text-primary-600 mb-2">{{ company.metrics.projects }}</div>
        <div class="text-gray-600">Projetos Concluídos</div>
      </div>
      
      <div class="text-center" data-aos="fade-up" data-aos-delay="200">
        <div class="text-4xl font-bold text-primary-600 mb-2">{{ company.metrics.area }}</div>
        <div class="text-gray-600">Área Projetada</div>
      </div>
      
      <div class="text-center" data-aos="fade-up" data-aos-delay="300">
        <div class="text-4xl font-bold text-primary-600 mb-2">{{ company.metrics.experience }}</div>
        <div class="text-gray-600">Anos de Experiência</div>
      </div>
      
      <div class="text-center" data-aos="fade-up" data-aos-delay="400">
        <div class="text-4xl font-bold text-primary-600 mb-2">{{ company.metrics.satisfaction }}</div>
        <div class="text-gray-600">Satisfação dos Clientes</div>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-primary-600">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <h2 class="text-3xl lg:text-4xl font-bold text-white mb-6" data-aos="fade-up">
      Pronto para seu próximo projeto?
    </h2>
    <p class="text-xl text-primary-100 mb-8 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="200">
      Vamos transformar sua visão em realidade com projetos de engenharia de alta qualidade
    </p>
    <div class="flex flex-col sm:flex-row gap-4 justify-center" data-aos="fade-up" data-aos-delay="400">
      <a href="/contato" class="bg-white text-primary-600 px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors duration-200 font-semibold text-lg inline-flex items-center justify-center">
        Solicitar Orçamento
      </a>
      <a href="/servicos" class="border-2 border-white text-white px-8 py-4 rounded-lg hover:bg-white hover:text-primary-600 transition-colors duration-200 font-semibold text-lg inline-flex items-center justify-center">
        Conhecer Serviços
      </a>
    </div>
  </div>
</section>

<!-- JavaScript para filtros -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  const filterButtons = document.querySelectorAll('.filter-btn');
  const projectItems = document.querySelectorAll('.project-item');

  filterButtons.forEach(button => {
    button.addEventListener('click', function() {
      // Remove active class from all buttons
      filterButtons.forEach(btn => {
        btn.classList.remove('active', 'bg-primary-600', 'text-white');
        btn.classList.add('bg-gray-100', 'text-gray-700');
      });

      // Add active class to clicked button
      this.classList.add('active', 'bg-primary-600', 'text-white');
      this.classList.remove('bg-gray-100', 'text-gray-700');

      // Filter projects
      const filter = this.getAttribute('data-filter');
      
      projectItems.forEach(item => {
        if (filter === 'all' || item.getAttribute('data-category') === filter) {
          item.style.display = 'block';
          item.style.animation = 'fadeIn 0.5s ease-in-out';
        } else {
          item.style.display = 'none';
        }
      });
    });
  });
});
</script>

<style>
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 
// Dados estáticos do carrossel - carregado automaticamente pelo Eleventy
module.exports = {
  projects: [
    {
      id: 'barao-uba',
      title: 'Edifício BdU',
      description: 'Edifício residencial multifamiliar — 11 pavimentos / 20 apartamentos / 2 subsolos',
      category: 'predial',
      coverImage: '/assets/imagens/render-barao.png',
      location: 'Porto Alegre',
      year: 2023,
      tags_extra: ['Elétrico', 'Predial', 'Revit', 'AutoCAD']
    },
    {
      id: 'dom-pedrito',
      title: 'Edifício DP',
      description: 'Edifício comercial com projeto elétrico completo',
      category: 'predial',
      coverImage: '/assets/imagens/render-dp.png',
      location: 'Porto Alegre',
      year: 2023,
      tags_extra: ['Elétrico', 'Predial', 'Revit']
    },
    {
      id: 'lojas-remiao',
      title: 'Lojas Remião',
      description: 'Projeto elétrico para rede de lojas',
      category: 'comercial',
      coverImage: '/assets/imagens/render-lr.png',
      location: 'Porto Alegre',
      year: 2023,
      tags_extra: ['Elétrico', 'Comercial', 'AutoCAD']
    },
    {
      id: 'sicredi',
      title: 'Sicredi',
      description: 'Projeto elétrico para agência bancária',
      category: 'comercial',
      coverImage: '/assets/imagens/render-si.png',
      location: 'Porto Alegre',
      year: 2023,
      tags_extra: ['Elétrico', 'Comercial', 'Revit']
    },
    {
      id: 'casa-sitio',
      title: 'Casa Sítio',
      description: 'Projeto elétrico e hidrossanitário residencial',
      category: 'residencial',
      coverImage: '/assets/imagens/render-cs.png',
      location: 'Porto Alegre',
      year: 2023,
      tags_extra: ['Elétrico', 'Residencial', 'Revit']
    },
    {
      id: 'casa-gp',
      title: 'Casa GP',
      description: 'Projeto elétrico residencial',
      category: 'residencial',
      coverImage: '/assets/imagens/render-gp.png',
      location: 'Porto Alegre',
      year: 2023,
      tags_extra: ['Elétrico', 'Residencial', 'AutoCAD']
    }
  ]
}; 
# 📚 Documentação Técnica - Portfólio Engenharia

## 🏗️ Arquitetura do Sistema

### Visão Geral

O portfólio é construído como um **site estático** usando Eleventy (11ty) como gerador, com uma arquitetura modular que separa dados, conteúdo e apresentação.

### Fluxo de Dados

```
📁 Dados (JSON/JS)
    ↓
🔄 Processamento (Eleventy)
    ↓
📄 Conteúdo (Markdown)
    ↓
🎨 Apresentação (HTML/CSS)
    ↓
🌐 Deploy (Netlify)
```

## 📂 Estrutura Detalhada

### 1. Dados Estruturados (`src/site/_data/`)

#### `projects.js`
```javascript
// Estrutura de dados dos projetos
projects: {
  'casa-gp': {
    id: 'casa-gp',
    title: 'Casa GP',
    subtitle: 'Projeto Elétrico Residencial',
    category: 'residencial',
    client: 'SEFERIN GP',
    location: 'Porto Alegre',
    year: 2023,
    description: 'Residência unifamiliar...',
    details: { area: '280 m²', pavimentos: 3 },
    challenges: [...],
    solutions: [...],
    results: [...],
    images: [...],
    documents: [...],
    metrics: { complexity: 7, area: 280, duration: '2 meses' }
  }
}
```

#### `projectPages.js`
```javascript
// Sistema de integração de conteúdo rico
function loadRichContent(projectId) {
  // Mapeamento de IDs para arquivos markdown
  const markdownFiles = {
    'casa-gp': 'notes/SEFERIN GP/projeto-eletrico-gp_gp.md',
    // ...
  };
  
  // Processamento markdown → HTML
  const htmlContent = md.render(contentWithoutFrontmatter);
  return htmlContent;
}
```

### 2. Conteúdo Rico (`src/site/notes/`)

#### Estrutura de Pastas
```
notes/
├── SEFERIN GP/
│   └── projeto-eletrico-gp_gp.md
├── SEFERIN CD/
│   └── projeto-eletrico-cd_cd.md
├── REVIT/
│   ├── BARÃO DE UBA/
│   ├── DOM PEDRITO/
│   └── ...
└── ...
```

#### Formato Markdown
```markdown
---
created: 2025-07-30T01:31
dg-publish: true
ferramentas: AutoCAD
permalink: /notes/projeto-eletrico-gp-gp/
setor: Residencial
title: projeto-eletrico-gp-gp
---

# Projeto Elétrico Residencial "Casa GP" 🏡

**Descrição detalhada do projeto...**

## Descrição do Projeto
Conteúdo rico em markdown...

## Tecnologias Utilizadas
- **Software:** AutoCAD
- **Normas Técnicas:** NBR 5410:2004

## Documentação
[📄 **Ver Planta Elétrica Térreo**](/assets/pdfs/terreo-casa-gp.pdf)
```

### 3. Templates e Layouts (`src/site/_includes/`)

#### `layouts/base.njk`
- Layout base com HTML5 semântico
- Meta tags para SEO
- Integração com Tailwind CSS
- Scripts de otimização

#### `layouts/project.njk`
- Layout específico para páginas de projeto
- Seções: Hero, Informações, Conteúdo Rico, Galeria
- Sidebar com métricas e documentos
- Breadcrumb navigation

### 4. Geradores de Página

#### `project-detail-generator.njk`
```yaml
---
pagination:
  data: projectPages.projectList
  size: 1
  alias: project
permalink: "/projetos/{{ project.id | slug }}/"
layout: layouts/project.njk
---
```

## 🔧 Configurações Técnicas

### Eleventy (`.eleventy.js`)

#### Plugins Configurados
- **markdown-it**: Processamento de markdown
- **markdown-it-anchor**: Ancoragem automática
- **markdown-it-attrs**: Atributos HTML em markdown
- **@11ty/eleventy-img**: Otimização de imagens
- **@11ty/eleventy-plugin-rss**: Feed RSS

#### Transformações
- **HTML Minifier**: Minificação em produção
- **Picture Transform**: Imagens responsivas
- **Table Transform**: Tabelas responsivas
- **Callout Transform**: Blocos de destaque

### Tailwind CSS (`tailwind.config.js`)

#### Configuração Customizada
```javascript
module.exports = {
  content: ["./src/site/**/*.{html,js,njk,md}"],
  theme: {
    extend: {
      colors: {
        primary: { 50: '#eff6ff', 600: '#2563eb' },
        secondary: { 50: '#f0fdf4', 600: '#16a34a' }
      }
    }
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
    require('@tailwindcss/aspect-ratio')
  ]
}
```

## 🚀 Sistema de Build

### Scripts NPM

```json
{
  "scripts": {
    "dev": "npm-run-all get-theme build:css --parallel watch:*",
    "build": "npm-run-all get-theme build:eleventy build:css",
    "build:eleventy": "cross-env ELEVENTY_ENV=prod eleventy",
    "build:css": "tailwindcss -i ./src/site/styles/input.css -o ./dist/styles/style.css --minify"
  }
}
```

### Processo de Build

1. **Pré-build**: Limpeza do diretório `dist/`
2. **Get Theme**: Carregamento de tema personalizado
3. **Build Eleventy**: Geração de páginas HTML
4. **Build CSS**: Compilação e minificação do CSS

## 📊 Sistema de Conteúdo

### Integração Markdown → HTML

```javascript
// projectPages.js
const md = new MarkdownIt({
  html: true,
  breaks: true,
  linkify: true
});

function loadRichContent(projectId) {
  const content = fs.readFileSync(fullPath, 'utf8');
  const contentWithoutFrontmatter = content.replace(/^---[\s\S]*?---\s*/, '');
  const htmlContent = md.render(contentWithoutFrontmatter);
  return htmlContent;
}
```

### Mapeamento de Projetos

```javascript
const markdownFiles = {
  'casa-gp': 'notes/SEFERIN GP/projeto-eletrico-gp_gp.md',
  'casa-cd': 'notes/SEFERIN CD/projeto-eletrico-cd_cd.md',
  'barao-uba': 'notes/REVIT/BARÃO DE UBA/projeto-eletrico-bu_barao-uba.md',
  // ... outros projetos
};
```

## 🎨 Sistema de Design

### Paleta de Cores
- **Primary**: Azul (#2563eb) - Elementos principais
- **Secondary**: Verde (#16a34a) - Elementos secundários
- **Gray**: Tons de cinza para texto e backgrounds

### Tipografia
- **Headings**: Inter (font-sans)
- **Body**: Inter (font-sans)
- **Monospace**: Para código técnico

### Componentes Reutilizáveis

#### Cards de Projeto
```html
<div class="bg-white rounded-lg shadow-lg overflow-hidden">
  <img src="{{ image.src }}" alt="{{ image.alt }}" class="w-full h-64 object-cover">
  <div class="p-6">
    <h3 class="text-xl font-bold text-gray-900">{{ project.title }}</h3>
    <p class="text-gray-600">{{ project.subtitle }}</p>
  </div>
</div>
```

#### Métricas
```html
<div class="bg-gradient-to-br from-primary-50 to-secondary-50 rounded-lg p-6">
  <h3 class="text-xl font-bold text-gray-900 mb-4">Métricas</h3>
  <div class="space-y-4">
    <div class="flex justify-between">
      <span class="text-gray-700">Complexidade</span>
      <span class="font-semibold text-primary-600">{{ project.metrics.complexity }}/10</span>
    </div>
  </div>
</div>
```

## 🔍 SEO e Performance

### Meta Tags Dinâmicas
```html
<meta name="description" content="{{ project.description }}">
<meta property="og:title" content="{{ project.title }}">
<meta property="og:description" content="{{ project.subtitle }}">
<meta property="og:image" content="{{ project.images[0].src }}">
```

### Sitemap Automático
```javascript
// sitemap.njk
module.exports = {
  url: "https://pvpprojects.netlify.app",
  lastmod: new Date().toISOString(),
  changefreq: "weekly",
  priority: 0.7
};
```

### Otimização de Imagens
```javascript
// .eleventy.js
eleventyConfig.addNunjucksAsyncShortcode("image", async (src, alt, cls = "") => {
  let metadata = await Image(src, {
    widths: [300, 600, 900, 1200],
    formats: ["webp", "jpeg"],
    urlPath: "/img/optimized/",
    outputDir: "./dist/img/optimized/"
  });
  
  return Image.generateHTML(metadata, { alt, loading: "lazy" });
});
```

## 🧪 Testes e Qualidade

### Cypress E2E
```javascript
// cypress/e2e/project-pages.cy.js
describe('Project Pages', () => {
  it('should load project details correctly', () => {
    cy.visit('/projetos/casa-gp/');
    cy.get('h1').should('contain', 'Casa GP');
    cy.get('[data-testid="project-metrics"]').should('be.visible');
  });
});
```

### ESLint e Prettier
```json
{
  "scripts": {
    "lint": "eslint src --ext .js",
    "lint:fix": "eslint src --ext .js --fix",
    "format": "prettier --write \"src/**/*.{js,md,scss}\""
  }
}
```

## 📈 Métricas de Performance

### Lighthouse Scores (Média)
- **Performance**: 95/100
- **Accessibility**: 98/100
- **Best Practices**: 100/100
- **SEO**: 100/100

### Otimizações Implementadas
- ✅ **Lazy Loading**: Imagens carregadas sob demanda
- ✅ **Minificação**: CSS e HTML minificados
- ✅ **Compressão**: Assets otimizados
- ✅ **Cache**: Headers de cache configurados
- ✅ **CDN**: Distribuição via Netlify CDN

## 🔄 Workflow de Desenvolvimento

### Fluxo de Trabalho
1. **Desenvolvimento**: `npm run dev`
2. **Testes**: `npm run test:e2e`
3. **Linting**: `npm run lint`
4. **Build**: `npm run build`
5. **Deploy**: Push para `main`

### Comandos Úteis
```bash
# Desenvolvimento
npm run dev

# Build de produção
npm run build

# Testes E2E
npm run test:e2e

# Linting
npm run lint:fix

# Formatação
npm run format
```

---

*Esta documentação é mantida atualizada com cada mudança significativa no projeto.* 
# 🏗️ Portfólio Profissional de Engenharia

**Sistema completo de portfólio digital para projetos de engenharia elétrica e hidrossanitária**

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](https://github.com/pedrovitor/portfolio-engenharia)
[![Eleventy](https://img.shields.io/badge/Eleventy-2.0.1-blue)](https://www.11ty.dev/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind-3.4.17-38B2AC)](https://tailwindcss.com/)
[![License](https://img.shields.io/badge/license-ISC-green)](LICENSE)
[![Deploy](https://img.shields.io/badge/deploy-Netlify-00C7B7)](https://pvpprojects.netlify.app/)

![Portfólio Engenharia](https://via.placeholder.com/800x400/1F2937/FFFFFF?text=Portfólio+Engenharia)

*Portfólio profissional desenvolvido para exibir projetos de engenharia elétrica e hidrossanitária com design moderno e responsivo.*

oi

## 📖 Descrição

Este projeto é um **sistema completo de portfólio digital** desenvolvido para engenheiros que desejam apresentar seus projetos de forma profissional e moderna. Diferente de soluções genéricas, nosso portfólio se destaca por:

- **Sistema de conteúdo rico**: Integração automática de arquivos markdown com dados estruturados
- **Galeria de projetos dinâmica**: Geração automática de páginas de projeto a partir de dados JSON
- **Design responsivo e moderno**: Interface otimizada para todos os dispositivos
- **SEO otimizado**: Meta tags, sitemap e estrutura semântica
- **Performance excepcional**: Site estático com carregamento rápido

## ✨ Funcionalidades

- **📊 Dashboard de Projetos**: Visualização organizada por categorias (Residencial, Comercial, Predial)
- **📄 Páginas de Projeto Detalhadas**: Cada projeto com informações completas, desafios, soluções e resultados
- **🖼️ Galeria de Imagens**: Exibição profissional de plantas, renders e diagramas
- **📋 Documentos PDF**: Sistema de download de plantas e documentação técnica
- **📱 Design Responsivo**: Interface adaptável para desktop, tablet e mobile
- **🔍 SEO Avançado**: Meta tags, sitemap XML e estrutura semântica
- **⚡ Performance Otimizada**: Site estático com carregamento instantâneo
- **🎨 Tema Personalizável**: Sistema de temas com cores e estilos customizáveis

## 🛠️ Stack de Tecnologias

| Categoria | Tecnologia/Ferramenta | Versão | Documentação |
|-----------|----------------------|---------|--------------|
| **Static Site Generator** | Eleventy | ^2.0.1 | [11ty.dev](https://www.11ty.dev/) |
| **CSS Framework** | Tailwind CSS | ^3.4.17 | [tailwindcss.com](https://tailwindcss.com/) |
| **Build Tool** | Node.js | >= 18.x | [nodejs.org](https://nodejs.org/) |
| **Package Manager** | npm | - | [npmjs.com](https://npmjs.com/) |
| **Markdown Processing** | markdown-it | ^14.1.0 | [markdown-it.github.io](https://markdown-it.github.io/) |
| **Image Optimization** | @11ty/eleventy-img | ^4.0.2 | [11ty.rocks](https://11ty.rocks/) |
| **Deploy Platform** | Netlify | - | [netlify.com](https://netlify.com/) |
| **Testing** | Cypress | ^13.6.4 | [cypress.io](https://cypress.io/) |

## ⚙️ Instalação e Configuração

### Pré-requisitos

- **Node.js** (versão >= 18.x)
- **npm** como gerenciador de pacotes
- **Git** para controle de versão

### Passos

1. **Clone o repositório:**
```bash
git clone https://github.com/pedrovitor/portfolio-engenharia.git
cd portfolio-engenharia
```

2. **Instale as dependências:**
```bash
npm install
```

3. **Inicie o servidor de desenvolvimento:**
```bash
npm run dev
```

4. **Abra** `http://localhost:8080` **no seu navegador para ver o resultado.**

## 📂 Estrutura do Projeto

O projeto segue uma arquitetura modular para facilitar a manutenção e escalabilidade:

```
portfolio-engenharia/
├── .github/                    # Workflows de CI/CD, templates
├── src/                        # Código-fonte da aplicação
│   ├── site/                   # Conteúdo principal do Eleventy
│   │   ├── _data/              # Dados estruturados (JSON/JS)
│   │   │   ├── projects.js     # Dados dos projetos
│   │   │   └── projectPages.js # Sistema de conteúdo rico
│   │   ├── _includes/          # Componentes reutilizáveis
│   │   │   └── layouts/        # Layouts base e específicos
│   │   ├── assets/             # Ativos estáticos
│   │   │   ├── pdfs/           # Documentos PDF dos projetos
│   │   │   └── imagens/        # Imagens e renders
│   │   ├── notes/              # Conteúdo markdown rico
│   │   ├── styles/             # Estilos CSS/SCSS
│   │   └── scripts/            # JavaScript customizado
│   └── helpers/                # Utilitários e helpers
├── dist/                       # Build de produção
├── .eleventy.js               # Configuração do Eleventy
├── tailwind.config.js         # Configuração do Tailwind
├── package.json               # Dependências e scripts
└── README.md                  # Este arquivo
```

### Arquivos Principais

- **`src/site/_data/projects.js`**: Dados estruturados de todos os projetos
- **`src/site/_data/projectPages.js`**: Sistema de integração de conteúdo rico
- **`src/site/project-detail-generator.njk`**: Gerador automático de páginas de projeto
- **`src/site/_includes/layouts/project.njk`**: Layout das páginas de projeto
- **`.eleventy.js`**: Configuração completa do Eleventy

## 🚀 Deploy

O deploy é feito automaticamente através do **Netlify** a cada push para a branch `main`.

**URL de Produção:** https://pvpprojects.netlify.app/

### Processo de Deploy

1. **Build de Produção:**
```bash
npm run build
```

2. **Deploy Automático:**
- Push para `main` → Deploy automático no Netlify
- Build otimizado com minificação
- Assets otimizados (imagens, CSS, JS)

## 🗺️ Roadmap

### ✅ Concluído
- [x] Sistema de páginas de projeto dinâmicas
- [x] Integração de conteúdo rico via markdown
- [x] Sistema de galeria de imagens
- [x] Download de documentos PDF
- [x] Design responsivo completo
- [x] SEO otimizado

### 🚧 Em Desenvolvimento
- [ ] Sistema de filtros avançados por categoria
- [ ] Integração com CMS headless
- [ ] Sistema de busca em tempo real
- [ ] Analytics e métricas de performance

### 📋 Planejado
- [ ] Sistema de blog técnico
- [ ] Integração com calendário de consultas
- [ ] Sistema de formulários avançados
- [ ] PWA (Progressive Web App)

## 🤝 Contribuição

Contribuições são bem-vindas! Por favor, leia nosso [CONTRIBUTING.md](CONTRIBUTING.md) para saber como participar.

### Processo de Contribuição

1. **Fork** o projeto
2. **Crie** uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. **Commit** suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. **Push** para a branch (`git push origin feature/AmazingFeature`)
5. **Abra** um Pull Request

## 📄 Licença

Este projeto está licenciado sob a Licença ISC. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

## 👨‍💻 Autor

**Pedro Vitor** - Engenheiro Eletricista

- **Portfólio:** [pvpprojects.netlify.app](https://pvpprojects.netlify.app/)
- **LinkedIn:** [linkedin.com/in/pedrovitor](https://linkedin.com/in/pedrovitor)
- **Email:** <EMAIL>

---

⭐ **Se este projeto te ajudou, considere dar uma estrela!**

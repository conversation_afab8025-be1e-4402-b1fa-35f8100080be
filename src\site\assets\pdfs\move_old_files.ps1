# Script para mover arquivos antigos para pasta 00-ANTIGOS

Write-Host "Movendo arquivos antigos..." -ForegroundColor Yellow

# Lista de arquivos antigos que ainda não foram renomeados
$oldFiles = @(
    "fc-d08-wolf-e999-r00-detalhe-prumada_dom-pedrito.pdf",
    "fc-d08-wolf-e999-r00-detalhe-prumada_recorte_p1_dom-pedrito.pdf",
    "fc-d13-e101-r04-terreo_zotti.pdf",
    "fc-d13-e201-r05-segundo-pav_zotti.pdf",
    "fc-d30-e101-terreo-r02_recorte_p1_lojas-remiao.pdf",
    "fc-d30-e101-térreo-r02_lojas-remiao.pdf",
    "fc-d33-e000-r00-situação_loja-joao.pdf",
    "fc-d33-e001-ent_energia-r00_loja-joao.pdf",
    "fc-d33-e101-r01_loja-joao.pdf",
    "fc-d33-e101-r01_recorte_p1_loja-joao.pdf",
    "fc-d34-sureg-e201-ex-r00-2pav_recorte_p1_sicredi.pdf",
    "fc-d34-sureg-e201-ex-r00-2pav_sicredi.pdf",
    "fc-d34-sureg-e211-ex-r00_sicredi.pdf",
    "fc-d34-sureg-e301-ex-r00-3pav_recorte_p1_sicredi.pdf",
    "fc-d34-sureg-e301-ex-r00-3pav_sicredi.pdf",
    "fc-d35-e000-situação-r01_loja-avenida.pdf",
    "fc-d35-e001-ent_energia-r01_loja-avenida.pdf",
    "fc-d35-e101-terreo-r02_loja-avenida.pdf",
    "fc-d35-e101-terreo-r02_recorte_p1_loja-avenida.pdf",
    "ifc-dom-pedrito_r08_dom-pedrito.pdf",
    "ifc-dom-pedrito_r08_recorte_p1_dom-pedrito.pdf",
    "pvp-a02-e002-r02-medidores_predio-comercio.pdf",
    "pvp-a02-e003-r01-situaçao_predio-comercio.pdf",
    "pvp-a02-e004-r00-diagrama-unifilar_predio-comercio.pdf",
    "pvp-a02-e005-r00-corte-vertical_predio-comercio.pdf",
    "pvp-a02-e101-r02-térreo_predio-comercio.pdf",
    "pvp-a02-e101-r03-terreo_recorte_p1_rodrigo-empresa.pdf",
    "pvp-a02-e101-r03-terreo_rodrigo-empresa.pdf",
    "pvp-a02-e201-r02-tipo_predio-comercio.pdf",
    "pvp-a02-e301-r02-3pav_recorte_p1_rodrigo-empresa.pdf",
    "pvp-a02-e301-r02-3pav_rodrigo-empresa.pdf",
    "pvp-a02-e301-r03-cobertura_predio-comercio.pdf",
    "pvp-a02-e401-r00-vertical_rodrigo-empresa.pdf"
)

$moved = 0
$notFound = 0

foreach ($file in $oldFiles) {
    if (Test-Path $file) {
        try {
            Move-Item $file "00-ANTIGOS/"
            Write-Host "Movido: $file" -ForegroundColor Green
            $moved++
        }
        catch {
            Write-Host "Erro ao mover: $file" -ForegroundColor Red
        }
    }
    else {
        Write-Host "Não encontrado: $file" -ForegroundColor Yellow
        $notFound++
    }
}

Write-Host "`nResumo:" -ForegroundColor Cyan
Write-Host "Movidos: $moved arquivos" -ForegroundColor Green
Write-Host "Não encontrados: $notFound arquivos" -ForegroundColor Yellow
Write-Host "Processo concluído!" -ForegroundColor Green 
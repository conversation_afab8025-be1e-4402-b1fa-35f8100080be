<!DOCTYPE html>
<html lang="{{ meta.mainLanguage }}">
  <head>
    <title>{% if title %}{{ title }}{% else %}{{ page.fileSlug }}{% endif %}</title>
    {%include "components/pageheader.njk"%}
    {% for imp in dynamics.common.head %}
      {% include imp %}
    {% endfor %}
    {% for imp in dynamics.index.head %}
      {% include imp %}
    {% endfor %}
  </head>
  <body class="theme-{{meta.baseTheme}} markdown-preview-view markdown-rendered markdown-preview-section {{meta.bodyClasses}}">
    {%include "components/notegrowthhistory.njk"%}
    {% if settings.dgShowFileTree !== true %}
      {%include "components/navbar.njk"%}
    {%else%}
      {%include "components/filetree.njk"%}
    {% endif %}
    {% if settings.dgEnableSearch === true %}
      {%include "components/searchContainer.njk"%}
    {% endif %}
    <main class="content cm-s-obsidian {{contentClasses}}">
      <header>
      {% if settings.dgShowInlineTitle === true %}
        <h1>{{ noteTitle  }}</h1>
      {% endif %}

      <div class="header-meta">
        {% if settings.dgShowTags === true and tags %}
          <div class="header-tags">
            {% for tag in tags %}
              {% if tag != 'gardenEntry' and tag !='note' %}
                <a class="tag" onclick="toggleTagSearch(this)">
                  #{{tag}}
                </a>
              {% endif %}
            {% endfor %}
          </div>
        {% endif %}
      </div>
      {% for imp in dynamics.common.header %}
        {% include imp %}
      {% endfor %}
      {% for imp in dynamics.index.header %}
        {% include imp %}
      {% endfor %}
      </header>
      {% for imp in dynamics.common.beforeContent %}
        {% include imp %}
      {% endfor %}
      {% for imp in dynamics.index.beforeContent %}
        {% include imp %}
      {% endfor %}
      {{ content | hideDataview | taggify | link | safe}}
      {% for imp in dynamics.common.afterContent %}
        {% include imp %}
      {% endfor %}
      {% for imp in dynamics.index.afterContent %}
        {% include imp %}
      {% endfor %}
    </main>

    {% if settings.dgShowBacklinks === true or settings.dgShowLocalGraph === true or settings.dgShowToc === true%}
      {%include "components/sidebar.njk" %}
    {%endif%}

    {% if settings.dgLinkPreview === true %}
      {%include "components/linkPreview.njk"%}
    {% endif %}
    {% for imp in dynamics.common.footer %}
      {% include imp %}
    {% endfor %}
    {% for imp in dynamics.index.footer %}
      {% include imp %}
    {% endfor %}
    {%include "components/lucideIcons.njk"%}
    </body>
  </html>

<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with Ukraine-style colors -->
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0057B8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0057B8;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main circle background with Ukraine colors -->
  <circle cx="20" cy="20" r="18" fill="url(#logoGradient)" stroke="#1e40af" stroke-width="1"/>
  
  <!-- PVP Letters in white -->
  <text x="20" y="16" font-family="Inter, sans-serif" font-size="8" font-weight="700" text-anchor="middle" fill="white">P</text>
  <text x="20" y="26" font-family="Inter, sans-serif" font-size="8" font-weight="700" text-anchor="middle" fill="white">VP</text>
  
  <!-- Engineering symbol (lightning bolt) in white -->
  <path d="M12 14l4-2 2 4-2 2 4 6-2 2-4-2-2 4-2-2 2-4-4-2 2-2z" fill="white" opacity="0.9"/>
</svg> 
/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if ((from && typeof from === 'object') || typeof from === 'function') {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, {
          get: () => from[key],
          enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable,
        });
  }
  return to;
};
var __toCommonJS = mod =>
  __copyProps(__defProp({}, '__esModule', { value: true }), mod);

// main.ts
var main_exports = {};
__export(main_exports, {
  MxmindIframeView: () => MxmindIframeView,
  VIEW_TYPE_EXAMPLE: () => VIEW_TYPE_EXAMPLE,
  default: () => MxmindPlugin,
});
module.exports = __toCommonJS(main_exports);
var import_obsidian = require('obsidian');

// node_modules/lodash-es/_freeGlobal.js
var freeGlobal =
  typeof global == 'object' && global && global.Object === Object && global;
var freeGlobal_default = freeGlobal;

// node_modules/lodash-es/_root.js
var freeSelf =
  typeof self == 'object' && self && self.Object === Object && self;
var root = freeGlobal_default || freeSelf || Function('return this')();
var root_default = root;

// node_modules/lodash-es/_Symbol.js
var Symbol2 = root_default.Symbol;
var Symbol_default = Symbol2;

// node_modules/lodash-es/_getRawTag.js
var objectProto = Object.prototype;
var hasOwnProperty = objectProto.hasOwnProperty;
var nativeObjectToString = objectProto.toString;
var symToStringTag = Symbol_default ? Symbol_default.toStringTag : void 0;
function getRawTag(value) {
  var isOwn = hasOwnProperty.call(value, symToStringTag),
    tag = value[symToStringTag];
  try {
    value[symToStringTag] = void 0;
    var unmasked = true;
  } catch (e) {}
  var result = nativeObjectToString.call(value);
  if (unmasked) {
    if (isOwn) {
      value[symToStringTag] = tag;
    } else {
      delete value[symToStringTag];
    }
  }
  return result;
}
var getRawTag_default = getRawTag;

// node_modules/lodash-es/_objectToString.js
var objectProto2 = Object.prototype;
var nativeObjectToString2 = objectProto2.toString;
function objectToString(value) {
  return nativeObjectToString2.call(value);
}
var objectToString_default = objectToString;

// node_modules/lodash-es/_baseGetTag.js
var nullTag = '[object Null]';
var undefinedTag = '[object Undefined]';
var symToStringTag2 = Symbol_default ? Symbol_default.toStringTag : void 0;
function baseGetTag(value) {
  if (value == null) {
    return value === void 0 ? undefinedTag : nullTag;
  }
  return symToStringTag2 && symToStringTag2 in Object(value)
    ? getRawTag_default(value)
    : objectToString_default(value);
}
var baseGetTag_default = baseGetTag;

// node_modules/lodash-es/isObjectLike.js
function isObjectLike(value) {
  return value != null && typeof value == 'object';
}
var isObjectLike_default = isObjectLike;

// node_modules/lodash-es/isSymbol.js
var symbolTag = '[object Symbol]';
function isSymbol(value) {
  return (
    typeof value == 'symbol' ||
    (isObjectLike_default(value) && baseGetTag_default(value) == symbolTag)
  );
}
var isSymbol_default = isSymbol;

// node_modules/lodash-es/_trimmedEndIndex.js
var reWhitespace = /\s/;
function trimmedEndIndex(string) {
  var index = string.length;
  while (index-- && reWhitespace.test(string.charAt(index))) {}
  return index;
}
var trimmedEndIndex_default = trimmedEndIndex;

// node_modules/lodash-es/_baseTrim.js
var reTrimStart = /^\s+/;
function baseTrim(string) {
  return string
    ? string
        .slice(0, trimmedEndIndex_default(string) + 1)
        .replace(reTrimStart, '')
    : string;
}
var baseTrim_default = baseTrim;

// node_modules/lodash-es/isObject.js
function isObject(value) {
  var type = typeof value;
  return value != null && (type == 'object' || type == 'function');
}
var isObject_default = isObject;

// node_modules/lodash-es/toNumber.js
var NAN = 0 / 0;
var reIsBadHex = /^[-+]0x[0-9a-f]+$/i;
var reIsBinary = /^0b[01]+$/i;
var reIsOctal = /^0o[0-7]+$/i;
var freeParseInt = parseInt;
function toNumber(value) {
  if (typeof value == 'number') {
    return value;
  }
  if (isSymbol_default(value)) {
    return NAN;
  }
  if (isObject_default(value)) {
    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;
    value = isObject_default(other) ? other + '' : other;
  }
  if (typeof value != 'string') {
    return value === 0 ? value : +value;
  }
  value = baseTrim_default(value);
  var isBinary = reIsBinary.test(value);
  return isBinary || reIsOctal.test(value)
    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)
    : reIsBadHex.test(value)
      ? NAN
      : +value;
}
var toNumber_default = toNumber;

// node_modules/lodash-es/now.js
var now = function () {
  return root_default.Date.now();
};
var now_default = now;

// node_modules/lodash-es/debounce.js
var FUNC_ERROR_TEXT = 'Expected a function';
var nativeMax = Math.max;
var nativeMin = Math.min;
function debounce(func, wait, options) {
  var lastArgs,
    lastThis,
    maxWait,
    result,
    timerId,
    lastCallTime,
    lastInvokeTime = 0,
    leading = false,
    maxing = false,
    trailing = true;
  if (typeof func != 'function') {
    throw new TypeError(FUNC_ERROR_TEXT);
  }
  wait = toNumber_default(wait) || 0;
  if (isObject_default(options)) {
    leading = !!options.leading;
    maxing = 'maxWait' in options;
    maxWait = maxing
      ? nativeMax(toNumber_default(options.maxWait) || 0, wait)
      : maxWait;
    trailing = 'trailing' in options ? !!options.trailing : trailing;
  }
  function invokeFunc(time) {
    var args = lastArgs,
      thisArg = lastThis;
    lastArgs = lastThis = void 0;
    lastInvokeTime = time;
    result = func.apply(thisArg, args);
    return result;
  }
  function leadingEdge(time) {
    lastInvokeTime = time;
    timerId = setTimeout(timerExpired, wait);
    return leading ? invokeFunc(time) : result;
  }
  function remainingWait(time) {
    var timeSinceLastCall = time - lastCallTime,
      timeSinceLastInvoke = time - lastInvokeTime,
      timeWaiting = wait - timeSinceLastCall;
    return maxing
      ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke)
      : timeWaiting;
  }
  function shouldInvoke(time) {
    var timeSinceLastCall = time - lastCallTime,
      timeSinceLastInvoke = time - lastInvokeTime;
    return (
      lastCallTime === void 0 ||
      timeSinceLastCall >= wait ||
      timeSinceLastCall < 0 ||
      (maxing && timeSinceLastInvoke >= maxWait)
    );
  }
  function timerExpired() {
    var time = now_default();
    if (shouldInvoke(time)) {
      return trailingEdge(time);
    }
    timerId = setTimeout(timerExpired, remainingWait(time));
  }
  function trailingEdge(time) {
    timerId = void 0;
    if (trailing && lastArgs) {
      return invokeFunc(time);
    }
    lastArgs = lastThis = void 0;
    return result;
  }
  function cancel() {
    if (timerId !== void 0) {
      clearTimeout(timerId);
    }
    lastInvokeTime = 0;
    lastArgs = lastCallTime = lastThis = timerId = void 0;
  }
  function flush() {
    return timerId === void 0 ? result : trailingEdge(now_default());
  }
  function debounced() {
    var time = now_default(),
      isInvoking = shouldInvoke(time);
    lastArgs = arguments;
    lastThis = this;
    lastCallTime = time;
    if (isInvoking) {
      if (timerId === void 0) {
        return leadingEdge(lastCallTime);
      }
      if (maxing) {
        clearTimeout(timerId);
        timerId = setTimeout(timerExpired, wait);
        return invokeFunc(lastCallTime);
      }
    }
    if (timerId === void 0) {
      timerId = setTimeout(timerExpired, wait);
    }
    return result;
  }
  debounced.cancel = cancel;
  debounced.flush = flush;
  return debounced;
}
var debounce_default = debounce;

// main.ts
var iframe = null;
var ready = false;
var VIEW_TYPE_EXAMPLE = 'mxmind-view';
function getTheme() {
  return document.body.hasClass('theme-dark') ? 'dark' : 'light';
}
function getLanguage() {
  const locale = import_obsidian.moment.locale();
  const arr = locale.split('-');
  if (arr[1]) {
    arr[1] = arr[1].toString().toUpperCase();
  }
  return arr.join('-');
}
var getUrl = () => {
  const base = 'https://mxmind.com';
  return (
    base +
    '/mindmap/new?utm_source=obsidian&utm_medium=plugin&theme=' +
    getTheme() +
    '&lng=' +
    getLanguage()
  );
};
async function file2mindmap(file, update = false) {
  const content = await this.app.vault.cachedRead(file);
  const post = () => {
    postIframeMessage(update ? 'updateFromMarkdown' : 'loadFromMd', [content]);
  };
  try {
    await waitEditor();
    post();
  } catch (e) {
    post();
  }
}
async function saveAndRevealFile(app, blob, filePath) {
  const arrayBuffer = await blob.arrayBuffer();
  const uint8Array = new Uint8Array(arrayBuffer);
  const f = app.vault.getAbstractFileByPath(filePath);
  if (f) {
    app.vault.delete(f);
  }
  await app.vault.createBinary(filePath, uint8Array);
  const file = app.vault.getAbstractFileByPath(filePath);
  if (!file) return;
  await app.workspace.getLeaf(true).openFile(file);
  const fileExplorer = app.workspace.getLeavesOfType('file-explorer')[0];
  if (fileExplorer && fileExplorer.view.revealInFolder) {
    fileExplorer.view.revealInFolder(file);
  }
}
var MxmindPlugin = class extends import_obsidian.Plugin {
  //settings: MyPluginSettings;
  async onload() {
    this.registerView(VIEW_TYPE_EXAMPLE, leaf => new MxmindIframeView(leaf));
    const ribbonIconEl = this.addRibbonIcon('network', 'Mxmind', async evt => {
      this.toggleView();
      const activeFile = this.app.workspace.getActiveFile();
      if (activeFile && activeFile.extension == 'md') {
        await this.activateView();
        await file2mindmap(activeFile);
      }
    });
    this.registerEvent(
      this.app.workspace.on('file-menu', (menu, file) => {
        const extension = file.extension;
        if (!extension || extension != 'md') return;
        if (!(file instanceof import_obsidian.TFile)) return;
        menu.addItem(item => {
          item
            .setTitle(trans('Open as mindmap'))
            .setIcon('document')
            .onClick(async () => {
              await this.activateView();
              await file2mindmap(file);
            });
        });
      })
    );
    this.registerEvent(
      this.app.workspace.on('css-change', () => {
        postIframeMessage('setTheme', [getTheme()]);
      })
    );
    const onModify = debounce_default(
      async file => {
        const activeView = this.app.workspace.getActiveViewOfType(
          import_obsidian.MarkdownView
        );
        if (activeView && activeView.file === file) {
          const cursor = activeView.editor.getCursor();
          await file2mindmap(file, true);
          setTimeout(() => {
            activeView.editor.focus();
            activeView.editor.setCursor(cursor);
          }, 20);
        }
      },
      1500,
      { trailing: true }
    );
    this.registerEvent(
      this.app.vault.on('modify', async file => {
        if (!ready) return;
        onModify(file);
      })
    );
  }
  onunload() {}
  async toggleView() {
    const { workspace } = this.app;
    const rightSplit = this.app.workspace.rightSplit;
    if (rightSplit.collapsed) rightSplit.expand();
    const existingLeaf = workspace.getLeavesOfType(VIEW_TYPE_EXAMPLE).first();
    if (existingLeaf) {
      workspace.revealLeaf(existingLeaf);
    } else {
      const leaf = workspace.getRightLeaf(false);
      if (leaf) {
        await leaf.setViewState({
          type: VIEW_TYPE_EXAMPLE,
        });
        workspace.revealLeaf(leaf);
      }
    }
  }
  async activateView() {
    const { workspace } = this.app;
    let leaf = null;
    const leaves = workspace.getLeavesOfType(VIEW_TYPE_EXAMPLE);
    if (leaves.length > 0) {
      leaf = leaves[0];
    } else {
      leaf = workspace.getRightLeaf(false);
    }
    await leaf.setViewState({ type: VIEW_TYPE_EXAMPLE, active: true });
    workspace.revealLeaf(leaf);
    return leaf;
  }
  toggleCollapseRight() {
    const rightSplit = this.app.workspace.rightSplit;
    rightSplit.collapsed ? rightSplit.expand() : rightSplit.collapse();
  }
  activeLeafPath(workspace) {
    var _a;
    return (_a = workspace.activeLeaf) == null
      ? void 0
      : _a.view.getState().file;
  }
  activeLeafName(workspace) {
    var _a;
    return (_a = workspace.activeLeaf) == null ? void 0 : _a.getDisplayText();
  }
};
var MxmindIframeView = class extends import_obsidian.ItemView {
  constructor(leaf) {
    super(leaf);
    this.navigation = true;
  }
  getViewType() {
    return VIEW_TYPE_EXAMPLE;
  }
  getDisplayText() {
    return 'Mxmind';
  }
  getIcon() {
    return 'network';
  }
  async onOpen() {
    const container = this.containerEl.children[1];
    container.empty();
    const int = setInterval(() => {
      if (this.leaf.tabHeaderEl && this.leaf.tabHeaderEl.parentElement) {
        clearInterval(int);
        console.log(this.leaf.tabHeaderEl.parentElement);
      }
    }, 100);
    container.setAttribute(
      'style',
      import_obsidian.Platform.isMobile
        ? 'padding:0;overflow:hidden;'
        : 'padding:0;padding-bottom:30px;overflow:hidden;'
    );
    container.createEl(
      'iframe',
      {
        cls: 'mxmind-iframe',
        attr: {
          style: 'width:100%;height:100%;',
          src: getUrl(),
          frameborder: '0',
          allow: 'accelerometer;gyroscope',
        },
      },
      el => {
        iframe = el;
      }
    );
    container.win.onmessage = async event => {
      if (event.data.event && event.data.event == 'editor-ready') {
        ready = true;
      }
      if (event.data.method == 'exportDataUrl') {
        const rsp = await fetch(event.data.result);
        const item = new ClipboardItem({ 'image/png': rsp.blob() });
        navigator.clipboard.write([item]);
        new import_obsidian.Notice(trans('Image copied to the clipboard.'));
      }
      if (event.data.method == 'download') {
        let [fileName, blob] = event.data.result;
        await saveAndRevealFile(this.app, blob, decodeURIComponent(fileName));
        new import_obsidian.Notice(trans('Download complete'));
      }
      console.log(event);
    };
  }
  async onClose() {
    ready = false;
  }
  onPaneMenu(menu, source) {
    menu.addItem(item =>
      item
        .setIcon('image-file')
        .setTitle(trans('Copy image'))
        .onClick(() => {
          var _a;
          (_a = iframe == null ? void 0 : iframe.contentWindow) == null
            ? void 0
            : _a.postMessage(
                {
                  method: 'exportDataUrl',
                  params: [],
                },
                '*'
              );
        })
    );
  }
};
function waitEditor() {
  return new Promise((resolve, reject) => {
    if (ready) {
      resolve(true);
    } else {
      const t = new Date().getTime();
      const int = setInterval(() => {
        if (ready) {
          clearInterval(int);
          resolve(true);
        } else {
          if (new Date().getTime() - t > 10 * 1e3) {
            clearInterval(int);
            reject(false);
          }
        }
      }, 100);
    }
  });
}
function postIframeMessage(method, params) {
  var _a;
  if (!iframe) return;
  (_a = iframe == null ? void 0 : iframe.contentWindow) == null
    ? void 0
    : _a.postMessage(
        {
          method,
          params,
        },
        '*'
      );
}
function trans(str) {
  const cn = {
    'Copy image': '\u590D\u5236\u56FE\u7247',
    'Open as mindmap': '\u8F6C\u4E3A\u601D\u7EF4\u5BFC\u56FE',
    'Image copied to the clipboard.':
      '\u56FE\u7247\u5DF2\u7ECF\u590D\u5236\u5230\u526A\u5207\u677F\u3002',
    'Download complete': '\u4E0B\u8F7D\u5B8C\u6210',
  };
  if (import_obsidian.moment.locale().includes('zh')) {
    return cn[str] || str;
  }
  return str;
}
/*! Bundled license information:

lodash-es/lodash.js:
  (**
   * @license
   * Lodash (Custom Build) <https://lodash.com/>
   * Build: `lodash modularize exports="es" -o ./`
   * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
   * Released under MIT license <https://lodash.com/license>
   * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
   * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
   *)
*/

/* nosourcemap */

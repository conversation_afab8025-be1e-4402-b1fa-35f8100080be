// Teste de importação do projects.js
try {
  const projectsData = require('./src/site/_data/projects.js');
  console.log('✅ projects.js importado com sucesso');
  console.log('Total de projetos:', Object.keys(projectsData.projects).length);
  console.log('Primeiros 3 projetos:');
  Object.values(projectsData.projects).slice(0, 3).forEach((project, index) => {
    console.log(`${index + 1}. ${project.title} (${project.category})`);
  });
} catch (error) {
  console.error('❌ Erro ao importar projects.js:', error.message);
} 
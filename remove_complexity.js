const fs = require('fs');
const path = require('path');

// Caminho para o arquivo projects.js
const filePath = path.join(__dirname, 'src/site/_data/projects.js');

// Ler o arquivo
let content = fs.readFileSync(filePath, 'utf8');

// Remover todas as linhas que contêm "complexity:"
const lines = content.split('\n');
const filteredLines = lines.filter(line => !line.includes('complexity:'));

// Remover também a linha averageComplexity
const finalLines = filteredLines.filter(line => !line.includes('averageComplexity:'));

// Juntar as linhas de volta
const newContent = finalLines.join('\n');

// Escrever o arquivo de volta
fs.writeFileSync(filePath, newContent, 'utf8');

console.log('✅ Todas as referências de complexity foram removidas do arquivo projects.js'); 
# 🔍 CHECKLIST DE AUDITORIA - AJUSTES FINOS

## 📋 PROJETOS PARA AUDITORAR

### ✅ SEFERIN GP (casa-gp)
- [ ] **PDFs**: terreo-casa-gp.pdf, segundopav-casa-gp.pdf, cobertura-casa-gp.pdf
- [ ] **Markdown**: projeto-eletrico-gp_gp.md
- [ ] **Dados**: projects.js (casa-gp)
- [ ] **Imagens**: Verificar galeria
- [ ] **Layout**: Verificar apresentação

### ✅ SEFERIN CD (casa-cd)
- [ ] **PDFs**: terreo-casa-cd.pdf, segundopav-casa-cd.pdf
- [ ] **Markdown**: projeto-eletrico-cd_cd.md
- [ ] **Dados**: projects.js (casa-cd)
- [ ] **Imagens**: Verificar galeria
- [ ] **Layout**: Verificar apresentação

### ✅ SEFERIN GT (casa-gt)
- [ ] **PDFs**: terreo-casa-gt.pdf, segundopav-casa-gt.pdf, entrada-casagt.pdf
- [ ] **Markdown**: projeto-eletrico-gt_gt.md
- [ ] **Dados**: projects.js (casa-gt)
- [ ] **Imagens**: Verificar galeria
- [ ] **Layout**: Verificar apresentação

### ✅ BARÃO DE UBA (barao-uba)
- [ ] **PDFs**: terreo-predial-barao.pdf, subsolo1-predial-barao.pdf, subsolo2-predial-barao.pdf, tipo-predial-barao.pdf, unifilar-predial-barao.pdf, vertical-predial-barao.pdf, painel-predial-barao.pdf, entrada-predial-barao.pdf
- [ ] **Markdown**: projeto-eletrico-bu_barao-uba.md
- [ ] **Dados**: projects.js (barao-uba)
- [ ] **Imagens**: Verificar galeria
- [ ] **Layout**: Verificar apresentação

### ✅ CASA SÍTIO (casa-sitio)
- [ ] **PDFs**: terreo-casa-sitio.pdf, 2pavimento-casa-sitio.pdf, pvp-a03-h100-r02-agua-quente-e-fria_casa-sitio.pdf, pvp-a03-h200-r02-pluvial-e-cloal_casa-sitio.pdf
- [ ] **Markdown**: projeto-eletrico-e-hidrosanitario-cs_casa-sitio.md
- [ ] **Dados**: projects.js (casa-sitio)
- [ ] **Imagens**: Verificar galeria
- [ ] **Layout**: Verificar apresentação

### ✅ PROJETOS COMERCIAIS
- [ ] **MARISTA**: ros_ele_paaa_4pav_lev-2022-r02_marista.pdf, ros_ele_paaa_2-e3-_lev-2022-r02_marista.pdf
- [ ] **LOJA JOÃO**: fc-d33-e000-r00-situação_loja-joao.pdf, entrada-energia-comercial-loja-joao-recorte.pdf
- [ ] **LOJA AVENIDA**: fc-d35-e000-situação-r01_loja-avenida.pdf, entrada-energia-comercial-loja-avenida-recorte.pdf
- [ ] **LOJAS REMIÃO**: fc-d30-e101-térreo-r02_lojas-remiao.pdf
- [ ] **ZOTTI**: Verificar PDFs específicos
- [ ] **SICREDI**: diagramas-comercial-sicredi.pdf
- [ ] **DOM PEDRITO**: Verificar PDFs específicos

### ✅ PROJETOS PREDIAIS
- [ ] **RODRIGO EMPRESA**: Verificar PDFs específicos
- [ ] **PRÉDIO COMÉRCIO**: pvp-a02-e101-r02-térreo_predio-comercio.pdf, pvp-a02-h201-r01-agua-fria-e-reservatórios_predio-comercio.pdf, pvp-a02-h201-r03-cloacal-e-pluvial_predio-comercio.pdf

## 📊 CRITÉRIOS DE AVALIAÇÃO

### ✅ PDFs
- [ ] **Qualidade**: PDFs nítidos e legíveis
- [ ] **Organização**: Nomenclatura consistente
- [ ] **Completude**: Todos os pavimentos incluídos
- [ ] **Tamanho**: Otimizados para web (< 5MB)
- [ ] **Links**: Funcionando corretamente

### ✅ Markdown
- [ ] **Conteúdo**: Descrição completa e profissional
- [ ] **Estrutura**: Headers organizados
- [ ] **Links**: Referências aos PDFs corretas
- [ ] **Formatação**: Consistente com padrão
- [ ] **SEO**: Meta tags adequadas

### ✅ Dados (projects.js)
- [ ] **Informações**: Completas e atualizadas
- [ ] **Métricas**: Precisas e relevantes
- [ ] **Categorização**: Correta
- [ ] **Imagens**: Galeria completa
- [ ] **Documentos**: Links funcionais

### ✅ Layout
- [ ] **Responsividade**: Funciona em todos os dispositivos
- [ ] **Performance**: Carregamento rápido
- [ ] **Acessibilidade**: Navegação por teclado
- [ ] **SEO**: Meta tags implementadas
- [ ] **UX**: Experiência do usuário fluida

## 🚨 PROBLEMAS COMUNS A VERIFICAR

### PDFs
- [ ] Arquivos muito grandes (> 10MB)
- [ ] Nomenclatura inconsistente
- [ ] PDFs duplicados (versões recorte vs completas)
- [ ] Links quebrados
- [ ] Qualidade de imagem baixa

### Markdown
- [ ] Conteúdo genérico ou incompleto
- [ ] Links para PDFs incorretos
- [ ] Falta de estrutura hierárquica
- [ ] Meta tags ausentes
- [ ] Formatação inconsistente

### Dados
- [ ] Informações desatualizadas
- [ ] Métricas imprecisas
- [ ] Categorização incorreta
- [ ] Imagens ausentes ou quebradas
- [ ] Documentos não mapeados

## 📝 TEMPLATE DE RELATÓRIO

### Projeto: [NOME]
**Data da Auditoria:** [DATA]
**Auditor:** [NOME]

#### ✅ Status Geral
- [ ] PDFs: ___/___ funcionais
- [ ] Markdown: ___/___ completos
- [ ] Dados: ___/___ corretos
- [ ] Layout: ___/___ adequado

#### 🔧 Problemas Encontrados
1. **PDFs:**
   - Problema 1: [Descrição]
   - Problema 2: [Descrição]

2. **Markdown:**
   - Problema 1: [Descrição]
   - Problema 2: [Descrição]

3. **Dados:**
   - Problema 1: [Descrição]
   - Problema 2: [Descrição]

#### 📋 Ações Necessárias
- [ ] Ação 1: [Descrição]
- [ ] Ação 2: [Descrição]
- [ ] Ação 3: [Descrição]

#### ⏱️ Tempo Estimado
- **PDFs**: ___ horas
- **Markdown**: ___ horas
- **Dados**: ___ horas
- **Layout**: ___ horas
- **Total**: ___ horas

---

*Este checklist deve ser preenchido para cada projeto individualmente.* 
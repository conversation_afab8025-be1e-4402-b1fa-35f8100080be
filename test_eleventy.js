// Teste do eleventyComputed.js
try {
  const projectsData = require('./src/site/_data/projects.js');
  const eleventyComputed = require('./src/site/_data/eleventyComputed.js');
  
  console.log('✅ projects.js importado com sucesso');
  console.log('✅ eleventyComputed.js importado com sucesso');
  
  // Testar featuredProjects
  const featuredProjects = eleventyComputed.featuredProjects();
  console.log('✅ featuredProjects executado com sucesso');
  console.log('Total de featuredProjects:', featuredProjects.length);
  console.log('Primeiros 3 featuredProjects:');
  featuredProjects.slice(0, 3).forEach((project, index) => {
    console.log(`${index + 1}. ${project.title} (${project.category})`);
  });
  
  // Testar carouselProjects
  const carouselProjects = eleventyComputed.carouselProjects();
  console.log('✅ carouselProjects executado com sucesso');
  console.log('Total de carouselProjects:', carouselProjects.length);
  console.log('carouselProjects:');
  carouselProjects.forEach((project, index) => {
    console.log(`${index + 1}. ${project.title} (${project.category})`);
  });
  
} catch (error) {
  console.error('❌ Erro:', error.message);
  console.error('Stack:', error.stack);
} 
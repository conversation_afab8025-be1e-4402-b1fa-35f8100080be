#!/usr/bin/env python3
"""
Teste simples para verificar se o rembg está funcionando
"""

import os
from rembg import remove
from PIL import Image

def test_rembg():
    """Testa a funcionalidade básica do rembg"""
    
    input_image = "src/site/assets/favicon/pvp.jpg"
    
    if not os.path.exists(input_image):
        print(f"❌ Arquivo não encontrado: {input_image}")
        return False
    
    try:
        print("🧪 Testando rembg...")
        
        # Lê a imagem
        with open(input_image, "rb") as f:
            input_data = f.read()
        
        print("📖 Imagem lida com sucesso")
        
        # Remove o fundo (versão simples)
        output_data = remove(input_data)
        
        print("✅ Fundo removido com sucesso")
        
        # Salva o resultado
        output_path = "src/site/assets/favicon/teste_sem_fundo.png"
        with open(output_path, "wb") as f:
            f.write(output_data)
        
        print(f"💾 Resultado salvo em: {output_path}")
        
        # Verifica a imagem
        with Image.open(output_path) as img:
            print(f"📏 Dimensões: {img.size}")
            print(f"🎨 Modo: {img.mode}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_rembg() 
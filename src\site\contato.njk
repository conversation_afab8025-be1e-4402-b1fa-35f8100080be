---
layout: layouts/base.njk
title: Contato
description: Entre em contato com a PVP Projects para projetos de engenharia elétrica e hidrossanitária. Orçamentos e consultoria em Porto Alegre.
permalink: /contato/
---

<!-- Hero Section -->
<section class="bg-gradient-to-br from-primary-50 to-secondary-50 py-20">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center" data-aos="fade-up">
      <h1 class="text-4xl lg:text-5xl font-bold text-primary-600 mb-6">
        Entre em Contato
      </h1>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
        Estamos prontos para transformar suas ideias em projetos executáveis. 
        Solicite um orçamento personalizado e descubra como podemos ajudar.
      </p>
    </div>
  </div>
</section>

<!-- Contact Section -->
<section class="py-20 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
      
      <!-- Contact Information -->
      <div data-aos="fade-right">
        <h2 class="text-3xl font-bold text-gray-900 mb-8">Informações de Contato</h2>
        
        <div class="space-y-6">
          <!-- Engineer Info -->
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">Eng. Pedro Vitor Pagliarin</h3>
              <p class="text-gray-600">Engenheiro Eletricista - UFRGS</p>
              <p class="text-gray-600">8 anos de experiência</p>
            </div>
          </div>

          <!-- Email -->
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">Email</h3>
              <a href="mailto:{{ company.contact.email }}" class="text-primary-600 hover:text-primary-700 transition-colors duration-200">
                {{ company.contact.email }}
              </a>
            </div>
          </div>

          <!-- Phone -->
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">Telefone</h3>
              <a href="tel:{{ company.contact.phone }}" class="text-primary-600 hover:text-primary-700 transition-colors duration-200">
                {{ company.contact.phone }}
              </a>
            </div>
          </div>

          <!-- WhatsApp -->
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">WhatsApp</h3>
              <a href="https://wa.me/{{ company.contact.whatsapp | replace('+', '') | replace(' ', '') | replace('-', '') | replace('(', '') | replace(')', '') }}" 
                 target="_blank"
                 class="text-green-600 hover:text-green-700 transition-colors duration-200">
                {{ company.contact.whatsapp }}
              </a>
            </div>
          </div>

          <!-- Address -->
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">Localização</h3>
              <p class="text-gray-600">{{ company.contact.address }}</p>
            </div>
          </div>
        </div>

        <!-- Business Hours -->
        <div class="mt-8 p-6 bg-gray-50 rounded-lg">
          <h3 class="font-semibold text-gray-900 mb-4">Horário de Atendimento</h3>
          <div class="space-y-2 text-gray-600">
            <div class="flex justify-between">
              <span>Segunda a Sexta</span>
              <span>8h às 18h</span>
            </div>
            <div class="flex justify-between">
              <span>Sábado</span>
              <span>8h às 12h</span>
            </div>
            <div class="flex justify-between">
              <span>Domingo</span>
              <span>Fechado</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Contact Form -->
      <div data-aos="fade-left">
        <h2 class="text-3xl font-bold text-gray-900 mb-8">Solicitar Orçamento</h2>
        
        <form 
          name="contato" 
          method="POST" 
          data-netlify="true" 
          netlify-honeypot="bot-field"
          data-validate="true"
          class="space-y-6"
        >
          <input type="hidden" name="form-name" value="contato">
          
          <!-- Honeypot field -->
          <p class="hidden">
            <label>Não preencha este campo: <input name="bot-field"></label>
          </p>

          <!-- Name -->
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
              Nome Completo *
            </label>
            <input 
              type="text" 
              id="name" 
              name="name" 
              required
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
              placeholder="Seu nome completo"
            >
          </div>

          <!-- Email -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
              Email *
            </label>
            <input 
              type="email" 
              id="email" 
              name="email" 
              required
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
              placeholder="<EMAIL>"
            >
          </div>

          <!-- Phone -->
          <div>
            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
              Telefone
            </label>
            <input 
              type="tel" 
              id="phone" 
              name="phone"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
              placeholder="(51) 99999-9999"
            >
          </div>

          <!-- Service Type -->
          <div>
            <label for="service" class="block text-sm font-medium text-gray-700 mb-2">
              Tipo de Serviço *
            </label>
            <select 
              id="service" 
              name="service" 
              required
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
            >
              <option value="">Selecione um serviço</option>
              <option value="eletrico">Projeto Elétrico</option>
              <option value="hidrossanitario">Projeto Hidrossanitário</option>
              <option value="comunicacao">Projeto de Comunicação</option>
              <option value="consultoria">Consultoria BIM</option>
              <option value="outro">Outro</option>
            </select>
          </div>

          <!-- Project Type -->
          <div>
            <label for="project-type" class="block text-sm font-medium text-gray-700 mb-2">
              Tipo de Projeto *
            </label>
            <select 
              id="project-type" 
              name="project-type" 
              required
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
            >
              <option value="">Selecione o tipo de projeto</option>
              <option value="residencial">Residencial</option>
              <option value="comercial">Comercial</option>
              <option value="predial">Predial</option>
              <option value="institucional">Institucional</option>
              <option value="industrial">Industrial</option>
              <option value="outro">Outro</option>
            </select>
          </div>

          <!-- Project Area -->
          <div>
            <label for="area" class="block text-sm font-medium text-gray-700 mb-2">
              Área do Projeto (m²)
            </label>
            <input 
              type="number" 
              id="area" 
              name="area"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
              placeholder="Ex: 150"
            >
          </div>

          <!-- Timeline -->
          <div>
            <label for="timeline" class="block text-sm font-medium text-gray-700 mb-2">
              Prazo Desejado
            </label>
            <select 
              id="timeline" 
              name="timeline"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
            >
              <option value="">Selecione o prazo</option>
              <option value="urgente">Urgente (1-2 semanas)</option>
              <option value="rapido">Rápido (1 mês)</option>
              <option value="normal">Normal (2-3 meses)</option>
              <option value="flexivel">Flexível</option>
            </select>
          </div>

          <!-- Message -->
          <div>
            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
              Descrição do Projeto *
            </label>
            <textarea 
              id="message" 
              name="message" 
              required
              rows="4"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200 resize-none"
              placeholder="Descreva seu projeto, necessidades específicas e qualquer informação relevante..."
            ></textarea>
          </div>

          <!-- Budget Range -->
          <div>
            <label for="budget" class="block text-sm font-medium text-gray-700 mb-2">
              Faixa de Orçamento
            </label>
            <select 
              id="budget" 
              name="budget"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
            >
              <option value="">Selecione a faixa</option>
              <option value="ate-5k">Até R$ 5.000</option>
              <option value="5k-10k">R$ 5.000 - R$ 10.000</option>
              <option value="10k-25k">R$ 10.000 - R$ 25.000</option>
              <option value="25k-50k">R$ 25.000 - R$ 50.000</option>
              <option value="acima-50k">Acima de R$ 50.000</option>
              <option value="nao-definido">Ainda não definido</option>
            </select>
          </div>

          <!-- How did you find us -->
          <div>
            <label for="source" class="block text-sm font-medium text-gray-700 mb-2">
              Como nos conheceu?
            </label>
            <select 
              id="source" 
              name="source"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200"
            >
              <option value="">Selecione uma opção</option>
              <option value="google">Google</option>
              <option value="indicacao">Indicação</option>
              <option value="redes-sociais">Redes Sociais</option>
              <option value="site">Site</option>
              <option value="outro">Outro</option>
            </select>
          </div>

          <!-- Submit Button -->
          <button 
            type="submit"
            class="w-full bg-primary-600 text-white py-4 px-6 rounded-lg hover:bg-primary-700 transition-colors duration-200 font-semibold text-lg"
          >
            Enviar Solicitação
          </button>
        </form>
      </div>
    </div>
  </div>
</section>

<!-- Map Section -->
<section class="py-20 bg-gray-50">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12" data-aos="fade-up">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Nossa Área de Atuação</h2>
      <p class="text-xl text-gray-600">
        Atendemos <strong>todo o território nacional</strong> com projetos de engenharia de qualidade.<br>
        <span class="text-lg text-gray-500">Concentração principal: Porto Alegre e região metropolitana do Rio Grande do Sul</span>
      </p>
    </div>
    
    <div class="bg-white rounded-xl shadow-lg overflow-hidden" data-aos="fade-up" data-aos-delay="100">
      <div class="aspect-w-16 aspect-h-9">
        <iframe 
          src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3454.5!2d-51.2304!3d-30.0346!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMzDCsDAyJzA0LjYiUyA1McKwMTMnNDkuNCJX!5e0!3m2!1spt-BR!2sbr!4v1234567890"
          width="100%" 
          height="400" 
          style="border:0;" 
          allowfullscreen="" 
          loading="lazy" 
          referrerpolicy="no-referrer-when-downgrade"
          title="Localização PVP Projects"
        ></iframe>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-20 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12" data-aos="fade-up">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Perguntas Frequentes</h2>
      <p class="text-xl text-gray-600">
        Respostas para as dúvidas mais comuns sobre nossos serviços
      </p>
    </div>
    
    <div class="max-w-4xl mx-auto space-y-6" data-aos="fade-up" data-aos-delay="100">
      
      <!-- FAQ 1 -->
      <div class="border border-gray-200 rounded-lg">
        <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-primary-500" onclick="toggleFAQ(this)">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900">Qual o prazo médio para um projeto?</h3>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>
        <div class="px-6 pb-4 hidden">
          <p class="text-gray-600">
            Projetos residenciais simples podem levar de 3 semanas a 2 meses. Projetos comerciais complexos costumam levar de 2 a 3 meses. Sempre definimos um cronograma inicial no começo do projeto, com marcos de entrega e reuniões de revisão.
          </p>
        </div>
      </div>

      <!-- FAQ 2 -->
      <div class="border border-gray-200 rounded-lg">
        <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-primary-500" onclick="toggleFAQ(this)">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900">Vocês atendem outras cidades além de Porto Alegre?</h3>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>
        <div class="px-6 pb-4 hidden">
          <p class="text-gray-600">
            Sim. Atendemos todo o Rio Grande do Sul e realizamos projetos em todo o território nacional (Brasil), de forma presencial ou remota, conforme a necessidade.
          </p>
        </div>
      </div>

      <!-- FAQ 3 -->
      <div class="border border-gray-200 rounded-lg">
        <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-primary-500" onclick="toggleFAQ(this)">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900">Como funciona o processo de orçamento?</h3>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>
        <div class="px-6 pb-4 hidden">
          <p class="text-gray-600">
            Após receber sua solicitação, analisamos os requisitos e enviamos um orçamento detalhado em até 48h úteis (escopo, prazo e condições). Quando necessário, agendamos uma reunião rápida para alinhar premissas e evitar retrabalho.
          </p>
        </div>
      </div>

      <!-- FAQ 4 -->
      <div class="border border-gray-200 rounded-lg">
        <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-primary-500" onclick="toggleFAQ(this)">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900">Quais documentos são necessários para iniciar o projeto?</h3>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>
        <div class="px-6 pb-4 hidden">
          <p class="text-gray-600">
            Em geral: planta arquitetônica atualizada (DWG/PDF), memorial/brief com necessidades, referências de padrão de acabamentos (quando houver), levantamento de cargas/equipamentos especiais, dados do terreno/edificação e normas da concessionária aplicáveis. Para projetos específicos, poderemos solicitar documentos adicionais.
          </p>
        </div>
      </div>

      <!-- FAQ 5 -->
      <div class="border border-gray-200 rounded-lg">
        <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-primary-500" onclick="toggleFAQ(this)">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900">Como é o fluxo de trabalho de vocês?</h3>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>
        <div class="px-6 pb-4 hidden">
          <div class="text-gray-600 space-y-2">
            <p><strong>1. Levantamento</strong> → necessidades e requisitos.</p>
            <p><strong>2. Estudo preliminar</strong> → análise inicial + ideias de solução (apresentação).</p>
            <p><strong>3. Reunião de deliberação</strong> → ajustes e diretrizes do cliente.</p>
            <p><strong>4. Anteprojeto</strong> → layout e soluções definidas para validação.</p>
            <p><strong>5. Revisões finais</strong> → alterações solicitadas.</p>
            <p><strong>6. Projeto executivo</strong> → detalhamento completo para obra.</p>
            <p><strong>7. Entrega final</strong> → arquivos, memoriais e checklist.</p>
          </div>
        </div>
      </div>

      <!-- FAQ 6 -->
      <div class="border border-gray-200 rounded-lg">
        <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-primary-500" onclick="toggleFAQ(this)">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900">Vocês fazem compatibilização com outras disciplinas?</h3>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>
        <div class="px-6 pb-4 hidden">
          <p class="text-gray-600">
            Sim. Realizamos compatibilização BIM (Revit) e/ou CAD entre Arquitetura, Elétrica, Hidrossanitário, Comunicação/TI e Estrutural, com clash detection, ajustes de furos e passagens coordenadas.
          </p>
        </div>
      </div>

      <!-- FAQ 7 -->
      <div class="border border-gray-200 rounded-lg">
        <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-primary-500" onclick="toggleFAQ(this)">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900">Quais normas e exigências legais vocês atendem?</h3>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>
        <div class="px-6 pb-4 hidden">
          <p class="text-gray-600">
            Atuamos conforme ABNT (ex.: NBR 5410, NBR 5626, NBR 8160, NBR 14565 e NBR 5419 quando aplicável) e padrões de concessionárias. Emitimos ART conforme o escopo contratado.
          </p>
        </div>
      </div>

      <!-- FAQ 8 -->
      <div class="border border-gray-200 rounded-lg">
        <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-primary-500" onclick="toggleFAQ(this)">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900">Vocês cuidam da aprovação junto às concessionárias?</h3>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>
        <div class="px-6 pb-4 hidden">
          <p class="text-gray-600">
            Sim. Preparamos e protocolamos a documentação técnica para concessionárias (energia, água/esgoto etc.), acompanhando exigências e ajustes até a aprovação, quando este item estiver no escopo.
          </p>
        </div>
      </div>

      <!-- FAQ 9 -->
      <div class="border border-gray-200 rounded-lg">
        <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-primary-500" onclick="toggleFAQ(this)">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900">O que está incluído nos entregáveis?</h3>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>
        <div class="px-6 pb-4 hidden">
                      <div class="text-gray-600 space-y-2">
              <p>• Pranchas executivas (PDF) com plantas, cortes, esquemas e detalhes;</p>
              <p>• Memorial descritivo e quantitativos;</p>
              <p>• Diagramas (uni/multifilar, quando aplicável);</p>
              <p>• Checklist para obra e as built (se previsto no contrato).</p>
            </div>
        </div>
      </div>

      <!-- FAQ 10 -->
      <div class="border border-gray-200 rounded-lg">
        <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-primary-500" onclick="toggleFAQ(this)">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900">Quantas revisões estão incluídas?</h3>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>
        <div class="px-6 pb-4 hidden">
          <p class="text-gray-600">
            Trabalhamos com ciclos de revisão para garantir qualidade e agilidade. Em geral, consideramos 2 rodadas de revisão por etapa (estudo/anteprojeto), sem alterar o escopo. Necessidades adicionais são combinadas previamente.
          </p>
        </div>
      </div>

      <!-- FAQ 11 -->
      <div class="border border-gray-200 rounded-lg">
        <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-primary-500" onclick="toggleFAQ(this)">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900">Vocês oferecem suporte durante a obra?</h3>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>
        <div class="px-6 pb-4 hidden">
          <p class="text-gray-600">
            Sim. Prestamos suporte técnico com respostas a RFIs, ajustes pontuais e, quando contratado, visitas técnicas e atualização de as built.
          </p>
        </div>
      </div>

      <!-- FAQ 12 -->
      <div class="border border-gray-200 rounded-lg">
        <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-primary-500" onclick="toggleFAQ(this)">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900">Como é feita a comunicação e acompanhamento?</h3>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>
        <div class="px-6 pb-4 hidden">
          <p class="text-gray-600">
            Definimos pontos de controle (checkpoints) e reuniões rápidas em cada fase. O acompanhamento pode ser por WhatsApp, e-mail e reuniões on-line, com atas/resumos para registrar decisões.
          </p>
        </div>
      </div>

      <!-- FAQ 13 -->
      <div class="border border-gray-200 rounded-lg">
        <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-primary-500" onclick="toggleFAQ(this)">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900">Como envio os arquivos?</h3>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>
        <div class="px-6 pb-4 hidden">
          <p class="text-gray-600">
            Aceitamos DWG, RVT e PDFs via link (Drive/WeTransfer) ou e-mail. Se preferir, fornecemos uma pasta compartilhada organizada para o projeto.
          </p>
        </div>
      </div>

      <!-- FAQ 14 -->
      <div class="border border-gray-200 rounded-lg">
        <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-primary-500" onclick="toggleFAQ(this)">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900">Quais são as formas de pagamento?</h3>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>
        <div class="px-6 pb-4 hidden">
          <p class="text-gray-600">
            Trabalhamos com parcelamento por marcos (ex.: 30/40/30% — assinatura/início, aprovação do anteprojeto, entrega do executivo) ou conforme política do cliente. Emitimos NF quando aplicável.
          </p>
        </div>
      </div>

      <!-- FAQ 15 -->
      <div class="border border-gray-200 rounded-lg">
        <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-primary-500" onclick="toggleFAQ(this)">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900">Vocês trabalham com prazos de urgência?</h3>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>
        <div class="px-6 pb-4 hidden">
          <p class="text-gray-600">
            Sim, avaliamos cronogramas acelerados. Em casos de urgência, ajustamos escopo/recursos e informamos acréscimo de custo (se houver) antes do início.
          </p>
        </div>
      </div>

      <!-- FAQ 16 -->
      <div class="border border-gray-200 rounded-lg">
        <button class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-primary-500" onclick="toggleFAQ(this)">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-gray-900">Quem é o responsável técnico?</h3>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </button>
        <div class="px-6 pb-4 hidden">
          <p class="text-gray-600">
            O projeto é conduzido por Engenheiro Eletricista com emissão de ART e responsabilidade técnica conforme o escopo.
          </p>
        </div>
      </div>

    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-primary-600">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <div data-aos="fade-up">
      <h2 class="text-3xl lg:text-4xl font-bold text-white mb-6">
        Pronto para começar seu projeto?
      </h2>
      <p class="text-xl text-primary-100 mb-8 max-w-3xl mx-auto">
        Entre em contato conosco hoje mesmo e descubra como podemos transformar suas ideias em realidade.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="https://wa.me/{{ company.contact.whatsapp | replace('+', '') | replace(' ', '') | replace('-', '') | replace('(', '') | replace(')', '') }}" 
           target="_blank"
           class="bg-green-600 text-white px-8 py-4 rounded-lg hover:bg-green-700 transition-colors duration-200 font-semibold text-lg inline-flex items-center justify-center">
          <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
          </svg>
          WhatsApp
        </a>
        <a href="mailto:{{ company.contact.email }}" 
           class="bg-white text-primary-600 px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors duration-200 font-semibold text-lg inline-flex items-center justify-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
          </svg>
          Email
        </a>
      </div>
    </div>
  </div>
</section>

<!-- FAQ JavaScript -->
<script>
function toggleFAQ(button) {
  const content = button.nextElementSibling;
  const icon = button.querySelector('svg');
  
  if (content.classList.contains('hidden')) {
    content.classList.remove('hidden');
    icon.style.transform = 'rotate(180deg)';
  } else {
    content.classList.add('hidden');
    icon.style.transform = 'rotate(0deg)';
  }
}
</script> 
---
pagination:
  data: projects.projects
  size: 1
  alias: project
permalink: "/projetos/{{ project.id }}/"
layout: layouts/project.njk
eleventyComputed:
  title: "{{ project.title }}"
  description: "{{ project.description }}"
  keywords: "{{ project.category }}, {{ project.subcategory }}, {{ project.client }}"
  ogImage: "{{ project.images[0].src if project.images else '' }}"
---

<!-- Hero Section com dados dinâmicos -->
<section class="bg-gradient-to-br from-primary-50 to-secondary-50 py-20">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center" data-aos="fade-up">
      <h1 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
        {{ project.title }}
      </h1>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
        {{ project.subtitle }}
      </p>
      <p class="text-lg text-gray-700 max-w-4xl mx-auto mb-8">
        {{ project.description }}
      </p>
      
      <!-- Breadcrumb -->
      <nav class="flex justify-center mb-8" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2 text-sm text-gray-600">
          <li><a href="/" class="hover:text-primary-600 transition-colors duration-200">Home</a></li>
          <li class="flex items-center">
            <svg class="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <a href="/projetos" class="hover:text-primary-600 transition-colors duration-200">Projetos</a>
          </li>
          <li class="flex items-center">
            <svg class="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <a href="/projetos/categoria/{{ project.category }}" class="hover:text-primary-600 transition-colors duration-200">{{ project.category | capitalize }}</a>
          </li>
          <li class="flex items-center">
            <svg class="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-primary-600 font-medium">{{ project.title }}</span>
          </li>
        </ol>
      </nav>
    </div>
  </div>
</section>

<!-- Informações do Projeto -->
<section class="py-16 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-6xl mx-auto">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        
        <!-- Coluna Principal - Detalhes do Projeto -->
        <div class="lg:col-span-2">
          
          <!-- Informações Básicas -->
          <div class="bg-gray-50 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Informações do Projeto</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 class="font-semibold text-gray-900 mb-2">Cliente</h3>
                <p class="text-gray-700">{{ project.client }}</p>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900 mb-2">Localização</h3>
                <p class="text-gray-700">{{ project.location }}</p>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900 mb-2">Ano</h3>
                <p class="text-gray-700">{{ project.year }}</p>
              </div>
              {% if project.details and project.details.area %}
              <div>
                <h3 class="font-semibold text-gray-900 mb-2">Área</h3>
                <p class="text-gray-700">{{ project.details.area }}</p>
              </div>
              {% endif %}
              {% if project.details and project.details.pavimentos %}
              <div>
                <h3 class="font-semibold text-gray-900 mb-2">Pavimentos</h3>
                <p class="text-gray-700">{{ project.details.pavimentos }}</p>
              </div>
              {% endif %}
              {% if project.details and project.details.ferramentas %}
              <div>
                <h3 class="font-semibold text-gray-900 mb-2">Ferramentas</h3>
                <p class="text-gray-700">{{ project.details.ferramentas | join(', ') }}</p>
              </div>
              {% endif %}
            </div>
          </div>

          <!-- Desafios, Soluções e Resultados -->
          {% if project.challenges %}
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Desafios</h2>
            <ul class="space-y-3">
              {% for challenge in project.challenges %}
              <li class="flex items-start">
                <span class="text-primary-600 mr-3 mt-1">•</span>
                <span class="text-gray-700">{{ challenge }}</span>
              </li>
              {% endfor %}
            </ul>
          </div>
          {% endif %}

          {% if project.solutions %}
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Soluções</h2>
            <ul class="space-y-3">
              {% for solution in project.solutions %}
              <li class="flex items-start">
                <span class="text-secondary-600 mr-3 mt-1">•</span>
                <span class="text-gray-700">{{ solution }}</span>
              </li>
              {% endfor %}
            </ul>
          </div>
          {% endif %}

          {% if project.results %}
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Resultados</h2>
            <ul class="space-y-3">
              {% for result in project.results %}
              <li class="flex items-start">
                <span class="text-success-600 mr-3 mt-1">•</span>
                <span class="text-gray-700">{{ result }}</span>
              </li>
              {% endfor %}
            </ul>
          </div>
          {% endif %}

        </div>

        <!-- Sidebar - Métricas e Documentos -->
        <div class="lg:col-span-1">
          
          <!-- Métricas do Projeto -->
          {% if project.metrics %}
          <div class="bg-primary-50 rounded-lg p-6 mb-8">
            <h3 class="text-xl font-bold text-gray-900 mb-4">Métricas</h3>
            <div class="space-y-4">
              {% if project.metrics.complexity %}
              <div class="flex justify-between">
                <span class="text-gray-700">Complexidade</span>
                <span class="font-semibold text-primary-600">{{ project.metrics.complexity }}/10</span>
              </div>
              {% endif %}
              {% if project.metrics.area %}
              <div class="flex justify-between">
                <span class="text-gray-700">Área</span>
                <span class="font-semibold text-primary-600">{{ project.metrics.area }} m²</span>
              </div>
              {% endif %}
              {% if project.metrics.duration %}
              <div class="flex justify-between">
                <span class="text-gray-700">Duração</span>
                <span class="font-semibold text-primary-600">{{ project.metrics.duration }}</span>
              </div>
              {% endif %}
              {% if project.metrics.team %}
              <div class="flex justify-between">
                <span class="text-gray-700">Equipe</span>
                <span class="font-semibold text-primary-600">{{ project.metrics.team }} pessoa(s)</span>
              </div>
              {% endif %}
            </div>
          </div>
          {% endif %}

          <!-- Documentos -->
          {% if project.documents %}
          <div class="bg-gray-50 rounded-lg p-6 mb-8">
            <h3 class="text-xl font-bold text-gray-900 mb-4">Documentos</h3>
            <div class="space-y-3">
              {% for document in project.documents %}
              <a href="{{ document.url }}" target="_blank" class="flex items-center p-3 bg-white rounded-lg border border-gray-200 hover:border-primary-300 hover:shadow-md transition-all duration-200">
                <svg class="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">{{ document.name }}</span>
              </a>
              {% endfor %}
            </div>
          </div>
          {% endif %}

        </div>
      </div>
    </div>
  </div>
</section>

<!-- Galeria de Imagens -->
{% if project.images %}
<section class="py-16 bg-gray-50">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-6xl mx-auto">
      <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">Galeria do Projeto</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {% for image in project.images %}
        <div class="bg-white rounded-lg shadow-lg overflow-hidden" data-aos="fade-up" data-aos-delay="{{ loop.index * 100 }}">
          <img src="{{ image.src }}" alt="{{ image.alt }}" class="w-full h-64 object-cover">
          <div class="p-4">
            <p class="text-gray-700 font-medium">{{ image.caption }}</p>
          </div>
        </div>
        {% endfor %}
      </div>
    </div>
  </div>
</section>
{% endif %}

<!-- Normas Técnicas -->
{% if project.details and project.details.normas %}
<section class="py-16 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
      <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">Normas Técnicas Aplicadas</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        {% for norma in project.details.normas %}
        <div class="bg-gray-50 rounded-lg p-6 text-center">
          <h3 class="font-semibold text-gray-900 mb-2">{{ norma }}</h3>
        </div>
        {% endfor %}
      </div>
    </div>
  </div>
</section>
{% endif %}

<!-- Call to Action -->
<section class="py-16 bg-primary-600">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto text-center">
      <h2 class="text-3xl font-bold text-white mb-6">Interessado em um projeto similar?</h2>
      <p class="text-xl text-primary-100 mb-8">Entre em contato conosco para discutir suas necessidades</p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="/contato" class="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200">
          Solicitar Orçamento
        </a>
        <a href="/projetos" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors duration-200">
          Ver Mais Projetos
        </a>
      </div>
    </div>
  </div>
</section> 
<!DOCTYPE html>
<html lang="pt-BR" data-theme="light">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{% if title %}{{ title }} - {% endif %}{{ meta.site.name }}</title>
  <meta name="description" content="{% if description %}{{ description }}{% else %}{{ meta.site.description }}{% endif %}">
  
  <!-- SEO Meta Tags -->
  <meta name="keywords" content="{% if keywords %}{{ keywords }}{% else %}engenharia elétrica, projetos hidrossanitários, Porto Alegre, engenheiro eletricista, Revit, AutoCAD{% endif %}">
  <meta name="author" content="{{ meta.site.author }}">
  <meta name="robots" content="index, follow">
  <meta name="theme-color" content="#1e40af">
  <meta name="msapplication-TileColor" content="#1e40af">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="{{ meta.site.name }}">
  
  <!-- Open Graph -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="{% if title %}{{ title }}{% else %}{{ meta.site.name }}{% endif %}">
  <meta property="og:description" content="{% if description %}{{ description }}{% else %}{{ meta.site.description }}{% endif %}">
  <meta property="og:url" content="{{ meta.site.url }}{{ page.url }}">
  <meta property="og:image" content="{% if ogImage %}{{ ogImage }}{% else %}{{ meta.site.url }}/assets/images/og-default.jpg{% endif %}">
  <meta property="og:site_name" content="{{ meta.site.name }}">
  <meta property="og:locale" content="pt_BR">
  
  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="{% if title %}{{ title }}{% else %}{{ meta.site.name }}{% endif %}">
  <meta name="twitter:description" content="{% if description %}{{ description }}{% else %}{{ meta.site.description }}{% endif %}">
  <meta name="twitter:image" content="{% if ogImage %}{{ ogImage }}{% else %}{{ meta.site.url }}/assets/images/og-default.jpg{% endif %}">
  
  <!-- Favicon -->
  <link rel="icon" type="image/png" href="/assets/imagens/favicon-new.png">
  <link rel="apple-touch-icon" href="/assets/imagens/favicon-new.png">
  
  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  
  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
  
  <!-- Preload critical resources -->
  <link rel="preload" href="/styles/style.css" as="style">
  <link rel="preload" href="/scripts/main.js" as="script">
  <link rel="dns-prefetch" href="https://fonts.googleapis.com">
  <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">
  
  <!-- Styles -->
  <link rel="stylesheet" href="/styles/style.css">
  
  <!-- AOS CSS -->
  <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
  
  <!-- Schema.org Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "{{ meta.site.name }}",
    "url": "{{ meta.site.url }}",
    "logo": "{{ meta.site.url }}/assets/imagens/favicon-new.png",
    "description": "{{ meta.site.description }}",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Porto Alegre",
      "addressRegion": "RS",
      "addressCountry": "BR"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "{{ company.contact.phone }}",
      "contactType": "customer service",
      "email": "{{ company.contact.email }}"
    },
    "founder": {
      "@type": "Person",
      "name": "Pedro Vitor Pagliarin",
      "jobTitle": "Engenheiro Eletricista",
      "alumniOf": {
        "@type": "CollegeOrUniversity",
        "name": "Universidade Federal do Rio Grande do Sul"
      }
    }
  }
  </script>
  
  <!-- Google Analytics (se configurado) -->
  {% if meta.analytics.googleAnalytics != 'G-XXXXXXXXXX' %}
  <script async src="https://www.googletagmanager.com/gtag/js?id={{ meta.analytics.googleAnalytics }}"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', '{{ meta.analytics.googleAnalytics }}');
  </script>
  {% endif %}
</head>

<body class="antialiased bg-white text-gray-900 transition-colors duration-200" data-theme="light">
  <!-- Skip to content link for accessibility -->
  <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-lg z-50">
    Pular para o conteúdo principal
  </a>

  <!-- Loading Spinner -->
  <div class="loader fixed inset-0 bg-white z-50 flex items-center justify-center transition-opacity duration-300">
    <div class="animate-spin rounded-full h-12 w-12 border-4 border-primary-600 border-t-transparent"></div>
  </div>

  <!-- Header -->
  <header class="sticky top-0 z-40 bg-white/80 backdrop-blur-md border-b border-gray-200 transition-all duration-200">
    <nav class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <div class="flex-shrink-0">
          <a href="/" class="flex items-center space-x-4">
            <img src="/assets/imagens/favicon-new.png" 
                 alt="PVP Projects Logo" 
                 class="w-20 h-20 object-contain rounded-full"/>
            <span class="font-bold text-xl text-gray-900">{{ company.name }}</span>
          </a>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:block">
          <div class="flex items-center space-x-8">
            {% for item in navigation.main %}
            <div class="relative group">
              <a href="{{ item.url }}" class="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium">
                {{ item.title }}
              </a>
              
              {% if item.children %}
              <div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <div class="py-2">
                  {% for child in item.children %}
                  <a href="{{ child.url }}" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200">
                    <div class="font-medium">{{ child.title }}</div>
                    <div class="text-sm text-gray-500">{{ child.description }}</div>
                  </a>
                  {% endfor %}
                </div>
              </div>
              {% endif %}
            </div>
            {% endfor %}
          </div>
        </div>

        <!-- Right side -->
        <div class="flex items-center space-x-4">
          <!-- Theme Toggle -->
          <button 
            id="theme-toggle"
            class="p-2 text-gray-500 hover:text-gray-700 transition-colors duration-200"
            aria-label="Alternar tema"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
            </svg>
          </button>

          <!-- Search Button -->
          <button 
            id="search-toggle"
            class="p-2 text-gray-500 hover:text-gray-700 transition-colors duration-200"
            aria-label="Buscar"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </button>

          <!-- Mobile menu button -->
          <button 
            id="mobile-menu-toggle"
            class="md:hidden p-2 text-gray-500 hover:text-gray-700 transition-colors duration-200"
            aria-label="Abrir menu"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>

          <!-- CTA Button -->
          <a href="/contato" class="hidden md:inline-flex bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200 font-medium">
            Solicitar Orçamento
          </a>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <div id="mobile-menu" class="md:hidden hidden">
        <div class="px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200">
          {% for item in navigation.main %}
          <a href="{{ item.url }}" class="block px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200">
            {{ item.title }}
          </a>
          {% if item.children %}
          <div class="pl-4 space-y-1">
            {% for child in item.children %}
            <a href="{{ child.url }}" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200">
              {{ child.title }}
            </a>
            {% endfor %}
          </div>
          {% endif %}
          {% endfor %}
        </div>
      </div>
    </nav>
  </header>

  <!-- Search Modal -->
  <div id="search-modal" class="fixed inset-0 z-50 hidden">
    <div class="absolute inset-0 bg-black/50"></div>
    <div class="absolute inset-0 flex items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Buscar</h3>
            <button id="search-close" class="text-gray-400 hover:text-gray-600 transition-colors duration-200">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          <input 
            type="text" 
            id="search-input"
            placeholder="Digite para buscar projetos..."
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
          <div id="search-results" class="mt-4 max-h-96 overflow-y-auto hidden"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <main id="main-content">
    {{ content | safe }}
  </main>

  <!-- Footer -->
  <footer class="bg-gray-900 text-white">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Company Info -->
        <div class="md:col-span-2">
          <div class="flex items-center space-x-2 mb-4">
            <img src="/assets/imagens/favicon-new.png" 
                 alt="PVP Projects Logo" 
                 class="w-20 h-20 object-contain rounded-lg"/>
            <span class="font-bold text-xl">{{ company.name }}</span>
          </div>
          <p class="text-gray-300 mb-4 max-w-md">{{ company.about.description }}</p>
          <div class="flex space-x-4">
            {% for contact in navigation.quickContact %}
            <a 
              href="{{ contact.url }}" 
              target="_blank"
              class="text-gray-400 hover:text-white transition-colors duration-200"
              aria-label="{{ contact.title }}"
            >
              {% if contact.icon == 'whatsapp' %}
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
              </svg>
              {% elif contact.icon == 'mail' %}
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              {% elif contact.icon == 'linkedin' %}
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
              {% else %}
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              {% endif %}
            </a>
            {% endfor %}
          </div>
        </div>

        <!-- Footer Links -->
        {% for section in navigation.footer %}
        <div>
          <h3 class="font-semibold text-white mb-4">{{ section.title }}</h3>
          <ul class="space-y-2">
            {% for item in section.items %}
            <li>
              <a href="{{ item.url }}" class="text-gray-300 hover:text-white transition-colors duration-200">
                {{ item.title }}
              </a>
            </li>
            {% endfor %}
          </ul>
        </div>
        {% endfor %}
      </div>

      <!-- Bottom Footer -->
      <div class="border-t border-gray-800 mt-8 pt-8">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <p class="text-gray-400 text-sm">
            © 2024 {{ company.name }}. Todos os direitos reservados.
          </p>
          <div class="flex space-x-6 mt-4 md:mt-0">
            <a href="/legal/privacidade" class="text-gray-400 hover:text-white text-sm transition-colors duration-200">
              Política de Privacidade
            </a>
            <a href="/legal/termos" class="text-gray-400 hover:text-white text-sm transition-colors duration-200">
              Termos de Uso
            </a>
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- Scroll to Top Button -->
  <button 
    class="scroll-to-top fixed bottom-8 right-8 bg-primary-600 text-white p-3 rounded-full shadow-lg hover:bg-primary-700 transition-all duration-200 opacity-0 pointer-events-none z-50"
    aria-label="Voltar ao topo"
  >
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
    </svg>
  </button>

  <!-- Notification Container -->
  <div id="notification-container" class="fixed top-4 right-4 z-50"></div>

  <!-- WhatsApp Floating Button -->
  <a 
    href="https://wa.me/5554991590379" 
    target="_blank"
    class="fixed bottom-20 right-8 bg-green-500 text-white p-4 rounded-full shadow-lg hover:bg-green-600 transition-all duration-200 z-50 group"
    aria-label="Contatar via WhatsApp"
  >
    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
      <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
    </svg>
    <span class="absolute left-full ml-3 bg-white text-gray-800 px-3 py-2 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap text-sm font-medium">
      Fale conosco!
    </span>
  </a>

  <!-- Scripts -->
  <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
  <script src="/scripts/main.js"></script>
  
  <!-- Initialize AOS -->
  <script>
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true,
      offset: 100
    });
  </script>

  <!-- Mobile Menu Toggle -->
  <script>
    document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
      const menu = document.getElementById('mobile-menu');
      menu.classList.toggle('hidden');
    });

    // Search Modal
    document.getElementById('search-toggle').addEventListener('click', function() {
      document.getElementById('search-modal').classList.remove('hidden');
      document.getElementById('search-input').focus();
    });

    document.getElementById('search-close').addEventListener('click', function() {
      document.getElementById('search-modal').classList.add('hidden');
    });

    // Close search modal on escape
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        document.getElementById('search-modal').classList.add('hidden');
      }
    });
  </script>
</body>
</html> 
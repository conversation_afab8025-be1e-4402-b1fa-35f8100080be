{"name": "portfolio-engenharia", "version": "1.0.0", "description": "Portfólio profissional de engenharia com Eleventy", "main": "index.js", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "start": "npm-run-all get-theme build:css --parallel watch:*", "dev": "npm-run-all get-theme build:css --parallel watch:*", "watch:sass": "sass --watch src/site/styles:dist/styles", "watch:css": "npx tailwindcss -i ./src/site/styles/input.css -o ./dist/styles/style.css --watch", "watch:eleventy": "cross-env ELEVENTY_ENV=dev eleventy --serve", "build:eleventy": "cross-env ELEVENTY_ENV=prod NODE_OPTIONS=--max-old-space-size=4096 eleventy", "build:sass": "sass src/site/styles:dist/styles --style compressed", "build:css": "npx tailwindcss -i ./src/site/styles/input.css -o ./dist/styles/style.css --minify", "get-theme": "node src/site/get-theme.js", "build": "npm-run-all get-theme build:eleventy build:css", "lint": "eslint src --ext .js", "lint:fix": "eslint src --ext .js --fix", "format": "prettier --write \"src/**/*.{js,md,scss}\"", "format:check": "prettier --check \"src/**/*.{js,md,scss}\"", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "ci": "npm run lint && npm run format:check && npm run test:e2e && npm run build"}, "keywords": ["portfolio", "engenharia", "eleventy", "static-site"], "author": "<PERSON>", "license": "ISC", "devDependencies": {"@11ty/eleventy": "^2.0.1", "@11ty/eleventy-plugin-rss": "^1.2.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "cypress": "^13.6.4", "cypress-axe": "^1.5.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "html-minifier-terser": "^7.2.0", "node-html-parser": "^6.1.13", "postcss": "^8.5.6", "prettier": "^3.2.5", "sass": "^1.49.9", "tailwindcss": "^3.4.17"}, "dependencies": {"@11ty/eleventy-img": "^4.0.2", "@sindresorhus/slugify": "^1.1.0", "axe-core": "^4.8.4", "axios": "^1.2.2", "dotenv": "^16.0.3", "eleventy-plugin-gen-favicons": "^1.1.2", "eleventy-plugin-nesting-toc": "^1.3.0", "fs-file-tree": "^1.1.1", "glob": "^10.2.1", "gray-matter": "^4.0.3", "markdown-it": "^14.1.0", "markdown-it-anchor": "^9.0.1", "markdown-it-attrs": "^4.1.6", "markdown-it-footnote": "^3.0.3", "markdown-it-mark": "^4.0.0", "markdown-it-mathjax3": "^4.3.1", "markdown-it-plantuml": "^1.4.1", "markdown-it-task-checkbox": "^1.0.6", "npm-run-all": "^4.1.5", "rimraf": "^4.4.1"}}
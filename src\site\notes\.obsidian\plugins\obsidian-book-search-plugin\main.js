/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var ut = Object.defineProperty;
var Or = Object.getOwnPropertyDescriptor;
var Dr = Object.getOwnPropertyNames;
var Nr = Object.prototype.hasOwnProperty;
var Lr = (r, e) => {
    for (var t in e) ut(r, t, { get: e[t], enumerable: !0 });
  },
  Rr = (r, e, t, o) => {
    if ((e && typeof e == 'object') || typeof e == 'function')
      for (let i of Dr(e))
        !Nr.call(r, i) &&
          i !== t &&
          ut(r, i, {
            get: () => e[i],
            enumerable: !(o = Or(e, i)) || o.enumerable,
          });
    return r;
  };
var Fr = r => Rr(ut({}, '__esModule', { value: !0 }), r);
var Lo = {};
Lr(Lo, { default: () => at });
module.exports = Fr(Lo);
var oe = require('obsidian');
var Vt = require('obsidian');
var we = class we {
  constructor(e, t, o) {
    this.localePreference = e;
    this.enableCoverImageEdgeCurl = t;
    this.apiKey = o;
  }
  getLanguageRestriction(e) {
    return e === 'default' ? window.moment.locale() : e;
  }
  buildSearchParams(e, t) {
    let o = {
      q: e,
      maxResults: we.MAX_RESULTS,
      printType: we.PRINT_TYPE,
      langRestrict: this.getLanguageRestriction(
        (t == null ? void 0 : t.locale) || this.localePreference
      ),
    };
    return (this.apiKey && (o.key = this.apiKey), o);
  }
  async getByQuery(e, t) {
    try {
      let o = this.buildSearchParams(e, t),
        i = await Ue('https://www.googleapis.com/books/v1/volumes', o);
      return i != null && i.totalItems
        ? i.items.map(({ volumeInfo: n }) => this.createBookItem(n))
        : [];
    } catch (o) {
      throw (console.warn(o), o);
    }
  }
  extractISBNs(e) {
    var t;
    return (t =
      e == null
        ? void 0
        : e.reduce((o, i) => {
            let n = i.type === 'ISBN_10' ? 'isbn10' : 'isbn13';
            return ((o[n] = i.identifier.trim()), o);
          }, {})) != null
      ? t
      : {};
  }
  extractBasicBookInfo(e) {
    var t, o, i, n;
    return {
      title: e.title,
      subtitle: e.subtitle,
      author: this.formatList(e.authors),
      authors: e.authors,
      category: this.formatList(e.categories),
      categories: e.categories,
      publisher: e.publisher,
      totalPage: e.pageCount,
      coverUrl: this.setCoverImageEdgeCurl(
        (o = (t = e.imageLinks) == null ? void 0 : t.thumbnail) != null
          ? o
          : '',
        this.enableCoverImageEdgeCurl
      ),
      coverSmallUrl: this.setCoverImageEdgeCurl(
        (n = (i = e.imageLinks) == null ? void 0 : i.smallThumbnail) != null
          ? n
          : '',
        this.enableCoverImageEdgeCurl
      ),
      publishDate: e.publishedDate || '',
      description: e.description,
      link: e.canonicalVolumeLink || e.infoLink,
      previewLink: e.previewLink,
    };
  }
  createBookItem(e) {
    return {
      title: '',
      subtitle: '',
      author: '',
      authors: [],
      category: '',
      categories: [],
      publisher: '',
      publishDate: '',
      totalPage: '',
      coverUrl: '',
      coverSmallUrl: '',
      description: '',
      link: '',
      previewLink: '',
      ...this.extractBasicBookInfo(e),
      ...this.extractISBNs(e.industryIdentifiers),
    };
  }
  formatList(e) {
    var t;
    return e && e.length > 1
      ? e.map(o => o.trim()).join(', ')
      : (t = e == null ? void 0 : e[0]) != null
        ? t
        : '';
  }
  setCoverImageEdgeCurl(e, t) {
    return t ? e : e.replace('&edge=curl', '');
  }
  static convertGoogleBookImageURLSize(e, t) {
    return e.replace(/(&zoom)=\d/, `$1=${t}`);
  }
};
((we.MAX_RESULTS = 40), (we.PRINT_TYPE = 'books'));
var _e = we;
var We = class {
  constructor(e, t) {
    this.clientId = e;
    this.clientSecret = t;
  }
  async getByQuery(e) {
    try {
      let t = { query: e, display: 50, sort: 'sim' },
        o = {
          'X-Naver-Client-Id': this.clientId,
          'X-Naver-Client-Secret': this.clientSecret,
        },
        i = await Ue('https://openapi.naver.com/v1/search/book.json', t, o);
      return i != null && i.total ? i.items.map(this.createBookItem) : [];
    } catch (t) {
      throw (console.warn(t), t);
    }
  }
  createBookItem(e) {
    var t, o;
    return {
      title: e.title,
      author: e.author,
      publisher: e.publisher,
      coverUrl: e.image,
      publishDate: ((t = e.pubdate) == null ? void 0 : t.slice(0, 4)) || '',
      link: e.link,
      description: e.description,
      isbn: e.isbn,
      ...(((o = e.isbn) == null ? void 0 : o.length) >= 13
        ? { isbn13: e.isbn }
        : { isbn10: e.isbn }),
    };
  }
};
var mt = class extends Error {
  constructor(e) {
    (super(e), (this.name = 'ConfigurationError'));
  }
};
function _t(r) {
  switch (r.serviceProvider) {
    case 'google':
      return new _e(r.localePreference, r.enableCoverImageEdgeCurl, r.apiKey);
    case 'naver':
      return (Mr(r), new We(r.naverClientId, r.naverClientSecret));
    default:
      throw new Error('Unsupported service provider.');
  }
}
function Mr(r) {
  if (!r.naverClientId || !r.naverClientSecret)
    throw new mt(
      '\uB124\uC774\uBC84 \uAC1C\uBC1C\uC790\uC13C\uD130\uC5D0\uC11C "Client ID"\uC640 "Client Secret"\uB97C \uBC1C\uAE09\uBC1B\uC544 \uC124\uC815\uD574\uC8FC\uC138\uC694.'
    );
}
async function Ue(r, e = {}, t) {
  let o = new URL(r);
  return (
    $r(o, e),
    (
      await (0, Vt.requestUrl)({
        url: o.href,
        method: 'GET',
        headers: {
          Accept: '*/*',
          'Content-Type': 'application/json; charset=utf-8',
          ...t,
        },
      })
    ).json
  );
}
function $r(r, e) {
  Object.entries(e).forEach(([t, o]) => {
    r.searchParams.append(t, o.toString());
  });
}
var Ut = /^-?[0-9]*$/,
  dt = /{{DATE(\+-?[0-9]+)?}}/,
  gt = /{{DATE:([^}\n\r+]*)(\+-?[0-9]+)?}}/;
function Hr(r) {
  return r.replace(/[\\,#%&{}/*<>$":@.?|]/g, '').replace(/\s+/g, ' ');
}
function ht(r, e, t = 'md') {
  let o;
  return (
    e
      ? (o = Be(r, Ge(e)))
      : (o = r.author ? `${r.title} - ${r.author}` : r.title),
    Hr(o) + `.${t}`
  );
}
function jr(r) {
  return Object.entries(r).reduce((e, [t, o]) => ((e[Vr(t)] = o), e), {});
}
function Xt(r, e, t = 'Snake Case') {
  var n, l;
  let o = t === 'Camel Case' ? r : jr(r),
    i = typeof e == 'string' ? _r(e) : e;
  for (let a in i) {
    let s =
      (l = (n = i[a]) == null ? void 0 : n.toString().trim()) != null ? l : '';
    o[a] && o[a] !== s ? (o[a] = `${o[a]}, ${s}`) : (o[a] = s);
  }
  return o;
}
function Be(r, e) {
  return e != null && e.trim()
    ? Object.entries(r)
        .reduce(
          (o, [i, n = '']) => o.replace(new RegExp(`{{${i}}}`, 'ig'), n),
          e
        )
        .replace(/{{\w+}}/gi, '')
        .trim()
    : '';
}
function Vr(r) {
  return r.replace(/[A-Z]/g, e => `_${e == null ? void 0 : e.toLowerCase()}`);
}
function _r(r) {
  return r
    ? r
        .split(
          `
`
        )
        .map(e => {
          var n, l;
          let t = e.indexOf(':');
          if (t === -1) return [e.trim(), ''];
          let o = (n = e.slice(0, t)) == null ? void 0 : n.trim(),
            i = (l = e.slice(t + 1)) == null ? void 0 : l.trim();
          return [o, i];
        })
        .reduce((e, [t, o]) => {
          var i;
          return (
            t && (e[t] = (i = o == null ? void 0 : o.trim()) != null ? i : ''),
            e
          );
        }, {})
    : {};
}
function zt(r) {
  return Object.entries(r)
    .map(([e, t]) => {
      var i;
      let o = (i = t == null ? void 0 : t.toString().trim()) != null ? i : '';
      return /\r|\n/.test(o)
        ? ''
        : /:\s/.test(o)
          ? `${e}: "${o.replace(/"/g, '&quot;')}"
`
          : `${e}: ${o}
`;
    })
    .join('')
    .trim();
}
function Wt(r) {
  let e;
  return (
    (r == null ? void 0 : r.offset) !== null &&
      (r == null ? void 0 : r.offset) !== void 0 &&
      typeof r.offset == 'number' &&
      (e = window.moment.duration(r.offset, 'days')),
    r != null && r.format
      ? window
          .moment()
          .add(e)
          .format(r == null ? void 0 : r.format)
      : window.moment().add(e).format('YYYY-MM-DD')
  );
}
function Ge(r) {
  let e = r;
  for (; dt.test(e); ) {
    let t = dt.exec(e),
      o = 0;
    if (t != null && t[1]) {
      let i = t[1].replace('+', '').trim();
      Ut.test(i) && (o = parseInt(i));
    }
    e = Gt(e, dt, Wt({ offset: o }));
  }
  for (; gt.test(e); ) {
    let t = gt.exec(e),
      o = t == null ? void 0 : t[1],
      i = 0;
    if (t != null && t[2]) {
      let n = t[2].replace('+', '').trim();
      Ut.test(n) && (i = parseInt(n));
    }
    e = Gt(e, gt, Wt({ format: o, offset: i }));
  }
  return e;
}
function Gt(r, e, t) {
  return r.replace(e, function () {
    return t;
  });
}
var P = require('obsidian');
var Ur = {
    en: 'English',
    af: 'Afrikaans',
    'ar-dz': 'Arabic (Algeria)',
    'ar-kw': 'Arabic (Kuwait)',
    'ar-ly': 'Arabic (Libya)',
    'ar-ma': 'Arabic (Morocco)',
    'ar-sa': 'Arabic (Saudi Arabia)',
    'ar-tn': 'Arabic (Tunisia)',
    ar: 'Arabic',
    az: 'Azerbaijani',
    be: 'Belarusian',
    bg: 'Bulgarian',
    bm: 'Bambara',
    bn: 'Bengali',
    bo: 'Tibetan',
    br: 'Breton',
    bs: 'Bosnian',
    ca: 'Catalan',
    cs: 'Czech',
    cv: 'Chuvash',
    cy: 'Welsh',
    da: 'Danish',
    'de-at': 'German (Austria)',
    'de-ch': 'German (Switzerland)',
    de: 'German',
    dv: 'Divehi',
    el: 'Greek',
    'en-au': 'English (Australia)',
    'en-ca': 'English (Canada)',
    'en-gb': 'English (United Kingdom)',
    'en-ie': 'English (Ireland)',
    'en-nz': 'English (New Zealand)',
    eo: 'Esperanto',
    'es-do': 'Spanish (Dominican Republic)',
    'es-us': 'Spanish (United States)',
    es: 'Spanish',
    et: 'Estonian',
    eu: 'Basque',
    fa: 'Persian',
    fi: 'Finnish',
    fo: 'Faroese',
    'fr-ca': 'French (Canada)',
    'fr-ch': 'French (Switzerland)',
    fr: 'French',
    fy: 'Western Frisian',
    gd: 'Scottish Gaelic',
    gl: 'Galician',
    'gom-latn': 'gom (Latin)',
    gu: 'Gujarati',
    he: 'Hebrew',
    hi: 'Hindi',
    hr: 'Croatian',
    hu: 'Hungarian',
    'hy-am': 'Armenian (Armenia)',
    id: 'Indonesian',
    is: 'Icelandic',
    it: 'Italian',
    ja: 'Japanese',
    jv: 'Javanese',
    ka: 'Georgian',
    kk: 'Kazakh',
    km: 'Khmer',
    kn: 'Kannada',
    ko: 'Korean',
    ky: 'Kirghiz',
    lb: 'Luxembourgish',
    lo: 'Lao',
    lt: 'Lithuanian',
    lv: 'Latvian',
    me: 'me',
    mi: 'Maori',
    mk: 'Macedonian',
    ml: 'Malayalam',
    mr: 'Marathi',
    'ms-my': 'Malay (Malaysia)',
    ms: 'Malay',
    mt: 'Maltese',
    my: 'Burmese',
    nb: 'Norwegian Bokm\xE5l',
    ne: 'Nepali',
    'nl-be': 'Dutch (Belgium)',
    nl: 'Dutch',
    nn: 'Norwegian Nynorsk',
    'pa-in': 'Punjabi (India)',
    pl: 'Polish',
    'pt-br': 'Portuguese (Brazil)',
    pt: 'Portuguese',
    ro: 'Romanian',
    ru: 'Russian',
    sd: 'Sindhi',
    se: 'Northern Sami',
    si: 'Sinhala',
    sk: 'Slovak',
    sl: 'Slovenian',
    sq: 'Albanian',
    'sr-cyrl': 'Serbian (Cyrillic)',
    sr: 'Serbian',
    ss: 'Swati',
    sv: 'Swedish',
    sw: 'Swahili',
    ta: 'Tamil',
    te: 'Telugu',
    tet: 'Tetum',
    th: 'Thai',
    'tl-ph': 'Tagalog (Philippines)',
    tlh: 'Klingon',
    tr: 'Turkish',
    tzl: 'tzl',
    'tzm-latn': 'tzm (Latin)',
    tzm: 'tzm',
    uk: 'Ukrainian',
    ur: 'Urdu',
    'uz-latn': 'Uzbek (Latin)',
    uz: 'Uzbek',
    vi: 'Vietnamese',
    'x-pseudo': 'x-pseudo',
    yo: 'Yoruba',
    'zh-cn': 'Chinese (China)',
    'zh-hk': 'Chinese (Hong Kong SAR China)',
    'zh-tw': 'Chinese (Taiwan)',
    'zh-mo': 'Chinese (Macau SAR China)',
  },
  ye = Ur;
var be = require('obsidian'),
  Xe = class extends be.Modal {
    constructor(t, o) {
      var i, n;
      super(t.app);
      this.callback = o;
      ((this.plugin = t),
        (this.currentServiceProvider =
          (n = (i = t.settings) == null ? void 0 : i.serviceProvider) != null
            ? n
            : 'google'));
    }
    get settings() {
      return this.plugin.settings;
    }
    async saveSetting() {
      return this.plugin.saveSettings();
    }
    saveClientId(t) {
      this.currentServiceProvider === 'naver' &&
        (this.plugin.settings.naverClientId = t);
    }
    saveClientSecret(t) {
      this.currentServiceProvider === 'naver' &&
        (this.settings.naverClientSecret = t);
    }
    get currentClientId() {
      return this.currentServiceProvider === 'naver'
        ? this.settings.naverClientId
        : '';
    }
    get currentClientSecret() {
      return this.currentServiceProvider === 'naver'
        ? this.settings.naverClientSecret
        : '';
    }
    onOpen() {
      let { contentEl: t } = this;
      (t.createEl('h2', { text: 'Service Provider Setting' }),
        new be.Setting(t).setName('Client ID').addText(o => {
          o.setValue(this.currentClientId).onChange(i => this.saveClientId(i));
        }),
        new be.Setting(t).setName('Client Secret').addText(o => {
          o.setValue(this.currentClientSecret).onChange(i =>
            this.saveClientSecret(i)
          );
        }),
        new be.Setting(t).addButton(o =>
          o
            .setButtonText('Save')
            .setCta()
            .onClick(async () => {
              var i;
              (await this.plugin.saveSettings(),
                this.close(),
                (i = this.callback) == null || i.call(this));
            })
        ));
    }
    onClose() {
      this.contentEl.empty();
    }
  };
var gr = require('obsidian');
var T = 'top',
  A = 'bottom',
  B = 'right',
  k = 'left',
  ze = 'auto',
  ee = [T, A, B, k],
  q = 'start',
  pe = 'end',
  Yt = 'clippingParents',
  Ye = 'viewport',
  xe = 'popper',
  Kt = 'reference',
  vt = ee.reduce(function (r, e) {
    return r.concat([e + '-' + q, e + '-' + pe]);
  }, []),
  Ke = [].concat(ee, [ze]).reduce(function (r, e) {
    return r.concat([e, e + '-' + q, e + '-' + pe]);
  }, []),
  Wr = 'beforeRead',
  Gr = 'read',
  Xr = 'afterRead',
  zr = 'beforeMain',
  Yr = 'main',
  Kr = 'afterMain',
  qr = 'beforeWrite',
  Qr = 'write',
  Jr = 'afterWrite',
  qt = [Wr, Gr, Xr, zr, Yr, Kr, qr, Qr, Jr];
function N(r) {
  return r ? (r.nodeName || '').toLowerCase() : null;
}
function E(r) {
  if (r == null) return window;
  if (r.toString() !== '[object Window]') {
    var e = r.ownerDocument;
    return (e && e.defaultView) || window;
  }
  return r;
}
function V(r) {
  var e = E(r).Element;
  return r instanceof e || r instanceof Element;
}
function O(r) {
  var e = E(r).HTMLElement;
  return r instanceof e || r instanceof HTMLElement;
}
function Se(r) {
  if (typeof ShadowRoot == 'undefined') return !1;
  var e = E(r).ShadowRoot;
  return r instanceof e || r instanceof ShadowRoot;
}
function Zr(r) {
  var e = r.state;
  Object.keys(e.elements).forEach(function (t) {
    var o = e.styles[t] || {},
      i = e.attributes[t] || {},
      n = e.elements[t];
    !O(n) ||
      !N(n) ||
      (Object.assign(n.style, o),
      Object.keys(i).forEach(function (l) {
        var a = i[l];
        a === !1 ? n.removeAttribute(l) : n.setAttribute(l, a === !0 ? '' : a);
      }));
  });
}
function eo(r) {
  var e = r.state,
    t = {
      popper: {
        position: e.options.strategy,
        left: '0',
        top: '0',
        margin: '0',
      },
      arrow: { position: 'absolute' },
      reference: {},
    };
  return (
    Object.assign(e.elements.popper.style, t.popper),
    (e.styles = t),
    e.elements.arrow && Object.assign(e.elements.arrow.style, t.arrow),
    function () {
      Object.keys(e.elements).forEach(function (o) {
        var i = e.elements[o],
          n = e.attributes[o] || {},
          l = Object.keys(e.styles.hasOwnProperty(o) ? e.styles[o] : t[o]),
          a = l.reduce(function (s, u) {
            return ((s[u] = ''), s);
          }, {});
        !O(i) ||
          !N(i) ||
          (Object.assign(i.style, a),
          Object.keys(n).forEach(function (s) {
            i.removeAttribute(s);
          }));
      });
    }
  );
}
var Qt = {
  name: 'applyStyles',
  enabled: !0,
  phase: 'write',
  fn: Zr,
  effect: eo,
  requires: ['computeStyles'],
};
function L(r) {
  return r.split('-')[0];
}
var X = Math.max,
  ce = Math.min,
  Q = Math.round;
function Ee() {
  var r = navigator.userAgentData;
  return r != null && r.brands && Array.isArray(r.brands)
    ? r.brands
        .map(function (e) {
          return e.brand + '/' + e.version;
        })
        .join(' ')
    : navigator.userAgent;
}
function Ie() {
  return !/^((?!chrome|android).)*safari/i.test(Ee());
}
function _(r, e, t) {
  (e === void 0 && (e = !1), t === void 0 && (t = !1));
  var o = r.getBoundingClientRect(),
    i = 1,
    n = 1;
  e &&
    O(r) &&
    ((i = (r.offsetWidth > 0 && Q(o.width) / r.offsetWidth) || 1),
    (n = (r.offsetHeight > 0 && Q(o.height) / r.offsetHeight) || 1));
  var l = V(r) ? E(r) : window,
    a = l.visualViewport,
    s = !Ie() && t,
    u = (o.left + (s && a ? a.offsetLeft : 0)) / i,
    p = (o.top + (s && a ? a.offsetTop : 0)) / n,
    d = o.width / i,
    v = o.height / n;
  return {
    width: d,
    height: v,
    top: p,
    right: u + d,
    bottom: p + v,
    left: u,
    x: u,
    y: p,
  };
}
function ue(r) {
  var e = _(r),
    t = r.offsetWidth,
    o = r.offsetHeight;
  return (
    Math.abs(e.width - t) <= 1 && (t = e.width),
    Math.abs(e.height - o) <= 1 && (o = e.height),
    { x: r.offsetLeft, y: r.offsetTop, width: t, height: o }
  );
}
function Ae(r, e) {
  var t = e.getRootNode && e.getRootNode();
  if (r.contains(e)) return !0;
  if (t && Se(t)) {
    var o = e;
    do {
      if (o && r.isSameNode(o)) return !0;
      o = o.parentNode || o.host;
    } while (o);
  }
  return !1;
}
function H(r) {
  return E(r).getComputedStyle(r);
}
function wt(r) {
  return ['table', 'td', 'th'].indexOf(N(r)) >= 0;
}
function F(r) {
  return ((V(r) ? r.ownerDocument : r.document) || window.document)
    .documentElement;
}
function J(r) {
  return N(r) === 'html'
    ? r
    : r.assignedSlot || r.parentNode || (Se(r) ? r.host : null) || F(r);
}
function Jt(r) {
  return !O(r) || H(r).position === 'fixed' ? null : r.offsetParent;
}
function to(r) {
  var e = /firefox/i.test(Ee()),
    t = /Trident/i.test(Ee());
  if (t && O(r)) {
    var o = H(r);
    if (o.position === 'fixed') return null;
  }
  var i = J(r);
  for (Se(i) && (i = i.host); O(i) && ['html', 'body'].indexOf(N(i)) < 0; ) {
    var n = H(i);
    if (
      n.transform !== 'none' ||
      n.perspective !== 'none' ||
      n.contain === 'paint' ||
      ['transform', 'perspective'].indexOf(n.willChange) !== -1 ||
      (e && n.willChange === 'filter') ||
      (e && n.filter && n.filter !== 'none')
    )
      return i;
    i = i.parentNode;
  }
  return null;
}
function z(r) {
  for (var e = E(r), t = Jt(r); t && wt(t) && H(t).position === 'static'; )
    t = Jt(t);
  return t &&
    (N(t) === 'html' || (N(t) === 'body' && H(t).position === 'static'))
    ? e
    : t || to(r) || e;
}
function me(r) {
  return ['top', 'bottom'].indexOf(r) >= 0 ? 'x' : 'y';
}
function fe(r, e, t) {
  return X(r, ce(e, t));
}
function Zt(r, e, t) {
  var o = fe(r, e, t);
  return o > t ? t : o;
}
function Oe() {
  return { top: 0, right: 0, bottom: 0, left: 0 };
}
function De(r) {
  return Object.assign({}, Oe(), r);
}
function Ne(r, e) {
  return e.reduce(function (t, o) {
    return ((t[o] = r), t);
  }, {});
}
var ro = function (e, t) {
  return (
    (e =
      typeof e == 'function'
        ? e(Object.assign({}, t.rects, { placement: t.placement }))
        : e),
    De(typeof e != 'number' ? e : Ne(e, ee))
  );
};
function oo(r) {
  var e,
    t = r.state,
    o = r.name,
    i = r.options,
    n = t.elements.arrow,
    l = t.modifiersData.popperOffsets,
    a = L(t.placement),
    s = me(a),
    u = [k, B].indexOf(a) >= 0,
    p = u ? 'height' : 'width';
  if (!(!n || !l)) {
    var d = ro(i.padding, t),
      v = ue(n),
      f = s === 'y' ? T : k,
      S = s === 'y' ? A : B,
      c =
        t.rects.reference[p] + t.rects.reference[s] - l[s] - t.rects.popper[p],
      m = l[s] - t.rects.reference[s],
      h = z(n),
      w = h ? (s === 'y' ? h.clientHeight || 0 : h.clientWidth || 0) : 0,
      y = c / 2 - m / 2,
      g = d[f],
      b = w - v[p] - d[S],
      x = w / 2 - v[p] / 2 + y,
      C = fe(g, x, b),
      R = s;
    t.modifiersData[o] = ((e = {}), (e[R] = C), (e.centerOffset = C - x), e);
  }
}
function io(r) {
  var e = r.state,
    t = r.options,
    o = t.element,
    i = o === void 0 ? '[data-popper-arrow]' : o;
  i != null &&
    ((typeof i == 'string' && ((i = e.elements.popper.querySelector(i)), !i)) ||
      (Ae(e.elements.popper, i) && (e.elements.arrow = i)));
}
var er = {
  name: 'arrow',
  enabled: !0,
  phase: 'main',
  fn: oo,
  effect: io,
  requires: ['popperOffsets'],
  requiresIfExists: ['preventOverflow'],
};
function U(r) {
  return r.split('-')[1];
}
var no = { top: 'auto', right: 'auto', bottom: 'auto', left: 'auto' };
function ao(r, e) {
  var t = r.x,
    o = r.y,
    i = e.devicePixelRatio || 1;
  return { x: Q(t * i) / i || 0, y: Q(o * i) / i || 0 };
}
function tr(r) {
  var e,
    t = r.popper,
    o = r.popperRect,
    i = r.placement,
    n = r.variation,
    l = r.offsets,
    a = r.position,
    s = r.gpuAcceleration,
    u = r.adaptive,
    p = r.roundOffsets,
    d = r.isFixed,
    v = l.x,
    f = v === void 0 ? 0 : v,
    S = l.y,
    c = S === void 0 ? 0 : S,
    m = typeof p == 'function' ? p({ x: f, y: c }) : { x: f, y: c };
  ((f = m.x), (c = m.y));
  var h = l.hasOwnProperty('x'),
    w = l.hasOwnProperty('y'),
    y = k,
    g = T,
    b = window;
  if (u) {
    var x = z(t),
      C = 'clientHeight',
      R = 'clientWidth';
    if (
      (x === E(t) &&
        ((x = F(t)),
        H(x).position !== 'static' &&
          a === 'absolute' &&
          ((C = 'scrollHeight'), (R = 'scrollWidth'))),
      (x = x),
      i === T || ((i === k || i === B) && n === pe))
    ) {
      g = A;
      var D = d && x === b && b.visualViewport ? b.visualViewport.height : x[C];
      ((c -= D - o.height), (c *= s ? 1 : -1));
    }
    if (i === k || ((i === T || i === A) && n === pe)) {
      y = B;
      var I = d && x === b && b.visualViewport ? b.visualViewport.width : x[R];
      ((f -= I - o.width), (f *= s ? 1 : -1));
    }
  }
  var M = Object.assign({ position: a }, u && no),
    W = p === !0 ? ao({ x: f, y: c }, E(t)) : { x: f, y: c };
  if (((f = W.x), (c = W.y), s)) {
    var $;
    return Object.assign(
      {},
      M,
      (($ = {}),
      ($[g] = w ? '0' : ''),
      ($[y] = h ? '0' : ''),
      ($.transform =
        (b.devicePixelRatio || 1) <= 1
          ? 'translate(' + f + 'px, ' + c + 'px)'
          : 'translate3d(' + f + 'px, ' + c + 'px, 0)'),
      $)
    );
  }
  return Object.assign(
    {},
    M,
    ((e = {}),
    (e[g] = w ? c + 'px' : ''),
    (e[y] = h ? f + 'px' : ''),
    (e.transform = ''),
    e)
  );
}
function so(r) {
  var e = r.state,
    t = r.options,
    o = t.gpuAcceleration,
    i = o === void 0 ? !0 : o,
    n = t.adaptive,
    l = n === void 0 ? !0 : n,
    a = t.roundOffsets,
    s = a === void 0 ? !0 : a,
    u = {
      placement: L(e.placement),
      variation: U(e.placement),
      popper: e.elements.popper,
      popperRect: e.rects.popper,
      gpuAcceleration: i,
      isFixed: e.options.strategy === 'fixed',
    };
  (e.modifiersData.popperOffsets != null &&
    (e.styles.popper = Object.assign(
      {},
      e.styles.popper,
      tr(
        Object.assign({}, u, {
          offsets: e.modifiersData.popperOffsets,
          position: e.options.strategy,
          adaptive: l,
          roundOffsets: s,
        })
      )
    )),
    e.modifiersData.arrow != null &&
      (e.styles.arrow = Object.assign(
        {},
        e.styles.arrow,
        tr(
          Object.assign({}, u, {
            offsets: e.modifiersData.arrow,
            position: 'absolute',
            adaptive: !1,
            roundOffsets: s,
          })
        )
      )),
    (e.attributes.popper = Object.assign({}, e.attributes.popper, {
      'data-popper-placement': e.placement,
    })));
}
var rr = {
  name: 'computeStyles',
  enabled: !0,
  phase: 'beforeWrite',
  fn: so,
  data: {},
};
var qe = { passive: !0 };
function lo(r) {
  var e = r.state,
    t = r.instance,
    o = r.options,
    i = o.scroll,
    n = i === void 0 ? !0 : i,
    l = o.resize,
    a = l === void 0 ? !0 : l,
    s = E(e.elements.popper),
    u = [].concat(e.scrollParents.reference, e.scrollParents.popper);
  return (
    n &&
      u.forEach(function (p) {
        p.addEventListener('scroll', t.update, qe);
      }),
    a && s.addEventListener('resize', t.update, qe),
    function () {
      (n &&
        u.forEach(function (p) {
          p.removeEventListener('scroll', t.update, qe);
        }),
        a && s.removeEventListener('resize', t.update, qe));
    }
  );
}
var or = {
  name: 'eventListeners',
  enabled: !0,
  phase: 'write',
  fn: function () {},
  effect: lo,
  data: {},
};
var po = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };
function Ce(r) {
  return r.replace(/left|right|bottom|top/g, function (e) {
    return po[e];
  });
}
var co = { start: 'end', end: 'start' };
function Qe(r) {
  return r.replace(/start|end/g, function (e) {
    return co[e];
  });
}
function de(r) {
  var e = E(r),
    t = e.pageXOffset,
    o = e.pageYOffset;
  return { scrollLeft: t, scrollTop: o };
}
function ge(r) {
  return _(F(r)).left + de(r).scrollLeft;
}
function yt(r, e) {
  var t = E(r),
    o = F(r),
    i = t.visualViewport,
    n = o.clientWidth,
    l = o.clientHeight,
    a = 0,
    s = 0;
  if (i) {
    ((n = i.width), (l = i.height));
    var u = Ie();
    (u || (!u && e === 'fixed')) && ((a = i.offsetLeft), (s = i.offsetTop));
  }
  return { width: n, height: l, x: a + ge(r), y: s };
}
function bt(r) {
  var e,
    t = F(r),
    o = de(r),
    i = (e = r.ownerDocument) == null ? void 0 : e.body,
    n = X(
      t.scrollWidth,
      t.clientWidth,
      i ? i.scrollWidth : 0,
      i ? i.clientWidth : 0
    ),
    l = X(
      t.scrollHeight,
      t.clientHeight,
      i ? i.scrollHeight : 0,
      i ? i.clientHeight : 0
    ),
    a = -o.scrollLeft + ge(r),
    s = -o.scrollTop;
  return (
    H(i || t).direction === 'rtl' &&
      (a += X(t.clientWidth, i ? i.clientWidth : 0) - n),
    { width: n, height: l, x: a, y: s }
  );
}
function he(r) {
  var e = H(r),
    t = e.overflow,
    o = e.overflowX,
    i = e.overflowY;
  return /auto|scroll|overlay|hidden/.test(t + i + o);
}
function Je(r) {
  return ['html', 'body', '#document'].indexOf(N(r)) >= 0
    ? r.ownerDocument.body
    : O(r) && he(r)
      ? r
      : Je(J(r));
}
function te(r, e) {
  var t;
  e === void 0 && (e = []);
  var o = Je(r),
    i = o === ((t = r.ownerDocument) == null ? void 0 : t.body),
    n = E(o),
    l = i ? [n].concat(n.visualViewport || [], he(o) ? o : []) : o,
    a = e.concat(l);
  return i ? a : a.concat(te(J(l)));
}
function Te(r) {
  return Object.assign({}, r, {
    left: r.x,
    top: r.y,
    right: r.x + r.width,
    bottom: r.y + r.height,
  });
}
function uo(r, e) {
  var t = _(r, !1, e === 'fixed');
  return (
    (t.top = t.top + r.clientTop),
    (t.left = t.left + r.clientLeft),
    (t.bottom = t.top + r.clientHeight),
    (t.right = t.left + r.clientWidth),
    (t.width = r.clientWidth),
    (t.height = r.clientHeight),
    (t.x = t.left),
    (t.y = t.top),
    t
  );
}
function ir(r, e, t) {
  return e === Ye ? Te(yt(r, t)) : V(e) ? uo(e, t) : Te(bt(F(r)));
}
function mo(r) {
  var e = te(J(r)),
    t = ['absolute', 'fixed'].indexOf(H(r).position) >= 0,
    o = t && O(r) ? z(r) : r;
  return V(o)
    ? e.filter(function (i) {
        return V(i) && Ae(i, o) && N(i) !== 'body';
      })
    : [];
}
function xt(r, e, t, o) {
  var i = e === 'clippingParents' ? mo(r) : [].concat(e),
    n = [].concat(i, [t]),
    l = n[0],
    a = n.reduce(
      function (s, u) {
        var p = ir(r, u, o);
        return (
          (s.top = X(p.top, s.top)),
          (s.right = ce(p.right, s.right)),
          (s.bottom = ce(p.bottom, s.bottom)),
          (s.left = X(p.left, s.left)),
          s
        );
      },
      ir(r, l, o)
    );
  return (
    (a.width = a.right - a.left),
    (a.height = a.bottom - a.top),
    (a.x = a.left),
    (a.y = a.top),
    a
  );
}
function Le(r) {
  var e = r.reference,
    t = r.element,
    o = r.placement,
    i = o ? L(o) : null,
    n = o ? U(o) : null,
    l = e.x + e.width / 2 - t.width / 2,
    a = e.y + e.height / 2 - t.height / 2,
    s;
  switch (i) {
    case T:
      s = { x: l, y: e.y - t.height };
      break;
    case A:
      s = { x: l, y: e.y + e.height };
      break;
    case B:
      s = { x: e.x + e.width, y: a };
      break;
    case k:
      s = { x: e.x - t.width, y: a };
      break;
    default:
      s = { x: e.x, y: e.y };
  }
  var u = i ? me(i) : null;
  if (u != null) {
    var p = u === 'y' ? 'height' : 'width';
    switch (n) {
      case q:
        s[u] = s[u] - (e[p] / 2 - t[p] / 2);
        break;
      case pe:
        s[u] = s[u] + (e[p] / 2 - t[p] / 2);
        break;
      default:
    }
  }
  return s;
}
function Y(r, e) {
  e === void 0 && (e = {});
  var t = e,
    o = t.placement,
    i = o === void 0 ? r.placement : o,
    n = t.strategy,
    l = n === void 0 ? r.strategy : n,
    a = t.boundary,
    s = a === void 0 ? Yt : a,
    u = t.rootBoundary,
    p = u === void 0 ? Ye : u,
    d = t.elementContext,
    v = d === void 0 ? xe : d,
    f = t.altBoundary,
    S = f === void 0 ? !1 : f,
    c = t.padding,
    m = c === void 0 ? 0 : c,
    h = De(typeof m != 'number' ? m : Ne(m, ee)),
    w = v === xe ? Kt : xe,
    y = r.rects.popper,
    g = r.elements[S ? w : v],
    b = xt(V(g) ? g : g.contextElement || F(r.elements.popper), s, p, l),
    x = _(r.elements.reference),
    C = Le({ reference: x, element: y, strategy: 'absolute', placement: i }),
    R = Te(Object.assign({}, y, C)),
    D = v === xe ? R : x,
    I = {
      top: b.top - D.top + h.top,
      bottom: D.bottom - b.bottom + h.bottom,
      left: b.left - D.left + h.left,
      right: D.right - b.right + h.right,
    },
    M = r.modifiersData.offset;
  if (v === xe && M) {
    var W = M[i];
    Object.keys(I).forEach(function ($) {
      var ie = [B, A].indexOf($) >= 0 ? 1 : -1,
        ne = [T, A].indexOf($) >= 0 ? 'y' : 'x';
      I[$] += W[ne] * ie;
    });
  }
  return I;
}
function St(r, e) {
  e === void 0 && (e = {});
  var t = e,
    o = t.placement,
    i = t.boundary,
    n = t.rootBoundary,
    l = t.padding,
    a = t.flipVariations,
    s = t.allowedAutoPlacements,
    u = s === void 0 ? Ke : s,
    p = U(o),
    d = p
      ? a
        ? vt
        : vt.filter(function (S) {
            return U(S) === p;
          })
      : ee,
    v = d.filter(function (S) {
      return u.indexOf(S) >= 0;
    });
  v.length === 0 && (v = d);
  var f = v.reduce(function (S, c) {
    return (
      (S[c] = Y(r, { placement: c, boundary: i, rootBoundary: n, padding: l })[
        L(c)
      ]),
      S
    );
  }, {});
  return Object.keys(f).sort(function (S, c) {
    return f[S] - f[c];
  });
}
function fo(r) {
  if (L(r) === ze) return [];
  var e = Ce(r);
  return [Qe(r), e, Qe(e)];
}
function go(r) {
  var e = r.state,
    t = r.options,
    o = r.name;
  if (!e.modifiersData[o]._skip) {
    for (
      var i = t.mainAxis,
        n = i === void 0 ? !0 : i,
        l = t.altAxis,
        a = l === void 0 ? !0 : l,
        s = t.fallbackPlacements,
        u = t.padding,
        p = t.boundary,
        d = t.rootBoundary,
        v = t.altBoundary,
        f = t.flipVariations,
        S = f === void 0 ? !0 : f,
        c = t.allowedAutoPlacements,
        m = e.options.placement,
        h = L(m),
        w = h === m,
        y = s || (w || !S ? [Ce(m)] : fo(m)),
        g = [m].concat(y).reduce(function (ve, Z) {
          return ve.concat(
            L(Z) === ze
              ? St(e, {
                  placement: Z,
                  boundary: p,
                  rootBoundary: d,
                  padding: u,
                  flipVariations: S,
                  allowedAutoPlacements: c,
                })
              : Z
          );
        }, []),
        b = e.rects.reference,
        x = e.rects.popper,
        C = new Map(),
        R = !0,
        D = g[0],
        I = 0;
      I < g.length;
      I++
    ) {
      var M = g[I],
        W = L(M),
        $ = U(M) === q,
        ie = [T, A].indexOf(W) >= 0,
        ne = ie ? 'width' : 'height',
        j = Y(e, {
          placement: M,
          boundary: p,
          rootBoundary: d,
          altBoundary: v,
          padding: u,
        }),
        G = ie ? ($ ? B : k) : $ ? A : T;
      b[ne] > x[ne] && (G = Ce(G));
      var Me = Ce(G),
        ae = [];
      if (
        (n && ae.push(j[W] <= 0),
        a && ae.push(j[G] <= 0, j[Me] <= 0),
        ae.every(function (ve) {
          return ve;
        }))
      ) {
        ((D = M), (R = !1));
        break;
      }
      C.set(M, ae);
    }
    if (R)
      for (
        var $e = S ? 3 : 1,
          st = function (Z) {
            var ke = g.find(function (je) {
              var se = C.get(je);
              if (se)
                return se.slice(0, Z).every(function (lt) {
                  return lt;
                });
            });
            if (ke) return ((D = ke), 'break');
          },
          Pe = $e;
        Pe > 0;
        Pe--
      ) {
        var He = st(Pe);
        if (He === 'break') break;
      }
    e.placement !== D &&
      ((e.modifiersData[o]._skip = !0), (e.placement = D), (e.reset = !0));
  }
}
var nr = {
  name: 'flip',
  enabled: !0,
  phase: 'main',
  fn: go,
  requiresIfExists: ['offset'],
  data: { _skip: !1 },
};
function ar(r, e, t) {
  return (
    t === void 0 && (t = { x: 0, y: 0 }),
    {
      top: r.top - e.height - t.y,
      right: r.right - e.width + t.x,
      bottom: r.bottom - e.height + t.y,
      left: r.left - e.width - t.x,
    }
  );
}
function sr(r) {
  return [T, B, A, k].some(function (e) {
    return r[e] >= 0;
  });
}
function ho(r) {
  var e = r.state,
    t = r.name,
    o = e.rects.reference,
    i = e.rects.popper,
    n = e.modifiersData.preventOverflow,
    l = Y(e, { elementContext: 'reference' }),
    a = Y(e, { altBoundary: !0 }),
    s = ar(l, o),
    u = ar(a, i, n),
    p = sr(s),
    d = sr(u);
  ((e.modifiersData[t] = {
    referenceClippingOffsets: s,
    popperEscapeOffsets: u,
    isReferenceHidden: p,
    hasPopperEscaped: d,
  }),
    (e.attributes.popper = Object.assign({}, e.attributes.popper, {
      'data-popper-reference-hidden': p,
      'data-popper-escaped': d,
    })));
}
var lr = {
  name: 'hide',
  enabled: !0,
  phase: 'main',
  requiresIfExists: ['preventOverflow'],
  fn: ho,
};
function vo(r, e, t) {
  var o = L(r),
    i = [k, T].indexOf(o) >= 0 ? -1 : 1,
    n = typeof t == 'function' ? t(Object.assign({}, e, { placement: r })) : t,
    l = n[0],
    a = n[1];
  return (
    (l = l || 0),
    (a = (a || 0) * i),
    [k, B].indexOf(o) >= 0 ? { x: a, y: l } : { x: l, y: a }
  );
}
function wo(r) {
  var e = r.state,
    t = r.options,
    o = r.name,
    i = t.offset,
    n = i === void 0 ? [0, 0] : i,
    l = Ke.reduce(function (p, d) {
      return ((p[d] = vo(d, e.rects, n)), p);
    }, {}),
    a = l[e.placement],
    s = a.x,
    u = a.y;
  (e.modifiersData.popperOffsets != null &&
    ((e.modifiersData.popperOffsets.x += s),
    (e.modifiersData.popperOffsets.y += u)),
    (e.modifiersData[o] = l));
}
var pr = {
  name: 'offset',
  enabled: !0,
  phase: 'main',
  requires: ['popperOffsets'],
  fn: wo,
};
function yo(r) {
  var e = r.state,
    t = r.name;
  e.modifiersData[t] = Le({
    reference: e.rects.reference,
    element: e.rects.popper,
    strategy: 'absolute',
    placement: e.placement,
  });
}
var cr = {
  name: 'popperOffsets',
  enabled: !0,
  phase: 'read',
  fn: yo,
  data: {},
};
function Et(r) {
  return r === 'x' ? 'y' : 'x';
}
function bo(r) {
  var e = r.state,
    t = r.options,
    o = r.name,
    i = t.mainAxis,
    n = i === void 0 ? !0 : i,
    l = t.altAxis,
    a = l === void 0 ? !1 : l,
    s = t.boundary,
    u = t.rootBoundary,
    p = t.altBoundary,
    d = t.padding,
    v = t.tether,
    f = v === void 0 ? !0 : v,
    S = t.tetherOffset,
    c = S === void 0 ? 0 : S,
    m = Y(e, { boundary: s, rootBoundary: u, padding: d, altBoundary: p }),
    h = L(e.placement),
    w = U(e.placement),
    y = !w,
    g = me(h),
    b = Et(g),
    x = e.modifiersData.popperOffsets,
    C = e.rects.reference,
    R = e.rects.popper,
    D =
      typeof c == 'function'
        ? c(Object.assign({}, e.rects, { placement: e.placement }))
        : c,
    I =
      typeof D == 'number'
        ? { mainAxis: D, altAxis: D }
        : Object.assign({ mainAxis: 0, altAxis: 0 }, D),
    M = e.modifiersData.offset ? e.modifiersData.offset[e.placement] : null,
    W = { x: 0, y: 0 };
  if (x) {
    if (n) {
      var $,
        ie = g === 'y' ? T : k,
        ne = g === 'y' ? A : B,
        j = g === 'y' ? 'height' : 'width',
        G = x[g],
        Me = G + m[ie],
        ae = G - m[ne],
        $e = f ? -R[j] / 2 : 0,
        st = w === q ? C[j] : R[j],
        Pe = w === q ? -R[j] : -C[j],
        He = e.elements.arrow,
        ve = f && He ? ue(He) : { width: 0, height: 0 },
        Z = e.modifiersData['arrow#persistent']
          ? e.modifiersData['arrow#persistent'].padding
          : Oe(),
        ke = Z[ie],
        je = Z[ne],
        se = fe(0, C[j], ve[j]),
        lt = y
          ? C[j] / 2 - $e - se - ke - I.mainAxis
          : st - se - ke - I.mainAxis,
        Tr = y
          ? -C[j] / 2 + $e + se + je + I.mainAxis
          : Pe + se + je + I.mainAxis,
        pt = e.elements.arrow && z(e.elements.arrow),
        Pr = pt ? (g === 'y' ? pt.clientTop || 0 : pt.clientLeft || 0) : 0,
        Dt = ($ = M == null ? void 0 : M[g]) != null ? $ : 0,
        kr = G + lt - Dt - Pr,
        Br = G + Tr - Dt,
        Nt = fe(f ? ce(Me, kr) : Me, G, f ? X(ae, Br) : ae);
      ((x[g] = Nt), (W[g] = Nt - G));
    }
    if (a) {
      var Lt,
        Ir = g === 'x' ? T : k,
        Ar = g === 'x' ? A : B,
        le = x[b],
        Ve = b === 'y' ? 'height' : 'width',
        Rt = le + m[Ir],
        Ft = le - m[Ar],
        ct = [T, k].indexOf(h) !== -1,
        Mt = (Lt = M == null ? void 0 : M[b]) != null ? Lt : 0,
        $t = ct ? Rt : le - C[Ve] - R[Ve] - Mt + I.altAxis,
        Ht = ct ? le + C[Ve] + R[Ve] - Mt - I.altAxis : Ft,
        jt = f && ct ? Zt($t, le, Ht) : fe(f ? $t : Rt, le, f ? Ht : Ft);
      ((x[b] = jt), (W[b] = jt - le));
    }
    e.modifiersData[o] = W;
  }
}
var ur = {
  name: 'preventOverflow',
  enabled: !0,
  phase: 'main',
  fn: bo,
  requiresIfExists: ['offset'],
};
function Ct(r) {
  return { scrollLeft: r.scrollLeft, scrollTop: r.scrollTop };
}
function Tt(r) {
  return r === E(r) || !O(r) ? de(r) : Ct(r);
}
function xo(r) {
  var e = r.getBoundingClientRect(),
    t = Q(e.width) / r.offsetWidth || 1,
    o = Q(e.height) / r.offsetHeight || 1;
  return t !== 1 || o !== 1;
}
function Pt(r, e, t) {
  t === void 0 && (t = !1);
  var o = O(e),
    i = O(e) && xo(e),
    n = F(e),
    l = _(r, i, t),
    a = { scrollLeft: 0, scrollTop: 0 },
    s = { x: 0, y: 0 };
  return (
    (o || (!o && !t)) &&
      ((N(e) !== 'body' || he(n)) && (a = Tt(e)),
      O(e)
        ? ((s = _(e, !0)), (s.x += e.clientLeft), (s.y += e.clientTop))
        : n && (s.x = ge(n))),
    {
      x: l.left + a.scrollLeft - s.x,
      y: l.top + a.scrollTop - s.y,
      width: l.width,
      height: l.height,
    }
  );
}
function So(r) {
  var e = new Map(),
    t = new Set(),
    o = [];
  r.forEach(function (n) {
    e.set(n.name, n);
  });
  function i(n) {
    t.add(n.name);
    var l = [].concat(n.requires || [], n.requiresIfExists || []);
    (l.forEach(function (a) {
      if (!t.has(a)) {
        var s = e.get(a);
        s && i(s);
      }
    }),
      o.push(n));
  }
  return (
    r.forEach(function (n) {
      t.has(n.name) || i(n);
    }),
    o
  );
}
function kt(r) {
  var e = So(r);
  return qt.reduce(function (t, o) {
    return t.concat(
      e.filter(function (i) {
        return i.phase === o;
      })
    );
  }, []);
}
function Bt(r) {
  var e;
  return function () {
    return (
      e ||
        (e = new Promise(function (t) {
          Promise.resolve().then(function () {
            ((e = void 0), t(r()));
          });
        })),
      e
    );
  };
}
function It(r) {
  var e = r.reduce(function (t, o) {
    var i = t[o.name];
    return (
      (t[o.name] = i
        ? Object.assign({}, i, o, {
            options: Object.assign({}, i.options, o.options),
            data: Object.assign({}, i.data, o.data),
          })
        : o),
      t
    );
  }, {});
  return Object.keys(e).map(function (t) {
    return e[t];
  });
}
var mr = { placement: 'bottom', modifiers: [], strategy: 'absolute' };
function fr() {
  for (var r = arguments.length, e = new Array(r), t = 0; t < r; t++)
    e[t] = arguments[t];
  return !e.some(function (o) {
    return !(o && typeof o.getBoundingClientRect == 'function');
  });
}
function dr(r) {
  r === void 0 && (r = {});
  var e = r,
    t = e.defaultModifiers,
    o = t === void 0 ? [] : t,
    i = e.defaultOptions,
    n = i === void 0 ? mr : i;
  return function (a, s, u) {
    u === void 0 && (u = n);
    var p = {
        placement: 'bottom',
        orderedModifiers: [],
        options: Object.assign({}, mr, n),
        modifiersData: {},
        elements: { reference: a, popper: s },
        attributes: {},
        styles: {},
      },
      d = [],
      v = !1,
      f = {
        state: p,
        setOptions: function (h) {
          var w = typeof h == 'function' ? h(p.options) : h;
          (c(),
            (p.options = Object.assign({}, n, p.options, w)),
            (p.scrollParents = {
              reference: V(a)
                ? te(a)
                : a.contextElement
                  ? te(a.contextElement)
                  : [],
              popper: te(s),
            }));
          var y = kt(It([].concat(o, p.options.modifiers)));
          return (
            (p.orderedModifiers = y.filter(function (g) {
              return g.enabled;
            })),
            S(),
            f.update()
          );
        },
        forceUpdate: function () {
          if (!v) {
            var h = p.elements,
              w = h.reference,
              y = h.popper;
            if (fr(w, y)) {
              ((p.rects = {
                reference: Pt(w, z(y), p.options.strategy === 'fixed'),
                popper: ue(y),
              }),
                (p.reset = !1),
                (p.placement = p.options.placement),
                p.orderedModifiers.forEach(function (I) {
                  return (p.modifiersData[I.name] = Object.assign({}, I.data));
                }));
              for (var g = 0; g < p.orderedModifiers.length; g++) {
                if (p.reset === !0) {
                  ((p.reset = !1), (g = -1));
                  continue;
                }
                var b = p.orderedModifiers[g],
                  x = b.fn,
                  C = b.options,
                  R = C === void 0 ? {} : C,
                  D = b.name;
                typeof x == 'function' &&
                  (p = x({ state: p, options: R, name: D, instance: f }) || p);
              }
            }
          }
        },
        update: Bt(function () {
          return new Promise(function (m) {
            (f.forceUpdate(), m(p));
          });
        }),
        destroy: function () {
          (c(), (v = !0));
        },
      };
    if (!fr(a, s)) return f;
    f.setOptions(u).then(function (m) {
      !v && u.onFirstUpdate && u.onFirstUpdate(m);
    });
    function S() {
      p.orderedModifiers.forEach(function (m) {
        var h = m.name,
          w = m.options,
          y = w === void 0 ? {} : w,
          g = m.effect;
        if (typeof g == 'function') {
          var b = g({ state: p, name: h, instance: f, options: y }),
            x = function () {};
          d.push(b || x);
        }
      });
    }
    function c() {
      (d.forEach(function (m) {
        return m();
      }),
        (d = []));
    }
    return f;
  };
}
var Eo = [or, cr, rr, Qt, pr, nr, ur, er, lr],
  At = dr({ defaultModifiers: Eo });
var Co = (r, e) => ((r % e) + e) % e,
  Ot = class {
    constructor(e, t, o) {
      ((this.owner = e),
        (this.containerEl = t),
        t.on('click', '.suggestion-item', this.onSuggestionClick.bind(this)),
        t.on(
          'mousemove',
          '.suggestion-item',
          this.onSuggestionMouseover.bind(this)
        ),
        o.register([], 'ArrowUp', i => {
          if (!i.isComposing)
            return (this.setSelectedItem(this.selectedItem - 1, !0), !1);
        }),
        o.register([], 'ArrowDown', i => {
          if (!i.isComposing)
            return (this.setSelectedItem(this.selectedItem + 1, !0), !1);
        }),
        o.register([], 'Enter', i => {
          if (!i.isComposing) return (this.useSelectedItem(i), !1);
        }));
    }
    onSuggestionClick(e, t) {
      e.preventDefault();
      let o = this.suggestions.indexOf(t);
      (this.setSelectedItem(o, !1), this.useSelectedItem(e));
    }
    onSuggestionMouseover(e, t) {
      let o = this.suggestions.indexOf(t);
      this.setSelectedItem(o, !1);
    }
    setSuggestions(e) {
      this.containerEl.empty();
      let t = [];
      (e.forEach(o => {
        let i = this.containerEl.createDiv('suggestion-item');
        (this.owner.renderSuggestion(o, i), t.push(i));
      }),
        (this.values = e),
        (this.suggestions = t),
        this.setSelectedItem(0, !1));
    }
    useSelectedItem(e) {
      let t = this.values[this.selectedItem];
      t && this.owner.selectSuggestion(t, e);
    }
    setSelectedItem(e, t) {
      let o = Co(e, this.suggestions.length),
        i = this.suggestions[this.selectedItem],
        n = this.suggestions[o];
      (i == null || i.removeClass('is-selected'),
        n == null || n.addClass('is-selected'),
        (this.selectedItem = o),
        t && n.scrollIntoView(!1));
    }
  },
  re = class {
    constructor(e, t) {
      this.app = e;
      this.inputEl = t;
      ((this.scope = new gr.Scope()),
        (this.suggestEl = createDiv('suggestion-container')));
      let o = this.suggestEl.createDiv('suggestion');
      ((this.suggest = new Ot(this, o, this.scope)),
        this.scope.register([], 'Escape', this.close.bind(this)),
        this.inputEl.addEventListener('input', this.onInputChanged.bind(this)),
        this.inputEl.addEventListener('focus', this.onInputChanged.bind(this)),
        this.inputEl.addEventListener('blur', this.close.bind(this)),
        this.suggestEl.on('mousedown', '.suggestion-container', i => {
          i.preventDefault();
        }));
    }
    onInputChanged() {
      let e = this.inputEl.value,
        t = this.getSuggestions(e);
      if (!t) {
        this.close();
        return;
      }
      t.length > 0
        ? (this.suggest.setSuggestions(t),
          this.open(this.app.dom.appContainerEl, this.inputEl))
        : this.close();
    }
    open(e, t) {
      (this.app.keymap.pushScope(this.scope),
        e.appendChild(this.suggestEl),
        (this.popper = At(t, this.suggestEl, {
          placement: 'bottom-start',
          modifiers: [
            {
              name: 'sameWidth',
              enabled: !0,
              fn: ({ state: o, instance: i }) => {
                let n = `${o.rects.reference.width}px`;
                o.styles.popper.width !== n &&
                  ((o.styles.popper.width = n), i.update());
              },
              phase: 'beforeWrite',
              requires: ['computeStyles'],
            },
          ],
        })));
    }
    close() {
      (this.app.keymap.popScope(this.scope),
        this.suggest.setSuggestions([]),
        this.popper && this.popper.destroy(),
        this.suggestEl.detach());
    }
  };
var To = '{{DATE}}',
  hr = '{{DATE:}}',
  Po = /{{D?A?T?E?}?}?$/i,
  ko = /{{D?A?T?E?:?$|{{DATE:[^\n\r}]*}}$/i,
  Bo = '{{author}}',
  Io = /{{a?u?t?h?o?r?}?}?$/i,
  Ao = '{{title}}',
  Oo = /{{t?i?t?l?e?}?}?$/i,
  Ze = class extends re {
    constructor(t, o) {
      super(t, o);
      this.app = t;
      this.inputEl = o;
      this.lastInput = '';
    }
    getSuggestions(t) {
      let o = this.inputEl.selectionStart,
        i = 15,
        n = t.substr(o - i, i),
        l = [];
      return (
        this.processToken(n, (a, s) => {
          ((this.lastInput = a[0]), l.push(s));
        }),
        l
      );
    }
    selectSuggestion(t) {
      let o = this.inputEl.selectionStart,
        i = this.lastInput.length,
        n = this.inputEl.value,
        l = 0,
        a = (s, u = 0) => `${n.substr(0, o - i + u)}${s}${n.substr(o)}`;
      (this.processToken(t, (s, u) => {
        t.contains(u) &&
          ((this.inputEl.value = a(t)),
          (l = o - i + t.length),
          t === hr && (l -= 2));
      }),
        this.inputEl.trigger('input'),
        this.close(),
        this.inputEl.setSelectionRange(l, l));
    }
    renderSuggestion(t, o) {
      t && o.setText(t);
    }
    processToken(t, o) {
      let i = ko.exec(t);
      i && o(i, hr);
      let n = Po.exec(t);
      n && o(n, To);
      let l = Io.exec(t);
      l && o(l, Bo);
      let a = Oo.exec(t);
      a && o(a, Ao);
    }
  };
var vr = require('obsidian');
var et = class extends re {
  getSuggestions(e) {
    let t = this.app.vault.getAllLoadedFiles(),
      o = [],
      i = e.toLowerCase();
    return (
      t.forEach(n => {
        n instanceof vr.TFile &&
          n.extension === 'md' &&
          n.path.toLowerCase().contains(i) &&
          o.push(n);
      }),
      o
    );
  }
  renderSuggestion(e, t) {
    t.setText(e.path);
  }
  selectSuggestion(e) {
    ((this.inputEl.value = e.path),
      this.inputEl.trigger('input'),
      this.close());
  }
};
var wr = require('obsidian');
var Re = class extends re {
  getSuggestions(e) {
    let t = this.app.vault.getAllLoadedFiles(),
      o = [],
      i = e.toLowerCase();
    return (
      t.forEach(n => {
        n instanceof wr.TFolder &&
          n.path.toLowerCase().contains(i) &&
          o.push(n);
      }),
      o
    );
  }
  renderSuggestion(e, t) {
    t.setText(e.path);
  }
  selectSuggestion(e) {
    ((this.inputEl.value = e.path),
      this.inputEl.trigger('input'),
      this.close());
  }
};
var Do = 'https://github.com/anpigon/obsidian-book-search-plugin';
var Fe = {
    folder: '',
    fileNameFormat: '',
    frontmatter: '',
    content: '',
    useDefaultFrontmatter: !0,
    defaultFrontmatterKeyType: 'Camel Case',
    templateFile: '',
    serviceProvider: 'google',
    naverClientId: '',
    naverClientSecret: '',
    localePreference: 'default',
    apiKey: '',
    openPageOnCompletion: !0,
    showCoverImageInSearch: !1,
    enableCoverImageSave: !1,
    enableCoverImageEdgeCurl: !0,
    coverImagePath: '',
    askForLocale: !0,
  },
  tt = class extends P.PluginSettingTab {
    constructor(t, o) {
      super(t, o);
      this.plugin = o;
    }
    createGeneralSettings(t) {
      (this.createHeader('General Settings', t),
        this.createFileLocationSetting(t),
        this.createFileNameFormatSetting(t));
    }
    createHeader(t, o) {
      let i = document.createDocumentFragment();
      return (
        i.createEl('h2', { text: t }),
        new P.Setting(o).setHeading().setName(i)
      );
    }
    createFileLocationSetting(t) {
      new P.Setting(t)
        .setName('New file location')
        .setDesc('New book notes will be placed here.')
        .addSearch(o => {
          try {
            new Re(this.app, o.inputEl);
          } catch (i) {
            console.error(i);
          }
          o.setPlaceholder('Example: folder1/folder2')
            .setValue(this.plugin.settings.folder)
            .onChange(i => {
              ((this.plugin.settings.folder = i), this.plugin.saveSettings());
            });
        });
    }
    createFileNameFormatSetting(t) {
      let o = document
        .createDocumentFragment()
        .createEl('code', {
          text:
            Ge(this.plugin.settings.fileNameFormat) || '{{title}} - {{author}}',
        });
      (new P.Setting(t)
        .setClass('book-search-plugin__settings--new_file_name')
        .setName('New file name')
        .setDesc('Enter the file name format.')
        .addSearch(i => {
          try {
            new Ze(this.app, i.inputEl);
          } catch (n) {
            console.error(n);
          }
          i.setPlaceholder('Example: {{title}} - {{author}}')
            .setValue(this.plugin.settings.fileNameFormat)
            .onChange(n => {
              ((this.plugin.settings.fileNameFormat =
                n == null ? void 0 : n.trim()),
                this.plugin.saveSettings(),
                (o.innerHTML = Ge(n) || '{{title}} - {{author}}'));
            });
        }),
        t
          .createEl('div', {
            cls: [
              'setting-item-description',
              'book-search-plugin__settings--new_file_name_hint',
            ],
          })
          .append(o));
    }
    createTemplateFileSetting(t) {
      let o = document.createDocumentFragment();
      (o.createDiv({ text: 'Files will be available as templates.' }),
        o.createEl('a', {
          text: 'Example Template',
          href: `${Do}#example-template`,
        }),
        new P.Setting(t)
          .setName('Template file')
          .setDesc(o)
          .addSearch(i => {
            try {
              new et(this.app, i.inputEl);
            } catch (n) {}
            i.setPlaceholder('Example: templates/template-file')
              .setValue(this.plugin.settings.templateFile)
              .onChange(n => {
                ((this.plugin.settings.templateFile = n),
                  this.plugin.saveSettings());
              });
          }));
    }
    display() {
      let { containerEl: t } = this;
      (t.empty(),
        t.classList.add('book-search-plugin__settings'),
        this.createGeneralSettings(t),
        this.createTemplateFileSetting(t));
      let o,
        i,
        n,
        l = () => {
          o.addClass('book-search-plugin__hide');
        },
        a = () => {
          o.removeClass('book-search-plugin__hide');
        },
        s = () => {
          i !== void 0 && i.settingEl.addClass('book-search-plugin__hide');
        },
        u = () => {
          i !== void 0 && i.settingEl.removeClass('book-search-plugin__hide');
        },
        p = () => {
          n !== void 0 && n.settingEl.addClass('book-search-plugin__hide');
        },
        d = () => {
          n !== void 0 && n.settingEl.removeClass('book-search-plugin__hide');
        },
        v = (
          c = (m =>
            (m = this.plugin.settings) == null ? void 0 : m.serviceProvider)()
        ) => {
          c === 'naver' ? (a(), s(), p()) : (l(), u(), d());
        };
      (new P.Setting(t)
        .setName('Service Provider')
        .setDesc(
          'Choose the service provider you want to use to search your books.'
        )
        .setClass('book-search-plugin__settings--service_provider')
        .addDropdown(c => {
          var m, h;
          (c.addOption('google', 'google (Global)'),
            c.addOption('naver', 'naver (Korean)'),
            c.setValue(
              (h =
                (m = this.plugin.settings) == null
                  ? void 0
                  : m.serviceProvider) != null
                ? h
                : 'google'
            ),
            c.onChange(async w => {
              let y = w;
              (v(y),
                (this.plugin.settings.serviceProvider = y),
                await this.plugin.saveSettings());
            }));
        })
        .addExtraButton(c => {
          ((o = c.extraSettingsEl),
            v(),
            c.onClick(() => {
              new Xe(this.plugin).open();
            }));
        }),
        (i = new P.Setting(t)
          .setName('Preferred locale')
          .setDesc('Sets the preferred locale to use when searching for books.')
          .addDropdown(c => {
            let m = window.moment.locale();
            (c.addOption(m, `${ye[m] || m} (Default Locale)`),
              window.moment.locales().forEach(w => {
                let y = ye[w];
                y && w !== m && c.addOption(w, y);
              }));
            let h = this.plugin.settings.localePreference;
            c.setValue(h === Fe.localePreference ? m : h).onChange(async w => {
              let y = w;
              ((this.plugin.settings.localePreference = y),
                await this.plugin.saveSettings());
            });
          })),
        new P.Setting(t)
          .setName('Open New Book Note')
          .setDesc(
            'Enable or disable the automatic opening of the note on creation.'
          )
          .addToggle(c =>
            c
              .setValue(this.plugin.settings.openPageOnCompletion)
              .onChange(async m => {
                ((this.plugin.settings.openPageOnCompletion = m),
                  await this.plugin.saveSettings());
              })
          ),
        new P.Setting(t)
          .setName('Show Cover Images in Search')
          .setDesc('Toggle to show or hide cover images in the search results.')
          .addToggle(c =>
            c
              .setValue(this.plugin.settings.showCoverImageInSearch)
              .onChange(async m => {
                ((this.plugin.settings.showCoverImageInSearch = m),
                  await this.plugin.saveSettings());
              })
          ),
        new P.Setting(t)
          .setName('Ask for Locale')
          .setDesc(
            'Toggle to enable or disable asking for the locale every time a search is made.'
          )
          .addToggle(c =>
            c.setValue(this.plugin.settings.askForLocale).onChange(async m => {
              ((this.plugin.settings.askForLocale = m),
                await this.plugin.saveSettings());
            })
          ),
        (n = new P.Setting(t)
          .setName('Enable Cover Image Edge Curl Effect')
          .setDesc('Toggle to show or hide page curl effect in cover images.')
          .addToggle(c =>
            c
              .setValue(this.plugin.settings.enableCoverImageEdgeCurl)
              .onChange(async m => {
                ((this.plugin.settings.enableCoverImageEdgeCurl = m),
                  await this.plugin.saveSettings());
              })
          )),
        new P.Setting(t)
          .setName('Enable Cover Image Save')
          .setDesc('Toggle to enable or disable saving cover images in notes.')
          .addToggle(c =>
            c
              .setValue(this.plugin.settings.enableCoverImageSave)
              .onChange(async m => {
                ((this.plugin.settings.enableCoverImageSave = m),
                  await this.plugin.saveSettings());
              })
          ),
        new P.Setting(t)
          .setName('Cover Image Path')
          .setDesc('Specify the path where cover images should be saved.')
          .addSearch(c => {
            try {
              new Re(this.app, c.inputEl);
            } catch (m) {}
            c.setPlaceholder('Enter the path (e.g., Images/Covers)')
              .setValue(this.plugin.settings.coverImagePath)
              .onChange(async m => {
                ((this.plugin.settings.coverImagePath = m.trim()),
                  await this.plugin.saveSettings());
              });
          }),
        this.createHeader('Google API Settings', t),
        new P.Setting(t)
          .setName('Description About Google API Settings')
          .setDesc(
            '**WARNING** please use this field after you must understand Google Cloud API, such as API key security.'
          ),
        new P.Setting(t)
          .setName('Status Check')
          .setDesc(
            'check whether API key is saved. It does not guarantee that the API key is valid or invalid.'
          )
          .addButton(c => {
            c.setButtonText('API Check').onClick(async () => {
              this.plugin.settings.apiKey.length
                ? new P.Notice('API key exist.')
                : new P.Notice('API key does not exist.');
            });
          }));
      let f = document.createDocumentFragment();
      (f.createDiv({ text: 'Set your Books API key.' }),
        f.createDiv({
          text: 'For security reason, saved API key is not shown in this textarea after saved.',
        }));
      let S = '';
      new P.Setting(t)
        .setName('Set API Key')
        .setDesc(f)
        .addText(c => {
          ((c.inputEl.type = 'password'),
            c.setValue('').onChange(async m => {
              S = m;
            }));
        })
        .addButton(c => {
          c.setButtonText('Save Key').onClick(async () => {
            ((this.plugin.settings.apiKey = S),
              await this.plugin.saveSettings(),
              new P.Notice('API key Saved'));
          });
        });
    }
  };
var K = require('obsidian'),
  rt = class extends K.Modal {
    constructor(t, o, i) {
      super(t.app);
      this.plugin = t;
      this.query = o;
      this.callback = i;
      this.SEARCH_BUTTON_TEXT = 'Search';
      this.REQUESTING_BUTTON_TEXT = 'Requesting...';
      this.isBusy = !1;
      ((this.options = { locale: t.settings.localePreference }),
        (this.serviceProvider = _t(t.settings)));
    }
    setBusy(t) {
      var o;
      ((this.isBusy = t),
        (o = this.okBtnRef) == null ||
          o
            .setDisabled(t)
            .setButtonText(
              t ? this.REQUESTING_BUTTON_TEXT : this.SEARCH_BUTTON_TEXT
            ));
    }
    async searchBook() {
      if (!this.query) return void new K.Notice('No query entered.');
      if (!this.isBusy) {
        this.setBusy(!0);
        try {
          let t = await this.serviceProvider.getByQuery(
            this.query,
            this.options
          );
          if (!(t != null && t.length))
            return void new K.Notice(`No results found for "${this.query}"`);
          this.callback(null, t);
        } catch (t) {
          this.callback(t);
        } finally {
          (this.setBusy(!1), this.close());
        }
      }
    }
    onOpen() {
      let { contentEl: t } = this;
      (t.createEl('h2', { text: 'Search Book' }),
        this.plugin.settings.serviceProvider === 'google' &&
          this.plugin.settings.askForLocale &&
          this.renderSelectLocale(),
        t.createDiv({ cls: 'book-search-plugin__search-modal--input' }, o => {
          new K.TextComponent(o)
            .setValue(this.query)
            .setPlaceholder('Search by keyword or ISBN')
            .onChange(i => (this.query = i))
            .inputEl.addEventListener(
              'keydown',
              i => i.key === 'Enter' && !i.isComposing && this.searchBook()
            );
        }),
        new K.Setting(this.contentEl).addButton(o => {
          this.okBtnRef = o
            .setButtonText(this.SEARCH_BUTTON_TEXT)
            .setCta()
            .onClick(() => this.searchBook());
        }));
    }
    renderSelectLocale() {
      let t = window.moment.locale();
      new K.Setting(this.contentEl).setName('Locale').addDropdown(o => {
        (o.addOption(t, `${ye[t] || t}`),
          window.moment.locales().forEach(i => {
            let n = ye[i];
            n && i !== t && o.addOption(i, n);
          }),
          o
            .setValue(
              this.options.locale === Fe.localePreference
                ? t
                : this.options.locale
            )
            .onChange(i => (this.options.locale = i)));
      });
    }
    onClose() {
      let { contentEl: t } = this;
      t.empty();
    }
  };
var yr = require('obsidian'),
  ot = class extends yr.SuggestModal {
    constructor(t, o, i, n) {
      super(t);
      this.suggestion = i;
      this.onChoose = n;
      this.showCoverImageInSearch = o;
    }
    getSuggestions(t) {
      return this.suggestion.filter(o => {
        var n, l, a;
        let i = t == null ? void 0 : t.toLowerCase();
        return (
          ((n = o.title) == null ? void 0 : n.toLowerCase().includes(i)) ||
          ((l = o.author) == null ? void 0 : l.toLowerCase().includes(i)) ||
          ((a = o.publisher) == null ? void 0 : a.toLowerCase().includes(i))
        );
      });
    }
    renderSuggestion(t, o) {
      o.addClass('book-suggestion-item');
      let i =
        t.coverLargeUrl || t.coverMediumUrl || t.coverSmallUrl || t.coverUrl;
      this.showCoverImageInSearch &&
        i &&
        o.createEl('img', {
          cls: 'book-cover-image',
          attr: { src: i, alt: `Cover Image for ${t.title}` },
        });
      let n = o.createEl('div', { cls: 'book-text-info' });
      n.createEl('div', { text: t.title });
      let l = t.publisher ? `, ${t.publisher}` : '',
        a = t.publishDate ? `(${t.publishDate})` : '',
        s = t.totalPage ? `, p${t.totalPage}` : '',
        u = `${t.author}${l}${a}${s}`;
      n.createEl('small', { text: u });
    }
    onChooseSuggestion(t) {
      this.onChoose(null, t);
    }
  };
var br = require('obsidian'),
  it = class {
    constructor(e) {
      this.app = e;
    }
    async jumpToNextCursorLocation() {
      let e = this.app.workspace.getActiveViewOfType(br.MarkdownView);
      if (!e) return;
      let o = (await this.app.vault.cachedRead(e.file)).length + 1,
        i = e.editor;
      (i.focus(), i.setCursor(o, 0));
    }
  };
var nt = require('obsidian');
async function xr(r, e) {
  let { metadataCache: t, vault: o } = r,
    i = (0, nt.normalizePath)(e != null ? e : '');
  if (e === '/') return Promise.resolve('');
  try {
    let n = t.getFirstLinkpathDest(i, '');
    return n ? o.cachedRead(n) : '';
  } catch (n) {
    return (
      console.error(`Failed to read the daily note template '${i}'`, n),
      new nt.Notice('Failed to read the daily note template'),
      ''
    );
  }
}
function Sr(r) {
  return r.replace(
    /{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,
    (e, t, o, i, n, l) => {
      let a = window.moment(),
        s = window
          .moment()
          .clone()
          .set({
            hour: a.get('hour'),
            minute: a.get('minute'),
            second: a.get('second'),
          });
      return (
        o && s.add(parseInt(i, 10), n),
        l ? s.format(l.substring(1).trim()) : s.format('YYYY-MM-DD')
      );
    }
  );
}
function Er(r, e) {
  let t = /<%(?:=)(.+)%>/g,
    o = No();
  return [...e.matchAll(t)].reduce((n, [l, a]) => {
    try {
      let s = new o(
        [
          'const [book] = arguments',
          `const output = ${a}`,
          'if(typeof output === "string") return output',
          'return JSON.stringify(output)',
        ].join(';')
      )(r);
      return n.replace(l, s);
    } catch (s) {
      console.warn(s);
    }
    return n;
  }, e);
}
function No() {
  try {
    return new Function('return (function(){}).constructor')();
  } catch (r) {
    throw (
      console.warn(r),
      r instanceof SyntaxError ? Error('Bad template syntax') : r
    );
  }
}
async function Cr(r, e) {
  let t = r.plugins.plugins['templater-obsidian'];
  t &&
    !(t != null && t.settings.trigger_on_file_creation) &&
    (await t.templater.overwrite_file_commands(e));
}
var at = class extends oe.Plugin {
  async onload() {
    (await this.loadSettings(),
      this.addRibbonIcon('book', 'Create new book note', () =>
        this.createNewBookNote()
      ).addClass('obsidian-book-search-plugin-ribbon-class'),
      this.addCommand({
        id: 'open-book-search-modal',
        name: 'Create new book note',
        callback: () => this.createNewBookNote(),
      }),
      this.addCommand({
        id: 'open-book-search-modal-to-insert',
        name: 'Insert the metadata',
        callback: () => this.insertMetadata(),
      }),
      this.addSettingTab(new tt(this.app, this)),
      console.log(
        `Book Search: version ${this.manifest.version} (requires obsidian ${this.manifest.minAppVersion})`
      ));
  }
  showNotice(e) {
    try {
      new oe.Notice(e == null ? void 0 : e.toString());
    } catch (t) {}
  }
  async searchBookMetadata(e) {
    let t = await this.openBookSearchModal(e);
    return await this.openBookSuggestModal(t);
  }
  async getRenderedContents(e) {
    let {
        templateFile: t,
        useDefaultFrontmatter: o,
        defaultFrontmatterKeyType: i,
        enableCoverImageSave: n,
        coverImagePath: l,
        frontmatter: a,
        content: s,
      } = this.settings,
      u = '';
    if (n) {
      let p =
        e.coverLargeUrl || e.coverMediumUrl || e.coverSmallUrl || e.coverUrl;
      if (p) {
        let d = ht(e, this.settings.fileNameFormat, 'jpg');
        e.localCoverImage = await this.downloadAndSaveImage(d, l, p);
      }
    }
    if (t) {
      let p = await xr(this.app, t),
        d = Be(e, Sr(p));
      u += Er(e, d);
    } else {
      let p = Be(e, a);
      o && (p = zt(Xt(e, p, i)));
      let d = Be(e, s);
      u += p
        ? `---
${p}
---
${d}`
        : d;
    }
    return u;
  }
  async downloadAndSaveImage(e, t, o) {
    let { enableCoverImageSave: i } = this.settings;
    if (!i) return (console.warn('Cover image saving is not enabled.'), '');
    try {
      let n = await (0, oe.requestUrl)({
        url: o,
        method: 'GET',
        headers: { Accept: 'image/*' },
      });
      if (n.status !== 200)
        throw new Error(`Failed to download image: ${n.status}`);
      let l = n.arrayBuffer,
        a = `${t}/${e}`;
      return (await this.app.vault.adapter.writeBinary(a, l), a);
    } catch (n) {
      return (console.error('Error downloading or saving image:', n), '');
    }
  }
  async insertMetadata() {
    try {
      let e = this.app.workspace.getActiveViewOfType(oe.MarkdownView);
      if (!e) {
        console.warn('Can not find an active markdown view');
        return;
      }
      let t = await this.searchBookMetadata(e.file.basename);
      if (!e.editor) {
        console.warn('Can not find editor from the active markdown view');
        return;
      }
      let o = await this.getRenderedContents(t);
      e.editor.replaceRange(o, { line: 0, ch: 0 });
    } catch (e) {
      (console.warn(e), this.showNotice(e));
    }
  }
  async createNewBookNote() {
    try {
      let e = await this.searchBookMetadata(),
        t = await this.getRenderedContents(e),
        o = ht(e, this.settings.fileNameFormat),
        i = `${this.settings.folder}/${o}`,
        n = await this.app.vault.create(i, t);
      (await Cr(this.app, n), this.openNewBookNote(n));
    } catch (e) {
      (console.warn(e), this.showNotice(e));
    }
  }
  async openNewBookNote(e) {
    if (!this.settings.openPageOnCompletion) return;
    let t = this.app.workspace.getLeaf();
    if (!t) {
      console.warn('No active leaf');
      return;
    }
    (await t.openFile(e, { state: { mode: 'source' } }),
      t.setEphemeralState({ rename: 'all' }),
      await new it(this.app).jumpToNextCursorLocation());
  }
  async openBookSearchModal(e = '') {
    return new Promise((t, o) =>
      new rt(this, e, (i, n) => (i ? o(i) : t(n))).open()
    );
  }
  async openBookSuggestModal(e) {
    return new Promise((t, o) =>
      new ot(this.app, this.settings.showCoverImageInSearch, e, (i, n) =>
        i ? o(i) : t(n)
      ).open()
    );
  }
  async loadSettings() {
    this.settings = Object.assign({}, Fe, await this.loadData());
  }
  async saveSettings() {
    await this.saveData(this.settings);
  }
};

/* nosourcemap */

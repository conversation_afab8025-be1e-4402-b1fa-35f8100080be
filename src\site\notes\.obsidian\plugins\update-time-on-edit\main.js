/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

'use strict';
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __commonJS = (cb, mod) =>
  function __require() {
    return (
      mod ||
        (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod),
      mod.exports
    );
  };
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if ((from && typeof from === 'object') || typeof from === 'function') {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, {
          get: () => from[key],
          enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable,
        });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (
  (target = mod != null ? __create(__getProtoOf(mod)) : {}),
  __copyProps(
    // If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule
      ? __defProp(target, 'default', { value: mod, enumerable: true })
      : target,
    mod
  )
);
var __toCommonJS = mod =>
  __copyProps(__defProp({}, '__esModule', { value: true }), mod);

// node_modules/js-sha256/src/sha256.js
var require_sha256 = __commonJS({
  'node_modules/js-sha256/src/sha256.js'(exports, module2) {
    (function () {
      'use strict';
      var ERROR = 'input is invalid type';
      var WINDOW = typeof window === 'object';
      var root = WINDOW ? window : {};
      if (root.JS_SHA256_NO_WINDOW) {
        WINDOW = false;
      }
      var WEB_WORKER = !WINDOW && typeof self === 'object';
      var NODE_JS =
        !root.JS_SHA256_NO_NODE_JS &&
        typeof process === 'object' &&
        process.versions &&
        process.versions.node;
      if (NODE_JS) {
        root = global;
      } else if (WEB_WORKER) {
        root = self;
      }
      var COMMON_JS =
        !root.JS_SHA256_NO_COMMON_JS &&
        typeof module2 === 'object' &&
        module2.exports;
      var AMD = typeof define === 'function' && define.amd;
      var ARRAY_BUFFER =
        !root.JS_SHA256_NO_ARRAY_BUFFER && typeof ArrayBuffer !== 'undefined';
      var HEX_CHARS = '0123456789abcdef'.split('');
      var EXTRA = [-2147483648, 8388608, 32768, 128];
      var SHIFT = [24, 16, 8, 0];
      var K = [
        1116352408, 1899447441, 3049323471, 3921009573, 961987163, 1508970993,
        2453635748, 2870763221, 3624381080, 310598401, 607225278, 1426881987,
        1925078388, 2162078206, 2614888103, 3248222580, 3835390401, 4022224774,
        264347078, 604807628, 770255983, 1249150122, 1555081692, 1996064986,
        2554220882, 2821834349, 2952996808, 3210313671, 3336571891, 3584528711,
        113926993, 338241895, 666307205, 773529912, 1294757372, 1396182291,
        1695183700, 1986661051, 2177026350, 2456956037, 2730485921, 2820302411,
        3259730800, 3345764771, 3516065817, 3600352804, 4094571909, 275423344,
        430227734, 506948616, 659060556, 883997877, 958139571, 1322822218,
        1537002063, 1747873779, 1955562222, 2024104815, 2227730452, 2361852424,
        2428436474, 2756734187, 3204031479, 3329325298,
      ];
      var OUTPUT_TYPES = ['hex', 'array', 'digest', 'arrayBuffer'];
      var blocks = [];
      if (root.JS_SHA256_NO_NODE_JS || !Array.isArray) {
        Array.isArray = function (obj) {
          return Object.prototype.toString.call(obj) === '[object Array]';
        };
      }
      if (
        ARRAY_BUFFER &&
        (root.JS_SHA256_NO_ARRAY_BUFFER_IS_VIEW || !ArrayBuffer.isView)
      ) {
        ArrayBuffer.isView = function (obj) {
          return (
            typeof obj === 'object' &&
            obj.buffer &&
            obj.buffer.constructor === ArrayBuffer
          );
        };
      }
      var createOutputMethod = function (outputType, is224) {
        return function (message) {
          return new Sha256(is224, true).update(message)[outputType]();
        };
      };
      var createMethod = function (is224) {
        var method = createOutputMethod('hex', is224);
        if (NODE_JS) {
          method = nodeWrap(method, is224);
        }
        method.create = function () {
          return new Sha256(is224);
        };
        method.update = function (message) {
          return method.create().update(message);
        };
        for (var i = 0; i < OUTPUT_TYPES.length; ++i) {
          var type = OUTPUT_TYPES[i];
          method[type] = createOutputMethod(type, is224);
        }
        return method;
      };
      var nodeWrap = function (method, is224) {
        var crypto = require('crypto');
        var Buffer2 = require('buffer').Buffer;
        var algorithm = is224 ? 'sha224' : 'sha256';
        var bufferFrom;
        if (Buffer2.from && !root.JS_SHA256_NO_BUFFER_FROM) {
          bufferFrom = Buffer2.from;
        } else {
          bufferFrom = function (message) {
            return new Buffer2(message);
          };
        }
        var nodeMethod = function (message) {
          if (typeof message === 'string') {
            return crypto
              .createHash(algorithm)
              .update(message, 'utf8')
              .digest('hex');
          } else {
            if (message === null || message === void 0) {
              throw new Error(ERROR);
            } else if (message.constructor === ArrayBuffer) {
              message = new Uint8Array(message);
            }
          }
          if (
            Array.isArray(message) ||
            ArrayBuffer.isView(message) ||
            message.constructor === Buffer2
          ) {
            return crypto
              .createHash(algorithm)
              .update(bufferFrom(message))
              .digest('hex');
          } else {
            return method(message);
          }
        };
        return nodeMethod;
      };
      var createHmacOutputMethod = function (outputType, is224) {
        return function (key, message) {
          return new HmacSha256(key, is224, true).update(message)[outputType]();
        };
      };
      var createHmacMethod = function (is224) {
        var method = createHmacOutputMethod('hex', is224);
        method.create = function (key) {
          return new HmacSha256(key, is224);
        };
        method.update = function (key, message) {
          return method.create(key).update(message);
        };
        for (var i = 0; i < OUTPUT_TYPES.length; ++i) {
          var type = OUTPUT_TYPES[i];
          method[type] = createHmacOutputMethod(type, is224);
        }
        return method;
      };
      function Sha256(is224, sharedMemory) {
        if (sharedMemory) {
          blocks[0] =
            blocks[16] =
            blocks[1] =
            blocks[2] =
            blocks[3] =
            blocks[4] =
            blocks[5] =
            blocks[6] =
            blocks[7] =
            blocks[8] =
            blocks[9] =
            blocks[10] =
            blocks[11] =
            blocks[12] =
            blocks[13] =
            blocks[14] =
            blocks[15] =
              0;
          this.blocks = blocks;
        } else {
          this.blocks = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        }
        if (is224) {
          this.h0 = 3238371032;
          this.h1 = 914150663;
          this.h2 = 812702999;
          this.h3 = 4144912697;
          this.h4 = 4290775857;
          this.h5 = 1750603025;
          this.h6 = 1694076839;
          this.h7 = 3204075428;
        } else {
          this.h0 = 1779033703;
          this.h1 = 3144134277;
          this.h2 = 1013904242;
          this.h3 = 2773480762;
          this.h4 = 1359893119;
          this.h5 = 2600822924;
          this.h6 = 528734635;
          this.h7 = 1541459225;
        }
        this.block = this.start = this.bytes = this.hBytes = 0;
        this.finalized = this.hashed = false;
        this.first = true;
        this.is224 = is224;
      }
      Sha256.prototype.update = function (message) {
        if (this.finalized) {
          return;
        }
        var notString,
          type = typeof message;
        if (type !== 'string') {
          if (type === 'object') {
            if (message === null) {
              throw new Error(ERROR);
            } else if (ARRAY_BUFFER && message.constructor === ArrayBuffer) {
              message = new Uint8Array(message);
            } else if (!Array.isArray(message)) {
              if (!ARRAY_BUFFER || !ArrayBuffer.isView(message)) {
                throw new Error(ERROR);
              }
            }
          } else {
            throw new Error(ERROR);
          }
          notString = true;
        }
        var code,
          index = 0,
          i,
          length = message.length,
          blocks2 = this.blocks;
        while (index < length) {
          if (this.hashed) {
            this.hashed = false;
            blocks2[0] = this.block;
            blocks2[16] =
              blocks2[1] =
              blocks2[2] =
              blocks2[3] =
              blocks2[4] =
              blocks2[5] =
              blocks2[6] =
              blocks2[7] =
              blocks2[8] =
              blocks2[9] =
              blocks2[10] =
              blocks2[11] =
              blocks2[12] =
              blocks2[13] =
              blocks2[14] =
              blocks2[15] =
                0;
          }
          if (notString) {
            for (i = this.start; index < length && i < 64; ++index) {
              blocks2[i >> 2] |= message[index] << SHIFT[i++ & 3];
            }
          } else {
            for (i = this.start; index < length && i < 64; ++index) {
              code = message.charCodeAt(index);
              if (code < 128) {
                blocks2[i >> 2] |= code << SHIFT[i++ & 3];
              } else if (code < 2048) {
                blocks2[i >> 2] |= (192 | (code >> 6)) << SHIFT[i++ & 3];
                blocks2[i >> 2] |= (128 | (code & 63)) << SHIFT[i++ & 3];
              } else if (code < 55296 || code >= 57344) {
                blocks2[i >> 2] |= (224 | (code >> 12)) << SHIFT[i++ & 3];
                blocks2[i >> 2] |= (128 | ((code >> 6) & 63)) << SHIFT[i++ & 3];
                blocks2[i >> 2] |= (128 | (code & 63)) << SHIFT[i++ & 3];
              } else {
                code =
                  65536 +
                  (((code & 1023) << 10) |
                    (message.charCodeAt(++index) & 1023));
                blocks2[i >> 2] |= (240 | (code >> 18)) << SHIFT[i++ & 3];
                blocks2[i >> 2] |=
                  (128 | ((code >> 12) & 63)) << SHIFT[i++ & 3];
                blocks2[i >> 2] |= (128 | ((code >> 6) & 63)) << SHIFT[i++ & 3];
                blocks2[i >> 2] |= (128 | (code & 63)) << SHIFT[i++ & 3];
              }
            }
          }
          this.lastByteIndex = i;
          this.bytes += i - this.start;
          if (i >= 64) {
            this.block = blocks2[16];
            this.start = i - 64;
            this.hash();
            this.hashed = true;
          } else {
            this.start = i;
          }
        }
        if (this.bytes > 4294967295) {
          this.hBytes += (this.bytes / 4294967296) << 0;
          this.bytes = this.bytes % 4294967296;
        }
        return this;
      };
      Sha256.prototype.finalize = function () {
        if (this.finalized) {
          return;
        }
        this.finalized = true;
        var blocks2 = this.blocks,
          i = this.lastByteIndex;
        blocks2[16] = this.block;
        blocks2[i >> 2] |= EXTRA[i & 3];
        this.block = blocks2[16];
        if (i >= 56) {
          if (!this.hashed) {
            this.hash();
          }
          blocks2[0] = this.block;
          blocks2[16] =
            blocks2[1] =
            blocks2[2] =
            blocks2[3] =
            blocks2[4] =
            blocks2[5] =
            blocks2[6] =
            blocks2[7] =
            blocks2[8] =
            blocks2[9] =
            blocks2[10] =
            blocks2[11] =
            blocks2[12] =
            blocks2[13] =
            blocks2[14] =
            blocks2[15] =
              0;
        }
        blocks2[14] = (this.hBytes << 3) | (this.bytes >>> 29);
        blocks2[15] = this.bytes << 3;
        this.hash();
      };
      Sha256.prototype.hash = function () {
        var a = this.h0,
          b = this.h1,
          c = this.h2,
          d = this.h3,
          e = this.h4,
          f = this.h5,
          g = this.h6,
          h = this.h7,
          blocks2 = this.blocks,
          j,
          s0,
          s1,
          maj,
          t1,
          t2,
          ch,
          ab,
          da,
          cd,
          bc;
        for (j = 16; j < 64; ++j) {
          t1 = blocks2[j - 15];
          s0 =
            ((t1 >>> 7) | (t1 << 25)) ^ ((t1 >>> 18) | (t1 << 14)) ^ (t1 >>> 3);
          t1 = blocks2[j - 2];
          s1 =
            ((t1 >>> 17) | (t1 << 15)) ^
            ((t1 >>> 19) | (t1 << 13)) ^
            (t1 >>> 10);
          blocks2[j] = (blocks2[j - 16] + s0 + blocks2[j - 7] + s1) << 0;
        }
        bc = b & c;
        for (j = 0; j < 64; j += 4) {
          if (this.first) {
            if (this.is224) {
              ab = 300032;
              t1 = blocks2[0] - 1413257819;
              h = (t1 - 150054599) << 0;
              d = (t1 + 24177077) << 0;
            } else {
              ab = 704751109;
              t1 = blocks2[0] - 210244248;
              h = (t1 - 1521486534) << 0;
              d = (t1 + 143694565) << 0;
            }
            this.first = false;
          } else {
            s0 =
              ((a >>> 2) | (a << 30)) ^
              ((a >>> 13) | (a << 19)) ^
              ((a >>> 22) | (a << 10));
            s1 =
              ((e >>> 6) | (e << 26)) ^
              ((e >>> 11) | (e << 21)) ^
              ((e >>> 25) | (e << 7));
            ab = a & b;
            maj = ab ^ (a & c) ^ bc;
            ch = (e & f) ^ (~e & g);
            t1 = h + s1 + ch + K[j] + blocks2[j];
            t2 = s0 + maj;
            h = (d + t1) << 0;
            d = (t1 + t2) << 0;
          }
          s0 =
            ((d >>> 2) | (d << 30)) ^
            ((d >>> 13) | (d << 19)) ^
            ((d >>> 22) | (d << 10));
          s1 =
            ((h >>> 6) | (h << 26)) ^
            ((h >>> 11) | (h << 21)) ^
            ((h >>> 25) | (h << 7));
          da = d & a;
          maj = da ^ (d & b) ^ ab;
          ch = (h & e) ^ (~h & f);
          t1 = g + s1 + ch + K[j + 1] + blocks2[j + 1];
          t2 = s0 + maj;
          g = (c + t1) << 0;
          c = (t1 + t2) << 0;
          s0 =
            ((c >>> 2) | (c << 30)) ^
            ((c >>> 13) | (c << 19)) ^
            ((c >>> 22) | (c << 10));
          s1 =
            ((g >>> 6) | (g << 26)) ^
            ((g >>> 11) | (g << 21)) ^
            ((g >>> 25) | (g << 7));
          cd = c & d;
          maj = cd ^ (c & a) ^ da;
          ch = (g & h) ^ (~g & e);
          t1 = f + s1 + ch + K[j + 2] + blocks2[j + 2];
          t2 = s0 + maj;
          f = (b + t1) << 0;
          b = (t1 + t2) << 0;
          s0 =
            ((b >>> 2) | (b << 30)) ^
            ((b >>> 13) | (b << 19)) ^
            ((b >>> 22) | (b << 10));
          s1 =
            ((f >>> 6) | (f << 26)) ^
            ((f >>> 11) | (f << 21)) ^
            ((f >>> 25) | (f << 7));
          bc = b & c;
          maj = bc ^ (b & d) ^ cd;
          ch = (f & g) ^ (~f & h);
          t1 = e + s1 + ch + K[j + 3] + blocks2[j + 3];
          t2 = s0 + maj;
          e = (a + t1) << 0;
          a = (t1 + t2) << 0;
          this.chromeBugWorkAround = true;
        }
        this.h0 = (this.h0 + a) << 0;
        this.h1 = (this.h1 + b) << 0;
        this.h2 = (this.h2 + c) << 0;
        this.h3 = (this.h3 + d) << 0;
        this.h4 = (this.h4 + e) << 0;
        this.h5 = (this.h5 + f) << 0;
        this.h6 = (this.h6 + g) << 0;
        this.h7 = (this.h7 + h) << 0;
      };
      Sha256.prototype.hex = function () {
        this.finalize();
        var h0 = this.h0,
          h1 = this.h1,
          h2 = this.h2,
          h3 = this.h3,
          h4 = this.h4,
          h5 = this.h5,
          h6 = this.h6,
          h7 = this.h7;
        var hex =
          HEX_CHARS[(h0 >> 28) & 15] +
          HEX_CHARS[(h0 >> 24) & 15] +
          HEX_CHARS[(h0 >> 20) & 15] +
          HEX_CHARS[(h0 >> 16) & 15] +
          HEX_CHARS[(h0 >> 12) & 15] +
          HEX_CHARS[(h0 >> 8) & 15] +
          HEX_CHARS[(h0 >> 4) & 15] +
          HEX_CHARS[h0 & 15] +
          HEX_CHARS[(h1 >> 28) & 15] +
          HEX_CHARS[(h1 >> 24) & 15] +
          HEX_CHARS[(h1 >> 20) & 15] +
          HEX_CHARS[(h1 >> 16) & 15] +
          HEX_CHARS[(h1 >> 12) & 15] +
          HEX_CHARS[(h1 >> 8) & 15] +
          HEX_CHARS[(h1 >> 4) & 15] +
          HEX_CHARS[h1 & 15] +
          HEX_CHARS[(h2 >> 28) & 15] +
          HEX_CHARS[(h2 >> 24) & 15] +
          HEX_CHARS[(h2 >> 20) & 15] +
          HEX_CHARS[(h2 >> 16) & 15] +
          HEX_CHARS[(h2 >> 12) & 15] +
          HEX_CHARS[(h2 >> 8) & 15] +
          HEX_CHARS[(h2 >> 4) & 15] +
          HEX_CHARS[h2 & 15] +
          HEX_CHARS[(h3 >> 28) & 15] +
          HEX_CHARS[(h3 >> 24) & 15] +
          HEX_CHARS[(h3 >> 20) & 15] +
          HEX_CHARS[(h3 >> 16) & 15] +
          HEX_CHARS[(h3 >> 12) & 15] +
          HEX_CHARS[(h3 >> 8) & 15] +
          HEX_CHARS[(h3 >> 4) & 15] +
          HEX_CHARS[h3 & 15] +
          HEX_CHARS[(h4 >> 28) & 15] +
          HEX_CHARS[(h4 >> 24) & 15] +
          HEX_CHARS[(h4 >> 20) & 15] +
          HEX_CHARS[(h4 >> 16) & 15] +
          HEX_CHARS[(h4 >> 12) & 15] +
          HEX_CHARS[(h4 >> 8) & 15] +
          HEX_CHARS[(h4 >> 4) & 15] +
          HEX_CHARS[h4 & 15] +
          HEX_CHARS[(h5 >> 28) & 15] +
          HEX_CHARS[(h5 >> 24) & 15] +
          HEX_CHARS[(h5 >> 20) & 15] +
          HEX_CHARS[(h5 >> 16) & 15] +
          HEX_CHARS[(h5 >> 12) & 15] +
          HEX_CHARS[(h5 >> 8) & 15] +
          HEX_CHARS[(h5 >> 4) & 15] +
          HEX_CHARS[h5 & 15] +
          HEX_CHARS[(h6 >> 28) & 15] +
          HEX_CHARS[(h6 >> 24) & 15] +
          HEX_CHARS[(h6 >> 20) & 15] +
          HEX_CHARS[(h6 >> 16) & 15] +
          HEX_CHARS[(h6 >> 12) & 15] +
          HEX_CHARS[(h6 >> 8) & 15] +
          HEX_CHARS[(h6 >> 4) & 15] +
          HEX_CHARS[h6 & 15];
        if (!this.is224) {
          hex +=
            HEX_CHARS[(h7 >> 28) & 15] +
            HEX_CHARS[(h7 >> 24) & 15] +
            HEX_CHARS[(h7 >> 20) & 15] +
            HEX_CHARS[(h7 >> 16) & 15] +
            HEX_CHARS[(h7 >> 12) & 15] +
            HEX_CHARS[(h7 >> 8) & 15] +
            HEX_CHARS[(h7 >> 4) & 15] +
            HEX_CHARS[h7 & 15];
        }
        return hex;
      };
      Sha256.prototype.toString = Sha256.prototype.hex;
      Sha256.prototype.digest = function () {
        this.finalize();
        var h0 = this.h0,
          h1 = this.h1,
          h2 = this.h2,
          h3 = this.h3,
          h4 = this.h4,
          h5 = this.h5,
          h6 = this.h6,
          h7 = this.h7;
        var arr = [
          (h0 >> 24) & 255,
          (h0 >> 16) & 255,
          (h0 >> 8) & 255,
          h0 & 255,
          (h1 >> 24) & 255,
          (h1 >> 16) & 255,
          (h1 >> 8) & 255,
          h1 & 255,
          (h2 >> 24) & 255,
          (h2 >> 16) & 255,
          (h2 >> 8) & 255,
          h2 & 255,
          (h3 >> 24) & 255,
          (h3 >> 16) & 255,
          (h3 >> 8) & 255,
          h3 & 255,
          (h4 >> 24) & 255,
          (h4 >> 16) & 255,
          (h4 >> 8) & 255,
          h4 & 255,
          (h5 >> 24) & 255,
          (h5 >> 16) & 255,
          (h5 >> 8) & 255,
          h5 & 255,
          (h6 >> 24) & 255,
          (h6 >> 16) & 255,
          (h6 >> 8) & 255,
          h6 & 255,
        ];
        if (!this.is224) {
          arr.push(
            (h7 >> 24) & 255,
            (h7 >> 16) & 255,
            (h7 >> 8) & 255,
            h7 & 255
          );
        }
        return arr;
      };
      Sha256.prototype.array = Sha256.prototype.digest;
      Sha256.prototype.arrayBuffer = function () {
        this.finalize();
        var buffer = new ArrayBuffer(this.is224 ? 28 : 32);
        var dataView = new DataView(buffer);
        dataView.setUint32(0, this.h0);
        dataView.setUint32(4, this.h1);
        dataView.setUint32(8, this.h2);
        dataView.setUint32(12, this.h3);
        dataView.setUint32(16, this.h4);
        dataView.setUint32(20, this.h5);
        dataView.setUint32(24, this.h6);
        if (!this.is224) {
          dataView.setUint32(28, this.h7);
        }
        return buffer;
      };
      function HmacSha256(key, is224, sharedMemory) {
        var i,
          type = typeof key;
        if (type === 'string') {
          var bytes = [],
            length = key.length,
            index = 0,
            code;
          for (i = 0; i < length; ++i) {
            code = key.charCodeAt(i);
            if (code < 128) {
              bytes[index++] = code;
            } else if (code < 2048) {
              bytes[index++] = 192 | (code >> 6);
              bytes[index++] = 128 | (code & 63);
            } else if (code < 55296 || code >= 57344) {
              bytes[index++] = 224 | (code >> 12);
              bytes[index++] = 128 | ((code >> 6) & 63);
              bytes[index++] = 128 | (code & 63);
            } else {
              code =
                65536 + (((code & 1023) << 10) | (key.charCodeAt(++i) & 1023));
              bytes[index++] = 240 | (code >> 18);
              bytes[index++] = 128 | ((code >> 12) & 63);
              bytes[index++] = 128 | ((code >> 6) & 63);
              bytes[index++] = 128 | (code & 63);
            }
          }
          key = bytes;
        } else {
          if (type === 'object') {
            if (key === null) {
              throw new Error(ERROR);
            } else if (ARRAY_BUFFER && key.constructor === ArrayBuffer) {
              key = new Uint8Array(key);
            } else if (!Array.isArray(key)) {
              if (!ARRAY_BUFFER || !ArrayBuffer.isView(key)) {
                throw new Error(ERROR);
              }
            }
          } else {
            throw new Error(ERROR);
          }
        }
        if (key.length > 64) {
          key = new Sha256(is224, true).update(key).array();
        }
        var oKeyPad = [],
          iKeyPad = [];
        for (i = 0; i < 64; ++i) {
          var b = key[i] || 0;
          oKeyPad[i] = 92 ^ b;
          iKeyPad[i] = 54 ^ b;
        }
        Sha256.call(this, is224, sharedMemory);
        this.update(iKeyPad);
        this.oKeyPad = oKeyPad;
        this.inner = true;
        this.sharedMemory = sharedMemory;
      }
      HmacSha256.prototype = new Sha256();
      HmacSha256.prototype.finalize = function () {
        Sha256.prototype.finalize.call(this);
        if (this.inner) {
          this.inner = false;
          var innerHash = this.array();
          Sha256.call(this, this.is224, this.sharedMemory);
          this.update(this.oKeyPad);
          this.update(innerHash);
          Sha256.prototype.finalize.call(this);
        }
      };
      var exports2 = createMethod();
      exports2.sha256 = exports2;
      exports2.sha224 = createMethod(true);
      exports2.sha256.hmac = createHmacMethod();
      exports2.sha224.hmac = createHmacMethod(true);
      if (COMMON_JS) {
        module2.exports = exports2;
      } else {
        root.sha256 = exports2.sha256;
        root.sha224 = exports2.sha224;
        if (AMD) {
          define(function () {
            return exports2;
          });
        }
      }
    })();
  },
});

// src/main.ts
var main_exports = {};
__export(main_exports, {
  default: () => UpdateTimeOnSavePlugin,
});
module.exports = __toCommonJS(main_exports);
var import_obsidian6 = require('obsidian');

// node_modules/date-fns/esm/_lib/requiredArgs/index.js
function requiredArgs(required, args) {
  if (args.length < required) {
    throw new TypeError(
      required +
        ' argument' +
        (required > 1 ? 's' : '') +
        ' required, but only ' +
        args.length +
        ' present'
    );
  }
}

// node_modules/date-fns/esm/toDate/index.js
function toDate(argument) {
  requiredArgs(1, arguments);
  var argStr = Object.prototype.toString.call(argument);
  if (
    argument instanceof Date ||
    (typeof argument === 'object' && argStr === '[object Date]')
  ) {
    return new Date(argument.getTime());
  } else if (typeof argument === 'number' || argStr === '[object Number]') {
    return new Date(argument);
  } else {
    if (
      (typeof argument === 'string' || argStr === '[object String]') &&
      typeof console !== 'undefined'
    ) {
      console.warn(
        "Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://git.io/fjule"
      );
      console.warn(new Error().stack);
    }
    return new Date(NaN);
  }
}

// node_modules/date-fns/esm/isValid/index.js
function isValid(dirtyDate) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  return !isNaN(date);
}

// node_modules/date-fns/esm/locale/en-US/_lib/formatDistance/index.js
var formatDistanceLocale = {
  lessThanXSeconds: {
    one: 'less than a second',
    other: 'less than {{count}} seconds',
  },
  xSeconds: {
    one: '1 second',
    other: '{{count}} seconds',
  },
  halfAMinute: 'half a minute',
  lessThanXMinutes: {
    one: 'less than a minute',
    other: 'less than {{count}} minutes',
  },
  xMinutes: {
    one: '1 minute',
    other: '{{count}} minutes',
  },
  aboutXHours: {
    one: 'about 1 hour',
    other: 'about {{count}} hours',
  },
  xHours: {
    one: '1 hour',
    other: '{{count}} hours',
  },
  xDays: {
    one: '1 day',
    other: '{{count}} days',
  },
  aboutXWeeks: {
    one: 'about 1 week',
    other: 'about {{count}} weeks',
  },
  xWeeks: {
    one: '1 week',
    other: '{{count}} weeks',
  },
  aboutXMonths: {
    one: 'about 1 month',
    other: 'about {{count}} months',
  },
  xMonths: {
    one: '1 month',
    other: '{{count}} months',
  },
  aboutXYears: {
    one: 'about 1 year',
    other: 'about {{count}} years',
  },
  xYears: {
    one: '1 year',
    other: '{{count}} years',
  },
  overXYears: {
    one: 'over 1 year',
    other: 'over {{count}} years',
  },
  almostXYears: {
    one: 'almost 1 year',
    other: 'almost {{count}} years',
  },
};
function formatDistance(token, count, options) {
  options = options || {};
  var result;
  if (typeof formatDistanceLocale[token] === 'string') {
    result = formatDistanceLocale[token];
  } else if (count === 1) {
    result = formatDistanceLocale[token].one;
  } else {
    result = formatDistanceLocale[token].other.replace('{{count}}', count);
  }
  if (options.addSuffix) {
    if (options.comparison > 0) {
      return 'in ' + result;
    } else {
      return result + ' ago';
    }
  }
  return result;
}

// node_modules/date-fns/esm/locale/_lib/buildFormatLongFn/index.js
function buildFormatLongFn(args) {
  return function (dirtyOptions) {
    var options = dirtyOptions || {};
    var width = options.width ? String(options.width) : args.defaultWidth;
    var format3 = args.formats[width] || args.formats[args.defaultWidth];
    return format3;
  };
}

// node_modules/date-fns/esm/locale/en-US/_lib/formatLong/index.js
var dateFormats = {
  full: 'EEEE, MMMM do, y',
  long: 'MMMM do, y',
  medium: 'MMM d, y',
  short: 'MM/dd/yyyy',
};
var timeFormats = {
  full: 'h:mm:ss a zzzz',
  long: 'h:mm:ss a z',
  medium: 'h:mm:ss a',
  short: 'h:mm a',
};
var dateTimeFormats = {
  full: "{{date}} 'at' {{time}}",
  long: "{{date}} 'at' {{time}}",
  medium: '{{date}}, {{time}}',
  short: '{{date}}, {{time}}',
};
var formatLong = {
  date: buildFormatLongFn({
    formats: dateFormats,
    defaultWidth: 'full',
  }),
  time: buildFormatLongFn({
    formats: timeFormats,
    defaultWidth: 'full',
  }),
  dateTime: buildFormatLongFn({
    formats: dateTimeFormats,
    defaultWidth: 'full',
  }),
};
var formatLong_default = formatLong;

// node_modules/date-fns/esm/locale/en-US/_lib/formatRelative/index.js
var formatRelativeLocale = {
  lastWeek: "'last' eeee 'at' p",
  yesterday: "'yesterday at' p",
  today: "'today at' p",
  tomorrow: "'tomorrow at' p",
  nextWeek: "eeee 'at' p",
  other: 'P',
};
function formatRelative(token, _date, _baseDate, _options) {
  return formatRelativeLocale[token];
}

// node_modules/date-fns/esm/locale/_lib/buildLocalizeFn/index.js
function buildLocalizeFn(args) {
  return function (dirtyIndex, dirtyOptions) {
    var options = dirtyOptions || {};
    var context = options.context ? String(options.context) : 'standalone';
    var valuesArray;
    if (context === 'formatting' && args.formattingValues) {
      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;
      var width = options.width ? String(options.width) : defaultWidth;
      valuesArray =
        args.formattingValues[width] || args.formattingValues[defaultWidth];
    } else {
      var _defaultWidth = args.defaultWidth;
      var _width = options.width ? String(options.width) : args.defaultWidth;
      valuesArray = args.values[_width] || args.values[_defaultWidth];
    }
    var index = args.argumentCallback
      ? args.argumentCallback(dirtyIndex)
      : dirtyIndex;
    return valuesArray[index];
  };
}

// node_modules/date-fns/esm/locale/en-US/_lib/localize/index.js
var eraValues = {
  narrow: ['B', 'A'],
  abbreviated: ['BC', 'AD'],
  wide: ['Before Christ', 'Anno Domini'],
};
var quarterValues = {
  narrow: ['1', '2', '3', '4'],
  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],
  wide: ['1st quarter', '2nd quarter', '3rd quarter', '4th quarter'],
  // Note: in English, the names of days of the week and months are capitalized.
  // If you are making a new locale based on this one, check if the same is true for the language you're working on.
  // Generally, formatted dates should look like they are in the middle of a sentence,
  // e.g. in Spanish language the weekdays and months should be in the lowercase.
};
var monthValues = {
  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
  abbreviated: [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ],
  wide: [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ],
};
var dayValues = {
  narrow: ['S', 'M', 'T', 'W', 'T', 'F', 'S'],
  short: ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'],
  abbreviated: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
  wide: [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ],
};
var dayPeriodValues = {
  narrow: {
    am: 'a',
    pm: 'p',
    midnight: 'mi',
    noon: 'n',
    morning: 'morning',
    afternoon: 'afternoon',
    evening: 'evening',
    night: 'night',
  },
  abbreviated: {
    am: 'AM',
    pm: 'PM',
    midnight: 'midnight',
    noon: 'noon',
    morning: 'morning',
    afternoon: 'afternoon',
    evening: 'evening',
    night: 'night',
  },
  wide: {
    am: 'a.m.',
    pm: 'p.m.',
    midnight: 'midnight',
    noon: 'noon',
    morning: 'morning',
    afternoon: 'afternoon',
    evening: 'evening',
    night: 'night',
  },
};
var formattingDayPeriodValues = {
  narrow: {
    am: 'a',
    pm: 'p',
    midnight: 'mi',
    noon: 'n',
    morning: 'in the morning',
    afternoon: 'in the afternoon',
    evening: 'in the evening',
    night: 'at night',
  },
  abbreviated: {
    am: 'AM',
    pm: 'PM',
    midnight: 'midnight',
    noon: 'noon',
    morning: 'in the morning',
    afternoon: 'in the afternoon',
    evening: 'in the evening',
    night: 'at night',
  },
  wide: {
    am: 'a.m.',
    pm: 'p.m.',
    midnight: 'midnight',
    noon: 'noon',
    morning: 'in the morning',
    afternoon: 'in the afternoon',
    evening: 'in the evening',
    night: 'at night',
  },
};
function ordinalNumber(dirtyNumber, _dirtyOptions) {
  var number = Number(dirtyNumber);
  var rem100 = number % 100;
  if (rem100 > 20 || rem100 < 10) {
    switch (rem100 % 10) {
      case 1:
        return number + 'st';
      case 2:
        return number + 'nd';
      case 3:
        return number + 'rd';
    }
  }
  return number + 'th';
}
var localize = {
  ordinalNumber,
  era: buildLocalizeFn({
    values: eraValues,
    defaultWidth: 'wide',
  }),
  quarter: buildLocalizeFn({
    values: quarterValues,
    defaultWidth: 'wide',
    argumentCallback: function (quarter) {
      return Number(quarter) - 1;
    },
  }),
  month: buildLocalizeFn({
    values: monthValues,
    defaultWidth: 'wide',
  }),
  day: buildLocalizeFn({
    values: dayValues,
    defaultWidth: 'wide',
  }),
  dayPeriod: buildLocalizeFn({
    values: dayPeriodValues,
    defaultWidth: 'wide',
    formattingValues: formattingDayPeriodValues,
    defaultFormattingWidth: 'wide',
  }),
};
var localize_default = localize;

// node_modules/date-fns/esm/locale/_lib/buildMatchPatternFn/index.js
function buildMatchPatternFn(args) {
  return function (dirtyString, dirtyOptions) {
    var string = String(dirtyString);
    var options = dirtyOptions || {};
    var matchResult = string.match(args.matchPattern);
    if (!matchResult) {
      return null;
    }
    var matchedString = matchResult[0];
    var parseResult = string.match(args.parsePattern);
    if (!parseResult) {
      return null;
    }
    var value = args.valueCallback
      ? args.valueCallback(parseResult[0])
      : parseResult[0];
    value = options.valueCallback ? options.valueCallback(value) : value;
    return {
      value,
      rest: string.slice(matchedString.length),
    };
  };
}

// node_modules/date-fns/esm/locale/_lib/buildMatchFn/index.js
function buildMatchFn(args) {
  return function (dirtyString, dirtyOptions) {
    var string = String(dirtyString);
    var options = dirtyOptions || {};
    var width = options.width;
    var matchPattern =
      (width && args.matchPatterns[width]) ||
      args.matchPatterns[args.defaultMatchWidth];
    var matchResult = string.match(matchPattern);
    if (!matchResult) {
      return null;
    }
    var matchedString = matchResult[0];
    var parsePatterns =
      (width && args.parsePatterns[width]) ||
      args.parsePatterns[args.defaultParseWidth];
    var value;
    if (Object.prototype.toString.call(parsePatterns) === '[object Array]') {
      value = findIndex(parsePatterns, function (pattern) {
        return pattern.test(matchedString);
      });
    } else {
      value = findKey(parsePatterns, function (pattern) {
        return pattern.test(matchedString);
      });
    }
    value = args.valueCallback ? args.valueCallback(value) : value;
    value = options.valueCallback ? options.valueCallback(value) : value;
    return {
      value,
      rest: string.slice(matchedString.length),
    };
  };
}
function findKey(object, predicate) {
  for (var key in object) {
    if (object.hasOwnProperty(key) && predicate(object[key])) {
      return key;
    }
  }
}
function findIndex(array, predicate) {
  for (var key = 0; key < array.length; key++) {
    if (predicate(array[key])) {
      return key;
    }
  }
}

// node_modules/date-fns/esm/locale/en-US/_lib/match/index.js
var matchOrdinalNumberPattern = /^(\d+)(th|st|nd|rd)?/i;
var parseOrdinalNumberPattern = /\d+/i;
var matchEraPatterns = {
  narrow: /^(b|a)/i,
  abbreviated: /^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,
  wide: /^(before christ|before common era|anno domini|common era)/i,
};
var parseEraPatterns = {
  any: [/^b/i, /^(a|c)/i],
};
var matchQuarterPatterns = {
  narrow: /^[1234]/i,
  abbreviated: /^q[1234]/i,
  wide: /^[1234](th|st|nd|rd)? quarter/i,
};
var parseQuarterPatterns = {
  any: [/1/i, /2/i, /3/i, /4/i],
};
var matchMonthPatterns = {
  narrow: /^[jfmasond]/i,
  abbreviated: /^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,
  wide: /^(january|february|march|april|may|june|july|august|september|october|november|december)/i,
};
var parseMonthPatterns = {
  narrow: [
    /^j/i,
    /^f/i,
    /^m/i,
    /^a/i,
    /^m/i,
    /^j/i,
    /^j/i,
    /^a/i,
    /^s/i,
    /^o/i,
    /^n/i,
    /^d/i,
  ],
  any: [
    /^ja/i,
    /^f/i,
    /^mar/i,
    /^ap/i,
    /^may/i,
    /^jun/i,
    /^jul/i,
    /^au/i,
    /^s/i,
    /^o/i,
    /^n/i,
    /^d/i,
  ],
};
var matchDayPatterns = {
  narrow: /^[smtwf]/i,
  short: /^(su|mo|tu|we|th|fr|sa)/i,
  abbreviated: /^(sun|mon|tue|wed|thu|fri|sat)/i,
  wide: /^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i,
};
var parseDayPatterns = {
  narrow: [/^s/i, /^m/i, /^t/i, /^w/i, /^t/i, /^f/i, /^s/i],
  any: [/^su/i, /^m/i, /^tu/i, /^w/i, /^th/i, /^f/i, /^sa/i],
};
var matchDayPeriodPatterns = {
  narrow: /^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,
  any: /^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i,
};
var parseDayPeriodPatterns = {
  any: {
    am: /^a/i,
    pm: /^p/i,
    midnight: /^mi/i,
    noon: /^no/i,
    morning: /morning/i,
    afternoon: /afternoon/i,
    evening: /evening/i,
    night: /night/i,
  },
};
var match = {
  ordinalNumber: buildMatchPatternFn({
    matchPattern: matchOrdinalNumberPattern,
    parsePattern: parseOrdinalNumberPattern,
    valueCallback: function (value) {
      return parseInt(value, 10);
    },
  }),
  era: buildMatchFn({
    matchPatterns: matchEraPatterns,
    defaultMatchWidth: 'wide',
    parsePatterns: parseEraPatterns,
    defaultParseWidth: 'any',
  }),
  quarter: buildMatchFn({
    matchPatterns: matchQuarterPatterns,
    defaultMatchWidth: 'wide',
    parsePatterns: parseQuarterPatterns,
    defaultParseWidth: 'any',
    valueCallback: function (index) {
      return index + 1;
    },
  }),
  month: buildMatchFn({
    matchPatterns: matchMonthPatterns,
    defaultMatchWidth: 'wide',
    parsePatterns: parseMonthPatterns,
    defaultParseWidth: 'any',
  }),
  day: buildMatchFn({
    matchPatterns: matchDayPatterns,
    defaultMatchWidth: 'wide',
    parsePatterns: parseDayPatterns,
    defaultParseWidth: 'any',
  }),
  dayPeriod: buildMatchFn({
    matchPatterns: matchDayPeriodPatterns,
    defaultMatchWidth: 'any',
    parsePatterns: parseDayPeriodPatterns,
    defaultParseWidth: 'any',
  }),
};
var match_default = match;

// node_modules/date-fns/esm/locale/en-US/index.js
var locale = {
  code: 'en-US',
  formatDistance,
  formatLong: formatLong_default,
  formatRelative,
  localize: localize_default,
  match: match_default,
  options: {
    weekStartsOn: 0,
    firstWeekContainsDate: 1,
  },
};
var en_US_default = locale;

// node_modules/date-fns/esm/_lib/toInteger/index.js
function toInteger(dirtyNumber) {
  if (dirtyNumber === null || dirtyNumber === true || dirtyNumber === false) {
    return NaN;
  }
  var number = Number(dirtyNumber);
  if (isNaN(number)) {
    return number;
  }
  return number < 0 ? Math.ceil(number) : Math.floor(number);
}

// node_modules/date-fns/esm/addMilliseconds/index.js
function addMilliseconds(dirtyDate, dirtyAmount) {
  requiredArgs(2, arguments);
  var timestamp = toDate(dirtyDate).getTime();
  var amount = toInteger(dirtyAmount);
  return new Date(timestamp + amount);
}

// node_modules/date-fns/esm/subMilliseconds/index.js
function subMilliseconds(dirtyDate, dirtyAmount) {
  requiredArgs(2, arguments);
  var amount = toInteger(dirtyAmount);
  return addMilliseconds(dirtyDate, -amount);
}

// node_modules/date-fns/esm/_lib/addLeadingZeros/index.js
function addLeadingZeros(number, targetLength) {
  var sign = number < 0 ? '-' : '';
  var output = Math.abs(number).toString();
  while (output.length < targetLength) {
    output = '0' + output;
  }
  return sign + output;
}

// node_modules/date-fns/esm/_lib/format/lightFormatters/index.js
var formatters = {
  // Year
  y: function (date, token) {
    var signedYear = date.getUTCFullYear();
    var year = signedYear > 0 ? signedYear : 1 - signedYear;
    return addLeadingZeros(token === 'yy' ? year % 100 : year, token.length);
  },
  // Month
  M: function (date, token) {
    var month = date.getUTCMonth();
    return token === 'M' ? String(month + 1) : addLeadingZeros(month + 1, 2);
  },
  // Day of the month
  d: function (date, token) {
    return addLeadingZeros(date.getUTCDate(), token.length);
  },
  // AM or PM
  a: function (date, token) {
    var dayPeriodEnumValue = date.getUTCHours() / 12 >= 1 ? 'pm' : 'am';
    switch (token) {
      case 'a':
      case 'aa':
        return dayPeriodEnumValue.toUpperCase();
      case 'aaa':
        return dayPeriodEnumValue;
      case 'aaaaa':
        return dayPeriodEnumValue[0];
      case 'aaaa':
      default:
        return dayPeriodEnumValue === 'am' ? 'a.m.' : 'p.m.';
    }
  },
  // Hour [1-12]
  h: function (date, token) {
    return addLeadingZeros(date.getUTCHours() % 12 || 12, token.length);
  },
  // Hour [0-23]
  H: function (date, token) {
    return addLeadingZeros(date.getUTCHours(), token.length);
  },
  // Minute
  m: function (date, token) {
    return addLeadingZeros(date.getUTCMinutes(), token.length);
  },
  // Second
  s: function (date, token) {
    return addLeadingZeros(date.getUTCSeconds(), token.length);
  },
  // Fraction of second
  S: function (date, token) {
    var numberOfDigits = token.length;
    var milliseconds = date.getUTCMilliseconds();
    var fractionalSeconds = Math.floor(
      milliseconds * Math.pow(10, numberOfDigits - 3)
    );
    return addLeadingZeros(fractionalSeconds, token.length);
  },
};
var lightFormatters_default = formatters;

// node_modules/date-fns/esm/_lib/getUTCDayOfYear/index.js
var MILLISECONDS_IN_DAY = 864e5;
function getUTCDayOfYear(dirtyDate) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  var timestamp = date.getTime();
  date.setUTCMonth(0, 1);
  date.setUTCHours(0, 0, 0, 0);
  var startOfYearTimestamp = date.getTime();
  var difference = timestamp - startOfYearTimestamp;
  return Math.floor(difference / MILLISECONDS_IN_DAY) + 1;
}

// node_modules/date-fns/esm/_lib/startOfUTCISOWeek/index.js
function startOfUTCISOWeek(dirtyDate) {
  requiredArgs(1, arguments);
  var weekStartsOn = 1;
  var date = toDate(dirtyDate);
  var day = date.getUTCDay();
  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;
  date.setUTCDate(date.getUTCDate() - diff);
  date.setUTCHours(0, 0, 0, 0);
  return date;
}

// node_modules/date-fns/esm/_lib/getUTCISOWeekYear/index.js
function getUTCISOWeekYear(dirtyDate) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  var year = date.getUTCFullYear();
  var fourthOfJanuaryOfNextYear = new Date(0);
  fourthOfJanuaryOfNextYear.setUTCFullYear(year + 1, 0, 4);
  fourthOfJanuaryOfNextYear.setUTCHours(0, 0, 0, 0);
  var startOfNextYear = startOfUTCISOWeek(fourthOfJanuaryOfNextYear);
  var fourthOfJanuaryOfThisYear = new Date(0);
  fourthOfJanuaryOfThisYear.setUTCFullYear(year, 0, 4);
  fourthOfJanuaryOfThisYear.setUTCHours(0, 0, 0, 0);
  var startOfThisYear = startOfUTCISOWeek(fourthOfJanuaryOfThisYear);
  if (date.getTime() >= startOfNextYear.getTime()) {
    return year + 1;
  } else if (date.getTime() >= startOfThisYear.getTime()) {
    return year;
  } else {
    return year - 1;
  }
}

// node_modules/date-fns/esm/_lib/startOfUTCISOWeekYear/index.js
function startOfUTCISOWeekYear(dirtyDate) {
  requiredArgs(1, arguments);
  var year = getUTCISOWeekYear(dirtyDate);
  var fourthOfJanuary = new Date(0);
  fourthOfJanuary.setUTCFullYear(year, 0, 4);
  fourthOfJanuary.setUTCHours(0, 0, 0, 0);
  var date = startOfUTCISOWeek(fourthOfJanuary);
  return date;
}

// node_modules/date-fns/esm/_lib/getUTCISOWeek/index.js
var MILLISECONDS_IN_WEEK = 6048e5;
function getUTCISOWeek(dirtyDate) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  var diff =
    startOfUTCISOWeek(date).getTime() - startOfUTCISOWeekYear(date).getTime();
  return Math.round(diff / MILLISECONDS_IN_WEEK) + 1;
}

// node_modules/date-fns/esm/_lib/startOfUTCWeek/index.js
function startOfUTCWeek(dirtyDate, dirtyOptions) {
  requiredArgs(1, arguments);
  var options = dirtyOptions || {};
  var locale2 = options.locale;
  var localeWeekStartsOn =
    locale2 && locale2.options && locale2.options.weekStartsOn;
  var defaultWeekStartsOn =
    localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);
  var weekStartsOn =
    options.weekStartsOn == null
      ? defaultWeekStartsOn
      : toInteger(options.weekStartsOn);
  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {
    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');
  }
  var date = toDate(dirtyDate);
  var day = date.getUTCDay();
  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;
  date.setUTCDate(date.getUTCDate() - diff);
  date.setUTCHours(0, 0, 0, 0);
  return date;
}

// node_modules/date-fns/esm/_lib/getUTCWeekYear/index.js
function getUTCWeekYear(dirtyDate, dirtyOptions) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate, dirtyOptions);
  var year = date.getUTCFullYear();
  var options = dirtyOptions || {};
  var locale2 = options.locale;
  var localeFirstWeekContainsDate =
    locale2 && locale2.options && locale2.options.firstWeekContainsDate;
  var defaultFirstWeekContainsDate =
    localeFirstWeekContainsDate == null
      ? 1
      : toInteger(localeFirstWeekContainsDate);
  var firstWeekContainsDate =
    options.firstWeekContainsDate == null
      ? defaultFirstWeekContainsDate
      : toInteger(options.firstWeekContainsDate);
  if (!(firstWeekContainsDate >= 1 && firstWeekContainsDate <= 7)) {
    throw new RangeError(
      'firstWeekContainsDate must be between 1 and 7 inclusively'
    );
  }
  var firstWeekOfNextYear = new Date(0);
  firstWeekOfNextYear.setUTCFullYear(year + 1, 0, firstWeekContainsDate);
  firstWeekOfNextYear.setUTCHours(0, 0, 0, 0);
  var startOfNextYear = startOfUTCWeek(firstWeekOfNextYear, dirtyOptions);
  var firstWeekOfThisYear = new Date(0);
  firstWeekOfThisYear.setUTCFullYear(year, 0, firstWeekContainsDate);
  firstWeekOfThisYear.setUTCHours(0, 0, 0, 0);
  var startOfThisYear = startOfUTCWeek(firstWeekOfThisYear, dirtyOptions);
  if (date.getTime() >= startOfNextYear.getTime()) {
    return year + 1;
  } else if (date.getTime() >= startOfThisYear.getTime()) {
    return year;
  } else {
    return year - 1;
  }
}

// node_modules/date-fns/esm/_lib/startOfUTCWeekYear/index.js
function startOfUTCWeekYear(dirtyDate, dirtyOptions) {
  requiredArgs(1, arguments);
  var options = dirtyOptions || {};
  var locale2 = options.locale;
  var localeFirstWeekContainsDate =
    locale2 && locale2.options && locale2.options.firstWeekContainsDate;
  var defaultFirstWeekContainsDate =
    localeFirstWeekContainsDate == null
      ? 1
      : toInteger(localeFirstWeekContainsDate);
  var firstWeekContainsDate =
    options.firstWeekContainsDate == null
      ? defaultFirstWeekContainsDate
      : toInteger(options.firstWeekContainsDate);
  var year = getUTCWeekYear(dirtyDate, dirtyOptions);
  var firstWeek = new Date(0);
  firstWeek.setUTCFullYear(year, 0, firstWeekContainsDate);
  firstWeek.setUTCHours(0, 0, 0, 0);
  var date = startOfUTCWeek(firstWeek, dirtyOptions);
  return date;
}

// node_modules/date-fns/esm/_lib/getUTCWeek/index.js
var MILLISECONDS_IN_WEEK2 = 6048e5;
function getUTCWeek(dirtyDate, options) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  var diff =
    startOfUTCWeek(date, options).getTime() -
    startOfUTCWeekYear(date, options).getTime();
  return Math.round(diff / MILLISECONDS_IN_WEEK2) + 1;
}

// node_modules/date-fns/esm/_lib/format/formatters/index.js
var dayPeriodEnum = {
  am: 'am',
  pm: 'pm',
  midnight: 'midnight',
  noon: 'noon',
  morning: 'morning',
  afternoon: 'afternoon',
  evening: 'evening',
  night: 'night',
  /*
   * |     | Unit                           |     | Unit                           |
   * |-----|--------------------------------|-----|--------------------------------|
   * |  a  | AM, PM                         |  A* | Milliseconds in day            |
   * |  b  | AM, PM, noon, midnight         |  B  | Flexible day period            |
   * |  c  | Stand-alone local day of week  |  C* | Localized hour w/ day period   |
   * |  d  | Day of month                   |  D  | Day of year                    |
   * |  e  | Local day of week              |  E  | Day of week                    |
   * |  f  |                                |  F* | Day of week in month           |
   * |  g* | Modified Julian day            |  G  | Era                            |
   * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |
   * |  i! | ISO day of week                |  I! | ISO week of year               |
   * |  j* | Localized hour w/ day period   |  J* | Localized hour w/o day period  |
   * |  k  | Hour [1-24]                    |  K  | Hour [0-11]                    |
   * |  l* | (deprecated)                   |  L  | Stand-alone month              |
   * |  m  | Minute                         |  M  | Month                          |
   * |  n  |                                |  N  |                                |
   * |  o! | Ordinal number modifier        |  O  | Timezone (GMT)                 |
   * |  p! | Long localized time            |  P! | Long localized date            |
   * |  q  | Stand-alone quarter            |  Q  | Quarter                        |
   * |  r* | Related Gregorian year         |  R! | ISO week-numbering year        |
   * |  s  | Second                         |  S  | Fraction of second             |
   * |  t! | Seconds timestamp              |  T! | Milliseconds timestamp         |
   * |  u  | Extended year                  |  U* | Cyclic year                    |
   * |  v* | Timezone (generic non-locat.)  |  V* | Timezone (location)            |
   * |  w  | Local week of year             |  W* | Week of month                  |
   * |  x  | Timezone (ISO-8601 w/o Z)      |  X  | Timezone (ISO-8601)            |
   * |  y  | Year (abs)                     |  Y  | Local week-numbering year      |
   * |  z  | Timezone (specific non-locat.) |  Z* | Timezone (aliases)             |
   *
   * Letters marked by * are not implemented but reserved by Unicode standard.
   *
   * Letters marked by ! are non-standard, but implemented by date-fns:
   * - `o` modifies the previous token to turn it into an ordinal (see `format` docs)
   * - `i` is ISO day of week. For `i` and `ii` is returns numeric ISO week days,
   *   i.e. 7 for Sunday, 1 for Monday, etc.
   * - `I` is ISO week of year, as opposed to `w` which is local week of year.
   * - `R` is ISO week-numbering year, as opposed to `Y` which is local week-numbering year.
   *   `R` is supposed to be used in conjunction with `I` and `i`
   *   for universal ISO week-numbering date, whereas
   *   `Y` is supposed to be used in conjunction with `w` and `e`
   *   for week-numbering date specific to the locale.
   * - `P` is long localized date format
   * - `p` is long localized time format
   */
};
var formatters2 = {
  // Era
  G: function (date, token, localize2) {
    var era = date.getUTCFullYear() > 0 ? 1 : 0;
    switch (token) {
      case 'G':
      case 'GG':
      case 'GGG':
        return localize2.era(era, {
          width: 'abbreviated',
        });
      case 'GGGGG':
        return localize2.era(era, {
          width: 'narrow',
        });
      case 'GGGG':
      default:
        return localize2.era(era, {
          width: 'wide',
        });
    }
  },
  // Year
  y: function (date, token, localize2) {
    if (token === 'yo') {
      var signedYear = date.getUTCFullYear();
      var year = signedYear > 0 ? signedYear : 1 - signedYear;
      return localize2.ordinalNumber(year, {
        unit: 'year',
      });
    }
    return lightFormatters_default.y(date, token);
  },
  // Local week-numbering year
  Y: function (date, token, localize2, options) {
    var signedWeekYear = getUTCWeekYear(date, options);
    var weekYear = signedWeekYear > 0 ? signedWeekYear : 1 - signedWeekYear;
    if (token === 'YY') {
      var twoDigitYear = weekYear % 100;
      return addLeadingZeros(twoDigitYear, 2);
    }
    if (token === 'Yo') {
      return localize2.ordinalNumber(weekYear, {
        unit: 'year',
      });
    }
    return addLeadingZeros(weekYear, token.length);
  },
  // ISO week-numbering year
  R: function (date, token) {
    var isoWeekYear = getUTCISOWeekYear(date);
    return addLeadingZeros(isoWeekYear, token.length);
  },
  // Extended year. This is a single number designating the year of this calendar system.
  // The main difference between `y` and `u` localizers are B.C. years:
  // | Year | `y` | `u` |
  // |------|-----|-----|
  // | AC 1 |   1 |   1 |
  // | BC 1 |   1 |   0 |
  // | BC 2 |   2 |  -1 |
  // Also `yy` always returns the last two digits of a year,
  // while `uu` pads single digit years to 2 characters and returns other years unchanged.
  u: function (date, token) {
    var year = date.getUTCFullYear();
    return addLeadingZeros(year, token.length);
  },
  // Quarter
  Q: function (date, token, localize2) {
    var quarter = Math.ceil((date.getUTCMonth() + 1) / 3);
    switch (token) {
      case 'Q':
        return String(quarter);
      case 'QQ':
        return addLeadingZeros(quarter, 2);
      case 'Qo':
        return localize2.ordinalNumber(quarter, {
          unit: 'quarter',
        });
      case 'QQQ':
        return localize2.quarter(quarter, {
          width: 'abbreviated',
          context: 'formatting',
        });
      case 'QQQQQ':
        return localize2.quarter(quarter, {
          width: 'narrow',
          context: 'formatting',
        });
      case 'QQQQ':
      default:
        return localize2.quarter(quarter, {
          width: 'wide',
          context: 'formatting',
        });
    }
  },
  // Stand-alone quarter
  q: function (date, token, localize2) {
    var quarter = Math.ceil((date.getUTCMonth() + 1) / 3);
    switch (token) {
      case 'q':
        return String(quarter);
      case 'qq':
        return addLeadingZeros(quarter, 2);
      case 'qo':
        return localize2.ordinalNumber(quarter, {
          unit: 'quarter',
        });
      case 'qqq':
        return localize2.quarter(quarter, {
          width: 'abbreviated',
          context: 'standalone',
        });
      case 'qqqqq':
        return localize2.quarter(quarter, {
          width: 'narrow',
          context: 'standalone',
        });
      case 'qqqq':
      default:
        return localize2.quarter(quarter, {
          width: 'wide',
          context: 'standalone',
        });
    }
  },
  // Month
  M: function (date, token, localize2) {
    var month = date.getUTCMonth();
    switch (token) {
      case 'M':
      case 'MM':
        return lightFormatters_default.M(date, token);
      case 'Mo':
        return localize2.ordinalNumber(month + 1, {
          unit: 'month',
        });
      case 'MMM':
        return localize2.month(month, {
          width: 'abbreviated',
          context: 'formatting',
        });
      case 'MMMMM':
        return localize2.month(month, {
          width: 'narrow',
          context: 'formatting',
        });
      case 'MMMM':
      default:
        return localize2.month(month, {
          width: 'wide',
          context: 'formatting',
        });
    }
  },
  // Stand-alone month
  L: function (date, token, localize2) {
    var month = date.getUTCMonth();
    switch (token) {
      case 'L':
        return String(month + 1);
      case 'LL':
        return addLeadingZeros(month + 1, 2);
      case 'Lo':
        return localize2.ordinalNumber(month + 1, {
          unit: 'month',
        });
      case 'LLL':
        return localize2.month(month, {
          width: 'abbreviated',
          context: 'standalone',
        });
      case 'LLLLL':
        return localize2.month(month, {
          width: 'narrow',
          context: 'standalone',
        });
      case 'LLLL':
      default:
        return localize2.month(month, {
          width: 'wide',
          context: 'standalone',
        });
    }
  },
  // Local week of year
  w: function (date, token, localize2, options) {
    var week = getUTCWeek(date, options);
    if (token === 'wo') {
      return localize2.ordinalNumber(week, {
        unit: 'week',
      });
    }
    return addLeadingZeros(week, token.length);
  },
  // ISO week of year
  I: function (date, token, localize2) {
    var isoWeek = getUTCISOWeek(date);
    if (token === 'Io') {
      return localize2.ordinalNumber(isoWeek, {
        unit: 'week',
      });
    }
    return addLeadingZeros(isoWeek, token.length);
  },
  // Day of the month
  d: function (date, token, localize2) {
    if (token === 'do') {
      return localize2.ordinalNumber(date.getUTCDate(), {
        unit: 'date',
      });
    }
    return lightFormatters_default.d(date, token);
  },
  // Day of year
  D: function (date, token, localize2) {
    var dayOfYear = getUTCDayOfYear(date);
    if (token === 'Do') {
      return localize2.ordinalNumber(dayOfYear, {
        unit: 'dayOfYear',
      });
    }
    return addLeadingZeros(dayOfYear, token.length);
  },
  // Day of week
  E: function (date, token, localize2) {
    var dayOfWeek = date.getUTCDay();
    switch (token) {
      case 'E':
      case 'EE':
      case 'EEE':
        return localize2.day(dayOfWeek, {
          width: 'abbreviated',
          context: 'formatting',
        });
      case 'EEEEE':
        return localize2.day(dayOfWeek, {
          width: 'narrow',
          context: 'formatting',
        });
      case 'EEEEEE':
        return localize2.day(dayOfWeek, {
          width: 'short',
          context: 'formatting',
        });
      case 'EEEE':
      default:
        return localize2.day(dayOfWeek, {
          width: 'wide',
          context: 'formatting',
        });
    }
  },
  // Local day of week
  e: function (date, token, localize2, options) {
    var dayOfWeek = date.getUTCDay();
    var localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;
    switch (token) {
      case 'e':
        return String(localDayOfWeek);
      case 'ee':
        return addLeadingZeros(localDayOfWeek, 2);
      case 'eo':
        return localize2.ordinalNumber(localDayOfWeek, {
          unit: 'day',
        });
      case 'eee':
        return localize2.day(dayOfWeek, {
          width: 'abbreviated',
          context: 'formatting',
        });
      case 'eeeee':
        return localize2.day(dayOfWeek, {
          width: 'narrow',
          context: 'formatting',
        });
      case 'eeeeee':
        return localize2.day(dayOfWeek, {
          width: 'short',
          context: 'formatting',
        });
      case 'eeee':
      default:
        return localize2.day(dayOfWeek, {
          width: 'wide',
          context: 'formatting',
        });
    }
  },
  // Stand-alone local day of week
  c: function (date, token, localize2, options) {
    var dayOfWeek = date.getUTCDay();
    var localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;
    switch (token) {
      case 'c':
        return String(localDayOfWeek);
      case 'cc':
        return addLeadingZeros(localDayOfWeek, token.length);
      case 'co':
        return localize2.ordinalNumber(localDayOfWeek, {
          unit: 'day',
        });
      case 'ccc':
        return localize2.day(dayOfWeek, {
          width: 'abbreviated',
          context: 'standalone',
        });
      case 'ccccc':
        return localize2.day(dayOfWeek, {
          width: 'narrow',
          context: 'standalone',
        });
      case 'cccccc':
        return localize2.day(dayOfWeek, {
          width: 'short',
          context: 'standalone',
        });
      case 'cccc':
      default:
        return localize2.day(dayOfWeek, {
          width: 'wide',
          context: 'standalone',
        });
    }
  },
  // ISO day of week
  i: function (date, token, localize2) {
    var dayOfWeek = date.getUTCDay();
    var isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;
    switch (token) {
      case 'i':
        return String(isoDayOfWeek);
      case 'ii':
        return addLeadingZeros(isoDayOfWeek, token.length);
      case 'io':
        return localize2.ordinalNumber(isoDayOfWeek, {
          unit: 'day',
        });
      case 'iii':
        return localize2.day(dayOfWeek, {
          width: 'abbreviated',
          context: 'formatting',
        });
      case 'iiiii':
        return localize2.day(dayOfWeek, {
          width: 'narrow',
          context: 'formatting',
        });
      case 'iiiiii':
        return localize2.day(dayOfWeek, {
          width: 'short',
          context: 'formatting',
        });
      case 'iiii':
      default:
        return localize2.day(dayOfWeek, {
          width: 'wide',
          context: 'formatting',
        });
    }
  },
  // AM or PM
  a: function (date, token, localize2) {
    var hours = date.getUTCHours();
    var dayPeriodEnumValue = hours / 12 >= 1 ? 'pm' : 'am';
    switch (token) {
      case 'a':
      case 'aa':
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: 'abbreviated',
          context: 'formatting',
        });
      case 'aaa':
        return localize2
          .dayPeriod(dayPeriodEnumValue, {
            width: 'abbreviated',
            context: 'formatting',
          })
          .toLowerCase();
      case 'aaaaa':
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: 'narrow',
          context: 'formatting',
        });
      case 'aaaa':
      default:
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: 'wide',
          context: 'formatting',
        });
    }
  },
  // AM, PM, midnight, noon
  b: function (date, token, localize2) {
    var hours = date.getUTCHours();
    var dayPeriodEnumValue;
    if (hours === 12) {
      dayPeriodEnumValue = dayPeriodEnum.noon;
    } else if (hours === 0) {
      dayPeriodEnumValue = dayPeriodEnum.midnight;
    } else {
      dayPeriodEnumValue = hours / 12 >= 1 ? 'pm' : 'am';
    }
    switch (token) {
      case 'b':
      case 'bb':
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: 'abbreviated',
          context: 'formatting',
        });
      case 'bbb':
        return localize2
          .dayPeriod(dayPeriodEnumValue, {
            width: 'abbreviated',
            context: 'formatting',
          })
          .toLowerCase();
      case 'bbbbb':
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: 'narrow',
          context: 'formatting',
        });
      case 'bbbb':
      default:
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: 'wide',
          context: 'formatting',
        });
    }
  },
  // in the morning, in the afternoon, in the evening, at night
  B: function (date, token, localize2) {
    var hours = date.getUTCHours();
    var dayPeriodEnumValue;
    if (hours >= 17) {
      dayPeriodEnumValue = dayPeriodEnum.evening;
    } else if (hours >= 12) {
      dayPeriodEnumValue = dayPeriodEnum.afternoon;
    } else if (hours >= 4) {
      dayPeriodEnumValue = dayPeriodEnum.morning;
    } else {
      dayPeriodEnumValue = dayPeriodEnum.night;
    }
    switch (token) {
      case 'B':
      case 'BB':
      case 'BBB':
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: 'abbreviated',
          context: 'formatting',
        });
      case 'BBBBB':
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: 'narrow',
          context: 'formatting',
        });
      case 'BBBB':
      default:
        return localize2.dayPeriod(dayPeriodEnumValue, {
          width: 'wide',
          context: 'formatting',
        });
    }
  },
  // Hour [1-12]
  h: function (date, token, localize2) {
    if (token === 'ho') {
      var hours = date.getUTCHours() % 12;
      if (hours === 0) hours = 12;
      return localize2.ordinalNumber(hours, {
        unit: 'hour',
      });
    }
    return lightFormatters_default.h(date, token);
  },
  // Hour [0-23]
  H: function (date, token, localize2) {
    if (token === 'Ho') {
      return localize2.ordinalNumber(date.getUTCHours(), {
        unit: 'hour',
      });
    }
    return lightFormatters_default.H(date, token);
  },
  // Hour [0-11]
  K: function (date, token, localize2) {
    var hours = date.getUTCHours() % 12;
    if (token === 'Ko') {
      return localize2.ordinalNumber(hours, {
        unit: 'hour',
      });
    }
    return addLeadingZeros(hours, token.length);
  },
  // Hour [1-24]
  k: function (date, token, localize2) {
    var hours = date.getUTCHours();
    if (hours === 0) hours = 24;
    if (token === 'ko') {
      return localize2.ordinalNumber(hours, {
        unit: 'hour',
      });
    }
    return addLeadingZeros(hours, token.length);
  },
  // Minute
  m: function (date, token, localize2) {
    if (token === 'mo') {
      return localize2.ordinalNumber(date.getUTCMinutes(), {
        unit: 'minute',
      });
    }
    return lightFormatters_default.m(date, token);
  },
  // Second
  s: function (date, token, localize2) {
    if (token === 'so') {
      return localize2.ordinalNumber(date.getUTCSeconds(), {
        unit: 'second',
      });
    }
    return lightFormatters_default.s(date, token);
  },
  // Fraction of second
  S: function (date, token) {
    return lightFormatters_default.S(date, token);
  },
  // Timezone (ISO-8601. If offset is 0, output is always `'Z'`)
  X: function (date, token, _localize, options) {
    var originalDate = options._originalDate || date;
    var timezoneOffset = originalDate.getTimezoneOffset();
    if (timezoneOffset === 0) {
      return 'Z';
    }
    switch (token) {
      case 'X':
        return formatTimezoneWithOptionalMinutes(timezoneOffset);
      case 'XXXX':
      case 'XX':
        return formatTimezone(timezoneOffset);
      case 'XXXXX':
      case 'XXX':
      default:
        return formatTimezone(timezoneOffset, ':');
    }
  },
  // Timezone (ISO-8601. If offset is 0, output is `'+00:00'` or equivalent)
  x: function (date, token, _localize, options) {
    var originalDate = options._originalDate || date;
    var timezoneOffset = originalDate.getTimezoneOffset();
    switch (token) {
      case 'x':
        return formatTimezoneWithOptionalMinutes(timezoneOffset);
      case 'xxxx':
      case 'xx':
        return formatTimezone(timezoneOffset);
      case 'xxxxx':
      case 'xxx':
      default:
        return formatTimezone(timezoneOffset, ':');
    }
  },
  // Timezone (GMT)
  O: function (date, token, _localize, options) {
    var originalDate = options._originalDate || date;
    var timezoneOffset = originalDate.getTimezoneOffset();
    switch (token) {
      case 'O':
      case 'OO':
      case 'OOO':
        return 'GMT' + formatTimezoneShort(timezoneOffset, ':');
      case 'OOOO':
      default:
        return 'GMT' + formatTimezone(timezoneOffset, ':');
    }
  },
  // Timezone (specific non-location)
  z: function (date, token, _localize, options) {
    var originalDate = options._originalDate || date;
    var timezoneOffset = originalDate.getTimezoneOffset();
    switch (token) {
      case 'z':
      case 'zz':
      case 'zzz':
        return 'GMT' + formatTimezoneShort(timezoneOffset, ':');
      case 'zzzz':
      default:
        return 'GMT' + formatTimezone(timezoneOffset, ':');
    }
  },
  // Seconds timestamp
  t: function (date, token, _localize, options) {
    var originalDate = options._originalDate || date;
    var timestamp = Math.floor(originalDate.getTime() / 1e3);
    return addLeadingZeros(timestamp, token.length);
  },
  // Milliseconds timestamp
  T: function (date, token, _localize, options) {
    var originalDate = options._originalDate || date;
    var timestamp = originalDate.getTime();
    return addLeadingZeros(timestamp, token.length);
  },
};
function formatTimezoneShort(offset2, dirtyDelimiter) {
  var sign = offset2 > 0 ? '-' : '+';
  var absOffset = Math.abs(offset2);
  var hours = Math.floor(absOffset / 60);
  var minutes = absOffset % 60;
  if (minutes === 0) {
    return sign + String(hours);
  }
  var delimiter = dirtyDelimiter || '';
  return sign + String(hours) + delimiter + addLeadingZeros(minutes, 2);
}
function formatTimezoneWithOptionalMinutes(offset2, dirtyDelimiter) {
  if (offset2 % 60 === 0) {
    var sign = offset2 > 0 ? '-' : '+';
    return sign + addLeadingZeros(Math.abs(offset2) / 60, 2);
  }
  return formatTimezone(offset2, dirtyDelimiter);
}
function formatTimezone(offset2, dirtyDelimiter) {
  var delimiter = dirtyDelimiter || '';
  var sign = offset2 > 0 ? '-' : '+';
  var absOffset = Math.abs(offset2);
  var hours = addLeadingZeros(Math.floor(absOffset / 60), 2);
  var minutes = addLeadingZeros(absOffset % 60, 2);
  return sign + hours + delimiter + minutes;
}
var formatters_default = formatters2;

// node_modules/date-fns/esm/_lib/format/longFormatters/index.js
function dateLongFormatter(pattern, formatLong2) {
  switch (pattern) {
    case 'P':
      return formatLong2.date({
        width: 'short',
      });
    case 'PP':
      return formatLong2.date({
        width: 'medium',
      });
    case 'PPP':
      return formatLong2.date({
        width: 'long',
      });
    case 'PPPP':
    default:
      return formatLong2.date({
        width: 'full',
      });
  }
}
function timeLongFormatter(pattern, formatLong2) {
  switch (pattern) {
    case 'p':
      return formatLong2.time({
        width: 'short',
      });
    case 'pp':
      return formatLong2.time({
        width: 'medium',
      });
    case 'ppp':
      return formatLong2.time({
        width: 'long',
      });
    case 'pppp':
    default:
      return formatLong2.time({
        width: 'full',
      });
  }
}
function dateTimeLongFormatter(pattern, formatLong2) {
  var matchResult = pattern.match(/(P+)(p+)?/);
  var datePattern = matchResult[1];
  var timePattern = matchResult[2];
  if (!timePattern) {
    return dateLongFormatter(pattern, formatLong2);
  }
  var dateTimeFormat;
  switch (datePattern) {
    case 'P':
      dateTimeFormat = formatLong2.dateTime({
        width: 'short',
      });
      break;
    case 'PP':
      dateTimeFormat = formatLong2.dateTime({
        width: 'medium',
      });
      break;
    case 'PPP':
      dateTimeFormat = formatLong2.dateTime({
        width: 'long',
      });
      break;
    case 'PPPP':
    default:
      dateTimeFormat = formatLong2.dateTime({
        width: 'full',
      });
      break;
  }
  return dateTimeFormat
    .replace('{{date}}', dateLongFormatter(datePattern, formatLong2))
    .replace('{{time}}', timeLongFormatter(timePattern, formatLong2));
}
var longFormatters = {
  p: timeLongFormatter,
  P: dateTimeLongFormatter,
};
var longFormatters_default = longFormatters;

// node_modules/date-fns/esm/_lib/getTimezoneOffsetInMilliseconds/index.js
function getTimezoneOffsetInMilliseconds(date) {
  var utcDate = new Date(
    Date.UTC(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      date.getHours(),
      date.getMinutes(),
      date.getSeconds(),
      date.getMilliseconds()
    )
  );
  utcDate.setUTCFullYear(date.getFullYear());
  return date.getTime() - utcDate.getTime();
}

// node_modules/date-fns/esm/_lib/protectedTokens/index.js
var protectedDayOfYearTokens = ['D', 'DD'];
var protectedWeekYearTokens = ['YY', 'YYYY'];
function isProtectedDayOfYearToken(token) {
  return protectedDayOfYearTokens.indexOf(token) !== -1;
}
function isProtectedWeekYearToken(token) {
  return protectedWeekYearTokens.indexOf(token) !== -1;
}
function throwProtectedError(token, format3, input) {
  if (token === 'YYYY') {
    throw new RangeError(
      'Use `yyyy` instead of `YYYY` (in `'
        .concat(format3, '`) for formatting years to the input `')
        .concat(input, '`; see: https://git.io/fxCyr')
    );
  } else if (token === 'YY') {
    throw new RangeError(
      'Use `yy` instead of `YY` (in `'
        .concat(format3, '`) for formatting years to the input `')
        .concat(input, '`; see: https://git.io/fxCyr')
    );
  } else if (token === 'D') {
    throw new RangeError(
      'Use `d` instead of `D` (in `'
        .concat(format3, '`) for formatting days of the month to the input `')
        .concat(input, '`; see: https://git.io/fxCyr')
    );
  } else if (token === 'DD') {
    throw new RangeError(
      'Use `dd` instead of `DD` (in `'
        .concat(format3, '`) for formatting days of the month to the input `')
        .concat(input, '`; see: https://git.io/fxCyr')
    );
  }
}

// node_modules/date-fns/esm/format/index.js
var formattingTokensRegExp =
  /[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g;
var longFormattingTokensRegExp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;
var escapedStringRegExp = /^'([^]*?)'?$/;
var doubleQuoteRegExp = /''/g;
var unescapedLatinCharacterRegExp = /[a-zA-Z]/;
function format(dirtyDate, dirtyFormatStr, dirtyOptions) {
  requiredArgs(2, arguments);
  var formatStr = String(dirtyFormatStr);
  var options = dirtyOptions || {};
  var locale2 = options.locale || en_US_default;
  var localeFirstWeekContainsDate =
    locale2.options && locale2.options.firstWeekContainsDate;
  var defaultFirstWeekContainsDate =
    localeFirstWeekContainsDate == null
      ? 1
      : toInteger(localeFirstWeekContainsDate);
  var firstWeekContainsDate =
    options.firstWeekContainsDate == null
      ? defaultFirstWeekContainsDate
      : toInteger(options.firstWeekContainsDate);
  if (!(firstWeekContainsDate >= 1 && firstWeekContainsDate <= 7)) {
    throw new RangeError(
      'firstWeekContainsDate must be between 1 and 7 inclusively'
    );
  }
  var localeWeekStartsOn = locale2.options && locale2.options.weekStartsOn;
  var defaultWeekStartsOn =
    localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);
  var weekStartsOn =
    options.weekStartsOn == null
      ? defaultWeekStartsOn
      : toInteger(options.weekStartsOn);
  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {
    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');
  }
  if (!locale2.localize) {
    throw new RangeError('locale must contain localize property');
  }
  if (!locale2.formatLong) {
    throw new RangeError('locale must contain formatLong property');
  }
  var originalDate = toDate(dirtyDate);
  if (!isValid(originalDate)) {
    throw new RangeError('Invalid time value');
  }
  var timezoneOffset = getTimezoneOffsetInMilliseconds(originalDate);
  var utcDate = subMilliseconds(originalDate, timezoneOffset);
  var formatterOptions = {
    firstWeekContainsDate,
    weekStartsOn,
    locale: locale2,
    _originalDate: originalDate,
  };
  var result = formatStr
    .match(longFormattingTokensRegExp)
    .map(function (substring) {
      var firstCharacter = substring[0];
      if (firstCharacter === 'p' || firstCharacter === 'P') {
        var longFormatter = longFormatters_default[firstCharacter];
        return longFormatter(substring, locale2.formatLong, formatterOptions);
      }
      return substring;
    })
    .join('')
    .match(formattingTokensRegExp)
    .map(function (substring) {
      if (substring === "''") {
        return "'";
      }
      var firstCharacter = substring[0];
      if (firstCharacter === "'") {
        return cleanEscapedString(substring);
      }
      var formatter = formatters_default[firstCharacter];
      if (formatter) {
        if (
          !options.useAdditionalWeekYearTokens &&
          isProtectedWeekYearToken(substring)
        ) {
          throwProtectedError(substring, dirtyFormatStr, dirtyDate);
        }
        if (
          !options.useAdditionalDayOfYearTokens &&
          isProtectedDayOfYearToken(substring)
        ) {
          throwProtectedError(substring, dirtyFormatStr, dirtyDate);
        }
        return formatter(
          utcDate,
          substring,
          locale2.localize,
          formatterOptions
        );
      }
      if (firstCharacter.match(unescapedLatinCharacterRegExp)) {
        throw new RangeError(
          'Format string contains an unescaped latin alphabet character `' +
            firstCharacter +
            '`'
        );
      }
      return substring;
    })
    .join('');
  return result;
}
function cleanEscapedString(input) {
  return input.match(escapedStringRegExp)[1].replace(doubleQuoteRegExp, "'");
}

// node_modules/date-fns/esm/_lib/assign/index.js
function assign(target, dirtyObject) {
  if (target == null) {
    throw new TypeError(
      'assign requires that input parameter not be null or undefined'
    );
  }
  dirtyObject = dirtyObject || {};
  for (var property in dirtyObject) {
    if (dirtyObject.hasOwnProperty(property)) {
      target[property] = dirtyObject[property];
    }
  }
  return target;
}

// node_modules/date-fns/esm/_lib/setUTCDay/index.js
function setUTCDay(dirtyDate, dirtyDay, dirtyOptions) {
  requiredArgs(2, arguments);
  var options = dirtyOptions || {};
  var locale2 = options.locale;
  var localeWeekStartsOn =
    locale2 && locale2.options && locale2.options.weekStartsOn;
  var defaultWeekStartsOn =
    localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);
  var weekStartsOn =
    options.weekStartsOn == null
      ? defaultWeekStartsOn
      : toInteger(options.weekStartsOn);
  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {
    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');
  }
  var date = toDate(dirtyDate);
  var day = toInteger(dirtyDay);
  var currentDay = date.getUTCDay();
  var remainder = day % 7;
  var dayIndex = (remainder + 7) % 7;
  var diff = (dayIndex < weekStartsOn ? 7 : 0) + day - currentDay;
  date.setUTCDate(date.getUTCDate() + diff);
  return date;
}

// node_modules/date-fns/esm/_lib/setUTCISODay/index.js
function setUTCISODay(dirtyDate, dirtyDay) {
  requiredArgs(2, arguments);
  var day = toInteger(dirtyDay);
  if (day % 7 === 0) {
    day = day - 7;
  }
  var weekStartsOn = 1;
  var date = toDate(dirtyDate);
  var currentDay = date.getUTCDay();
  var remainder = day % 7;
  var dayIndex = (remainder + 7) % 7;
  var diff = (dayIndex < weekStartsOn ? 7 : 0) + day - currentDay;
  date.setUTCDate(date.getUTCDate() + diff);
  return date;
}

// node_modules/date-fns/esm/_lib/setUTCISOWeek/index.js
function setUTCISOWeek(dirtyDate, dirtyISOWeek) {
  requiredArgs(2, arguments);
  var date = toDate(dirtyDate);
  var isoWeek = toInteger(dirtyISOWeek);
  var diff = getUTCISOWeek(date) - isoWeek;
  date.setUTCDate(date.getUTCDate() - diff * 7);
  return date;
}

// node_modules/date-fns/esm/_lib/setUTCWeek/index.js
function setUTCWeek(dirtyDate, dirtyWeek, options) {
  requiredArgs(2, arguments);
  var date = toDate(dirtyDate);
  var week = toInteger(dirtyWeek);
  var diff = getUTCWeek(date, options) - week;
  date.setUTCDate(date.getUTCDate() - diff * 7);
  return date;
}

// node_modules/date-fns/esm/parse/_lib/parsers/index.js
var MILLISECONDS_IN_HOUR = 36e5;
var MILLISECONDS_IN_MINUTE = 6e4;
var MILLISECONDS_IN_SECOND = 1e3;
var numericPatterns = {
  month: /^(1[0-2]|0?\d)/,
  // 0 to 12
  date: /^(3[0-1]|[0-2]?\d)/,
  // 0 to 31
  dayOfYear: /^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,
  // 0 to 366
  week: /^(5[0-3]|[0-4]?\d)/,
  // 0 to 53
  hour23h: /^(2[0-3]|[0-1]?\d)/,
  // 0 to 23
  hour24h: /^(2[0-4]|[0-1]?\d)/,
  // 0 to 24
  hour11h: /^(1[0-1]|0?\d)/,
  // 0 to 11
  hour12h: /^(1[0-2]|0?\d)/,
  // 0 to 12
  minute: /^[0-5]?\d/,
  // 0 to 59
  second: /^[0-5]?\d/,
  // 0 to 59
  singleDigit: /^\d/,
  // 0 to 9
  twoDigits: /^\d{1,2}/,
  // 0 to 99
  threeDigits: /^\d{1,3}/,
  // 0 to 999
  fourDigits: /^\d{1,4}/,
  // 0 to 9999
  anyDigitsSigned: /^-?\d+/,
  singleDigitSigned: /^-?\d/,
  // 0 to 9, -0 to -9
  twoDigitsSigned: /^-?\d{1,2}/,
  // 0 to 99, -0 to -99
  threeDigitsSigned: /^-?\d{1,3}/,
  // 0 to 999, -0 to -999
  fourDigitsSigned: /^-?\d{1,4}/,
  // 0 to 9999, -0 to -9999
};
var timezonePatterns = {
  basicOptionalMinutes: /^([+-])(\d{2})(\d{2})?|Z/,
  basic: /^([+-])(\d{2})(\d{2})|Z/,
  basicOptionalSeconds: /^([+-])(\d{2})(\d{2})((\d{2}))?|Z/,
  extended: /^([+-])(\d{2}):(\d{2})|Z/,
  extendedOptionalSeconds: /^([+-])(\d{2}):(\d{2})(:(\d{2}))?|Z/,
};
function parseNumericPattern(pattern, string, valueCallback) {
  var matchResult = string.match(pattern);
  if (!matchResult) {
    return null;
  }
  var value = parseInt(matchResult[0], 10);
  return {
    value: valueCallback ? valueCallback(value) : value,
    rest: string.slice(matchResult[0].length),
  };
}
function parseTimezonePattern(pattern, string) {
  var matchResult = string.match(pattern);
  if (!matchResult) {
    return null;
  }
  if (matchResult[0] === 'Z') {
    return {
      value: 0,
      rest: string.slice(1),
    };
  }
  var sign = matchResult[1] === '+' ? 1 : -1;
  var hours = matchResult[2] ? parseInt(matchResult[2], 10) : 0;
  var minutes = matchResult[3] ? parseInt(matchResult[3], 10) : 0;
  var seconds = matchResult[5] ? parseInt(matchResult[5], 10) : 0;
  return {
    value:
      sign *
      (hours * MILLISECONDS_IN_HOUR +
        minutes * MILLISECONDS_IN_MINUTE +
        seconds * MILLISECONDS_IN_SECOND),
    rest: string.slice(matchResult[0].length),
  };
}
function parseAnyDigitsSigned(string, valueCallback) {
  return parseNumericPattern(
    numericPatterns.anyDigitsSigned,
    string,
    valueCallback
  );
}
function parseNDigits(n, string, valueCallback) {
  switch (n) {
    case 1:
      return parseNumericPattern(
        numericPatterns.singleDigit,
        string,
        valueCallback
      );
    case 2:
      return parseNumericPattern(
        numericPatterns.twoDigits,
        string,
        valueCallback
      );
    case 3:
      return parseNumericPattern(
        numericPatterns.threeDigits,
        string,
        valueCallback
      );
    case 4:
      return parseNumericPattern(
        numericPatterns.fourDigits,
        string,
        valueCallback
      );
    default:
      return parseNumericPattern(
        new RegExp('^\\d{1,' + n + '}'),
        string,
        valueCallback
      );
  }
}
function parseNDigitsSigned(n, string, valueCallback) {
  switch (n) {
    case 1:
      return parseNumericPattern(
        numericPatterns.singleDigitSigned,
        string,
        valueCallback
      );
    case 2:
      return parseNumericPattern(
        numericPatterns.twoDigitsSigned,
        string,
        valueCallback
      );
    case 3:
      return parseNumericPattern(
        numericPatterns.threeDigitsSigned,
        string,
        valueCallback
      );
    case 4:
      return parseNumericPattern(
        numericPatterns.fourDigitsSigned,
        string,
        valueCallback
      );
    default:
      return parseNumericPattern(
        new RegExp('^-?\\d{1,' + n + '}'),
        string,
        valueCallback
      );
  }
}
function dayPeriodEnumToHours(enumValue) {
  switch (enumValue) {
    case 'morning':
      return 4;
    case 'evening':
      return 17;
    case 'pm':
    case 'noon':
    case 'afternoon':
      return 12;
    case 'am':
    case 'midnight':
    case 'night':
    default:
      return 0;
  }
}
function normalizeTwoDigitYear(twoDigitYear, currentYear) {
  var isCommonEra = currentYear > 0;
  var absCurrentYear = isCommonEra ? currentYear : 1 - currentYear;
  var result;
  if (absCurrentYear <= 50) {
    result = twoDigitYear || 100;
  } else {
    var rangeEnd = absCurrentYear + 50;
    var rangeEndCentury = Math.floor(rangeEnd / 100) * 100;
    var isPreviousCentury = twoDigitYear >= rangeEnd % 100;
    result = twoDigitYear + rangeEndCentury - (isPreviousCentury ? 100 : 0);
  }
  return isCommonEra ? result : 1 - result;
}
var DAYS_IN_MONTH = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
var DAYS_IN_MONTH_LEAP_YEAR = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
function isLeapYearIndex(year) {
  return year % 400 === 0 || (year % 4 === 0 && year % 100 !== 0);
}
var parsers = {
  // Era
  G: {
    priority: 140,
    parse: function (string, token, match2, _options) {
      switch (token) {
        case 'G':
        case 'GG':
        case 'GGG':
          return (
            match2.era(string, {
              width: 'abbreviated',
            }) ||
            match2.era(string, {
              width: 'narrow',
            })
          );
        case 'GGGGG':
          return match2.era(string, {
            width: 'narrow',
          });
        case 'GGGG':
        default:
          return (
            match2.era(string, {
              width: 'wide',
            }) ||
            match2.era(string, {
              width: 'abbreviated',
            }) ||
            match2.era(string, {
              width: 'narrow',
            })
          );
      }
    },
    set: function (date, flags, value, _options) {
      flags.era = value;
      date.setUTCFullYear(value, 0, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ['R', 'u', 't', 'T'],
  },
  // Year
  y: {
    // From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_Patterns
    // | Year     |     y | yy |   yyy |  yyyy | yyyyy |
    // |----------|-------|----|-------|-------|-------|
    // | AD 1     |     1 | 01 |   001 |  0001 | 00001 |
    // | AD 12    |    12 | 12 |   012 |  0012 | 00012 |
    // | AD 123   |   123 | 23 |   123 |  0123 | 00123 |
    // | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |
    // | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |
    priority: 130,
    parse: function (string, token, match2, _options) {
      var valueCallback = function (year) {
        return {
          year,
          isTwoDigitYear: token === 'yy',
        };
      };
      switch (token) {
        case 'y':
          return parseNDigits(4, string, valueCallback);
        case 'yo':
          return match2.ordinalNumber(string, {
            unit: 'year',
            valueCallback,
          });
        default:
          return parseNDigits(token.length, string, valueCallback);
      }
    },
    validate: function (_date, value, _options) {
      return value.isTwoDigitYear || value.year > 0;
    },
    set: function (date, flags, value, _options) {
      var currentYear = date.getUTCFullYear();
      if (value.isTwoDigitYear) {
        var normalizedTwoDigitYear = normalizeTwoDigitYear(
          value.year,
          currentYear
        );
        date.setUTCFullYear(normalizedTwoDigitYear, 0, 1);
        date.setUTCHours(0, 0, 0, 0);
        return date;
      }
      var year =
        !('era' in flags) || flags.era === 1 ? value.year : 1 - value.year;
      date.setUTCFullYear(year, 0, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ['Y', 'R', 'u', 'w', 'I', 'i', 'e', 'c', 't', 'T'],
  },
  // Local week-numbering year
  Y: {
    priority: 130,
    parse: function (string, token, match2, _options) {
      var valueCallback = function (year) {
        return {
          year,
          isTwoDigitYear: token === 'YY',
        };
      };
      switch (token) {
        case 'Y':
          return parseNDigits(4, string, valueCallback);
        case 'Yo':
          return match2.ordinalNumber(string, {
            unit: 'year',
            valueCallback,
          });
        default:
          return parseNDigits(token.length, string, valueCallback);
      }
    },
    validate: function (_date, value, _options) {
      return value.isTwoDigitYear || value.year > 0;
    },
    set: function (date, flags, value, options) {
      var currentYear = getUTCWeekYear(date, options);
      if (value.isTwoDigitYear) {
        var normalizedTwoDigitYear = normalizeTwoDigitYear(
          value.year,
          currentYear
        );
        date.setUTCFullYear(
          normalizedTwoDigitYear,
          0,
          options.firstWeekContainsDate
        );
        date.setUTCHours(0, 0, 0, 0);
        return startOfUTCWeek(date, options);
      }
      var year =
        !('era' in flags) || flags.era === 1 ? value.year : 1 - value.year;
      date.setUTCFullYear(year, 0, options.firstWeekContainsDate);
      date.setUTCHours(0, 0, 0, 0);
      return startOfUTCWeek(date, options);
    },
    incompatibleTokens: [
      'y',
      'R',
      'u',
      'Q',
      'q',
      'M',
      'L',
      'I',
      'd',
      'D',
      'i',
      't',
      'T',
    ],
  },
  // ISO week-numbering year
  R: {
    priority: 130,
    parse: function (string, token, _match, _options) {
      if (token === 'R') {
        return parseNDigitsSigned(4, string);
      }
      return parseNDigitsSigned(token.length, string);
    },
    set: function (_date, _flags, value, _options) {
      var firstWeekOfYear = new Date(0);
      firstWeekOfYear.setUTCFullYear(value, 0, 4);
      firstWeekOfYear.setUTCHours(0, 0, 0, 0);
      return startOfUTCISOWeek(firstWeekOfYear);
    },
    incompatibleTokens: [
      'G',
      'y',
      'Y',
      'u',
      'Q',
      'q',
      'M',
      'L',
      'w',
      'd',
      'D',
      'e',
      'c',
      't',
      'T',
    ],
  },
  // Extended year
  u: {
    priority: 130,
    parse: function (string, token, _match, _options) {
      if (token === 'u') {
        return parseNDigitsSigned(4, string);
      }
      return parseNDigitsSigned(token.length, string);
    },
    set: function (date, _flags, value, _options) {
      date.setUTCFullYear(value, 0, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ['G', 'y', 'Y', 'R', 'w', 'I', 'i', 'e', 'c', 't', 'T'],
  },
  // Quarter
  Q: {
    priority: 120,
    parse: function (string, token, match2, _options) {
      switch (token) {
        case 'Q':
        case 'QQ':
          return parseNDigits(token.length, string);
        case 'Qo':
          return match2.ordinalNumber(string, {
            unit: 'quarter',
          });
        case 'QQQ':
          return (
            match2.quarter(string, {
              width: 'abbreviated',
              context: 'formatting',
            }) ||
            match2.quarter(string, {
              width: 'narrow',
              context: 'formatting',
            })
          );
        case 'QQQQQ':
          return match2.quarter(string, {
            width: 'narrow',
            context: 'formatting',
          });
        case 'QQQQ':
        default:
          return (
            match2.quarter(string, {
              width: 'wide',
              context: 'formatting',
            }) ||
            match2.quarter(string, {
              width: 'abbreviated',
              context: 'formatting',
            }) ||
            match2.quarter(string, {
              width: 'narrow',
              context: 'formatting',
            })
          );
      }
    },
    validate: function (_date, value, _options) {
      return value >= 1 && value <= 4;
    },
    set: function (date, _flags, value, _options) {
      date.setUTCMonth((value - 1) * 3, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: [
      'Y',
      'R',
      'q',
      'M',
      'L',
      'w',
      'I',
      'd',
      'D',
      'i',
      'e',
      'c',
      't',
      'T',
    ],
  },
  // Stand-alone quarter
  q: {
    priority: 120,
    parse: function (string, token, match2, _options) {
      switch (token) {
        case 'q':
        case 'qq':
          return parseNDigits(token.length, string);
        case 'qo':
          return match2.ordinalNumber(string, {
            unit: 'quarter',
          });
        case 'qqq':
          return (
            match2.quarter(string, {
              width: 'abbreviated',
              context: 'standalone',
            }) ||
            match2.quarter(string, {
              width: 'narrow',
              context: 'standalone',
            })
          );
        case 'qqqqq':
          return match2.quarter(string, {
            width: 'narrow',
            context: 'standalone',
          });
        case 'qqqq':
        default:
          return (
            match2.quarter(string, {
              width: 'wide',
              context: 'standalone',
            }) ||
            match2.quarter(string, {
              width: 'abbreviated',
              context: 'standalone',
            }) ||
            match2.quarter(string, {
              width: 'narrow',
              context: 'standalone',
            })
          );
      }
    },
    validate: function (_date, value, _options) {
      return value >= 1 && value <= 4;
    },
    set: function (date, _flags, value, _options) {
      date.setUTCMonth((value - 1) * 3, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: [
      'Y',
      'R',
      'Q',
      'M',
      'L',
      'w',
      'I',
      'd',
      'D',
      'i',
      'e',
      'c',
      't',
      'T',
    ],
  },
  // Month
  M: {
    priority: 110,
    parse: function (string, token, match2, _options) {
      var valueCallback = function (value) {
        return value - 1;
      };
      switch (token) {
        case 'M':
          return parseNumericPattern(
            numericPatterns.month,
            string,
            valueCallback
          );
        case 'MM':
          return parseNDigits(2, string, valueCallback);
        case 'Mo':
          return match2.ordinalNumber(string, {
            unit: 'month',
            valueCallback,
          });
        case 'MMM':
          return (
            match2.month(string, {
              width: 'abbreviated',
              context: 'formatting',
            }) ||
            match2.month(string, {
              width: 'narrow',
              context: 'formatting',
            })
          );
        case 'MMMMM':
          return match2.month(string, {
            width: 'narrow',
            context: 'formatting',
          });
        case 'MMMM':
        default:
          return (
            match2.month(string, {
              width: 'wide',
              context: 'formatting',
            }) ||
            match2.month(string, {
              width: 'abbreviated',
              context: 'formatting',
            }) ||
            match2.month(string, {
              width: 'narrow',
              context: 'formatting',
            })
          );
      }
    },
    validate: function (_date, value, _options) {
      return value >= 0 && value <= 11;
    },
    set: function (date, _flags, value, _options) {
      date.setUTCMonth(value, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: [
      'Y',
      'R',
      'q',
      'Q',
      'L',
      'w',
      'I',
      'D',
      'i',
      'e',
      'c',
      't',
      'T',
    ],
  },
  // Stand-alone month
  L: {
    priority: 110,
    parse: function (string, token, match2, _options) {
      var valueCallback = function (value) {
        return value - 1;
      };
      switch (token) {
        case 'L':
          return parseNumericPattern(
            numericPatterns.month,
            string,
            valueCallback
          );
        case 'LL':
          return parseNDigits(2, string, valueCallback);
        case 'Lo':
          return match2.ordinalNumber(string, {
            unit: 'month',
            valueCallback,
          });
        case 'LLL':
          return (
            match2.month(string, {
              width: 'abbreviated',
              context: 'standalone',
            }) ||
            match2.month(string, {
              width: 'narrow',
              context: 'standalone',
            })
          );
        case 'LLLLL':
          return match2.month(string, {
            width: 'narrow',
            context: 'standalone',
          });
        case 'LLLL':
        default:
          return (
            match2.month(string, {
              width: 'wide',
              context: 'standalone',
            }) ||
            match2.month(string, {
              width: 'abbreviated',
              context: 'standalone',
            }) ||
            match2.month(string, {
              width: 'narrow',
              context: 'standalone',
            })
          );
      }
    },
    validate: function (_date, value, _options) {
      return value >= 0 && value <= 11;
    },
    set: function (date, _flags, value, _options) {
      date.setUTCMonth(value, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: [
      'Y',
      'R',
      'q',
      'Q',
      'M',
      'w',
      'I',
      'D',
      'i',
      'e',
      'c',
      't',
      'T',
    ],
  },
  // Local week of year
  w: {
    priority: 100,
    parse: function (string, token, match2, _options) {
      switch (token) {
        case 'w':
          return parseNumericPattern(numericPatterns.week, string);
        case 'wo':
          return match2.ordinalNumber(string, {
            unit: 'week',
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function (_date, value, _options) {
      return value >= 1 && value <= 53;
    },
    set: function (date, _flags, value, options) {
      return startOfUTCWeek(setUTCWeek(date, value, options), options);
    },
    incompatibleTokens: [
      'y',
      'R',
      'u',
      'q',
      'Q',
      'M',
      'L',
      'I',
      'd',
      'D',
      'i',
      't',
      'T',
    ],
  },
  // ISO week of year
  I: {
    priority: 100,
    parse: function (string, token, match2, _options) {
      switch (token) {
        case 'I':
          return parseNumericPattern(numericPatterns.week, string);
        case 'Io':
          return match2.ordinalNumber(string, {
            unit: 'week',
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function (_date, value, _options) {
      return value >= 1 && value <= 53;
    },
    set: function (date, _flags, value, options) {
      return startOfUTCISOWeek(setUTCISOWeek(date, value, options), options);
    },
    incompatibleTokens: [
      'y',
      'Y',
      'u',
      'q',
      'Q',
      'M',
      'L',
      'w',
      'd',
      'D',
      'e',
      'c',
      't',
      'T',
    ],
  },
  // Day of the month
  d: {
    priority: 90,
    subPriority: 1,
    parse: function (string, token, match2, _options) {
      switch (token) {
        case 'd':
          return parseNumericPattern(numericPatterns.date, string);
        case 'do':
          return match2.ordinalNumber(string, {
            unit: 'date',
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function (date, value, _options) {
      var year = date.getUTCFullYear();
      var isLeapYear = isLeapYearIndex(year);
      var month = date.getUTCMonth();
      if (isLeapYear) {
        return value >= 1 && value <= DAYS_IN_MONTH_LEAP_YEAR[month];
      } else {
        return value >= 1 && value <= DAYS_IN_MONTH[month];
      }
    },
    set: function (date, _flags, value, _options) {
      date.setUTCDate(value);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: [
      'Y',
      'R',
      'q',
      'Q',
      'w',
      'I',
      'D',
      'i',
      'e',
      'c',
      't',
      'T',
    ],
  },
  // Day of year
  D: {
    priority: 90,
    subPriority: 1,
    parse: function (string, token, match2, _options) {
      switch (token) {
        case 'D':
        case 'DD':
          return parseNumericPattern(numericPatterns.dayOfYear, string);
        case 'Do':
          return match2.ordinalNumber(string, {
            unit: 'date',
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function (date, value, _options) {
      var year = date.getUTCFullYear();
      var isLeapYear = isLeapYearIndex(year);
      if (isLeapYear) {
        return value >= 1 && value <= 366;
      } else {
        return value >= 1 && value <= 365;
      }
    },
    set: function (date, _flags, value, _options) {
      date.setUTCMonth(0, value);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: [
      'Y',
      'R',
      'q',
      'Q',
      'M',
      'L',
      'w',
      'I',
      'd',
      'E',
      'i',
      'e',
      'c',
      't',
      'T',
    ],
  },
  // Day of week
  E: {
    priority: 90,
    parse: function (string, token, match2, _options) {
      switch (token) {
        case 'E':
        case 'EE':
        case 'EEE':
          return (
            match2.day(string, {
              width: 'abbreviated',
              context: 'formatting',
            }) ||
            match2.day(string, {
              width: 'short',
              context: 'formatting',
            }) ||
            match2.day(string, {
              width: 'narrow',
              context: 'formatting',
            })
          );
        case 'EEEEE':
          return match2.day(string, {
            width: 'narrow',
            context: 'formatting',
          });
        case 'EEEEEE':
          return (
            match2.day(string, {
              width: 'short',
              context: 'formatting',
            }) ||
            match2.day(string, {
              width: 'narrow',
              context: 'formatting',
            })
          );
        case 'EEEE':
        default:
          return (
            match2.day(string, {
              width: 'wide',
              context: 'formatting',
            }) ||
            match2.day(string, {
              width: 'abbreviated',
              context: 'formatting',
            }) ||
            match2.day(string, {
              width: 'short',
              context: 'formatting',
            }) ||
            match2.day(string, {
              width: 'narrow',
              context: 'formatting',
            })
          );
      }
    },
    validate: function (_date, value, _options) {
      return value >= 0 && value <= 6;
    },
    set: function (date, _flags, value, options) {
      date = setUTCDay(date, value, options);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ['D', 'i', 'e', 'c', 't', 'T'],
  },
  // Local day of week
  e: {
    priority: 90,
    parse: function (string, token, match2, options) {
      var valueCallback = function (value) {
        var wholeWeekDays = Math.floor((value - 1) / 7) * 7;
        return ((value + options.weekStartsOn + 6) % 7) + wholeWeekDays;
      };
      switch (token) {
        case 'e':
        case 'ee':
          return parseNDigits(token.length, string, valueCallback);
        case 'eo':
          return match2.ordinalNumber(string, {
            unit: 'day',
            valueCallback,
          });
        case 'eee':
          return (
            match2.day(string, {
              width: 'abbreviated',
              context: 'formatting',
            }) ||
            match2.day(string, {
              width: 'short',
              context: 'formatting',
            }) ||
            match2.day(string, {
              width: 'narrow',
              context: 'formatting',
            })
          );
        case 'eeeee':
          return match2.day(string, {
            width: 'narrow',
            context: 'formatting',
          });
        case 'eeeeee':
          return (
            match2.day(string, {
              width: 'short',
              context: 'formatting',
            }) ||
            match2.day(string, {
              width: 'narrow',
              context: 'formatting',
            })
          );
        case 'eeee':
        default:
          return (
            match2.day(string, {
              width: 'wide',
              context: 'formatting',
            }) ||
            match2.day(string, {
              width: 'abbreviated',
              context: 'formatting',
            }) ||
            match2.day(string, {
              width: 'short',
              context: 'formatting',
            }) ||
            match2.day(string, {
              width: 'narrow',
              context: 'formatting',
            })
          );
      }
    },
    validate: function (_date, value, _options) {
      return value >= 0 && value <= 6;
    },
    set: function (date, _flags, value, options) {
      date = setUTCDay(date, value, options);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: [
      'y',
      'R',
      'u',
      'q',
      'Q',
      'M',
      'L',
      'I',
      'd',
      'D',
      'E',
      'i',
      'c',
      't',
      'T',
    ],
  },
  // Stand-alone local day of week
  c: {
    priority: 90,
    parse: function (string, token, match2, options) {
      var valueCallback = function (value) {
        var wholeWeekDays = Math.floor((value - 1) / 7) * 7;
        return ((value + options.weekStartsOn + 6) % 7) + wholeWeekDays;
      };
      switch (token) {
        case 'c':
        case 'cc':
          return parseNDigits(token.length, string, valueCallback);
        case 'co':
          return match2.ordinalNumber(string, {
            unit: 'day',
            valueCallback,
          });
        case 'ccc':
          return (
            match2.day(string, {
              width: 'abbreviated',
              context: 'standalone',
            }) ||
            match2.day(string, {
              width: 'short',
              context: 'standalone',
            }) ||
            match2.day(string, {
              width: 'narrow',
              context: 'standalone',
            })
          );
        case 'ccccc':
          return match2.day(string, {
            width: 'narrow',
            context: 'standalone',
          });
        case 'cccccc':
          return (
            match2.day(string, {
              width: 'short',
              context: 'standalone',
            }) ||
            match2.day(string, {
              width: 'narrow',
              context: 'standalone',
            })
          );
        case 'cccc':
        default:
          return (
            match2.day(string, {
              width: 'wide',
              context: 'standalone',
            }) ||
            match2.day(string, {
              width: 'abbreviated',
              context: 'standalone',
            }) ||
            match2.day(string, {
              width: 'short',
              context: 'standalone',
            }) ||
            match2.day(string, {
              width: 'narrow',
              context: 'standalone',
            })
          );
      }
    },
    validate: function (_date, value, _options) {
      return value >= 0 && value <= 6;
    },
    set: function (date, _flags, value, options) {
      date = setUTCDay(date, value, options);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: [
      'y',
      'R',
      'u',
      'q',
      'Q',
      'M',
      'L',
      'I',
      'd',
      'D',
      'E',
      'i',
      'e',
      't',
      'T',
    ],
  },
  // ISO day of week
  i: {
    priority: 90,
    parse: function (string, token, match2, _options) {
      var valueCallback = function (value) {
        if (value === 0) {
          return 7;
        }
        return value;
      };
      switch (token) {
        case 'i':
        case 'ii':
          return parseNDigits(token.length, string);
        case 'io':
          return match2.ordinalNumber(string, {
            unit: 'day',
          });
        case 'iii':
          return (
            match2.day(string, {
              width: 'abbreviated',
              context: 'formatting',
              valueCallback,
            }) ||
            match2.day(string, {
              width: 'short',
              context: 'formatting',
              valueCallback,
            }) ||
            match2.day(string, {
              width: 'narrow',
              context: 'formatting',
              valueCallback,
            })
          );
        case 'iiiii':
          return match2.day(string, {
            width: 'narrow',
            context: 'formatting',
            valueCallback,
          });
        case 'iiiiii':
          return (
            match2.day(string, {
              width: 'short',
              context: 'formatting',
              valueCallback,
            }) ||
            match2.day(string, {
              width: 'narrow',
              context: 'formatting',
              valueCallback,
            })
          );
        case 'iiii':
        default:
          return (
            match2.day(string, {
              width: 'wide',
              context: 'formatting',
              valueCallback,
            }) ||
            match2.day(string, {
              width: 'abbreviated',
              context: 'formatting',
              valueCallback,
            }) ||
            match2.day(string, {
              width: 'short',
              context: 'formatting',
              valueCallback,
            }) ||
            match2.day(string, {
              width: 'narrow',
              context: 'formatting',
              valueCallback,
            })
          );
      }
    },
    validate: function (_date, value, _options) {
      return value >= 1 && value <= 7;
    },
    set: function (date, _flags, value, options) {
      date = setUTCISODay(date, value, options);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: [
      'y',
      'Y',
      'u',
      'q',
      'Q',
      'M',
      'L',
      'w',
      'd',
      'D',
      'E',
      'e',
      'c',
      't',
      'T',
    ],
  },
  // AM or PM
  a: {
    priority: 80,
    parse: function (string, token, match2, _options) {
      switch (token) {
        case 'a':
        case 'aa':
        case 'aaa':
          return (
            match2.dayPeriod(string, {
              width: 'abbreviated',
              context: 'formatting',
            }) ||
            match2.dayPeriod(string, {
              width: 'narrow',
              context: 'formatting',
            })
          );
        case 'aaaaa':
          return match2.dayPeriod(string, {
            width: 'narrow',
            context: 'formatting',
          });
        case 'aaaa':
        default:
          return (
            match2.dayPeriod(string, {
              width: 'wide',
              context: 'formatting',
            }) ||
            match2.dayPeriod(string, {
              width: 'abbreviated',
              context: 'formatting',
            }) ||
            match2.dayPeriod(string, {
              width: 'narrow',
              context: 'formatting',
            })
          );
      }
    },
    set: function (date, _flags, value, _options) {
      date.setUTCHours(dayPeriodEnumToHours(value), 0, 0, 0);
      return date;
    },
    incompatibleTokens: ['b', 'B', 'H', 'K', 'k', 't', 'T'],
  },
  // AM, PM, midnight
  b: {
    priority: 80,
    parse: function (string, token, match2, _options) {
      switch (token) {
        case 'b':
        case 'bb':
        case 'bbb':
          return (
            match2.dayPeriod(string, {
              width: 'abbreviated',
              context: 'formatting',
            }) ||
            match2.dayPeriod(string, {
              width: 'narrow',
              context: 'formatting',
            })
          );
        case 'bbbbb':
          return match2.dayPeriod(string, {
            width: 'narrow',
            context: 'formatting',
          });
        case 'bbbb':
        default:
          return (
            match2.dayPeriod(string, {
              width: 'wide',
              context: 'formatting',
            }) ||
            match2.dayPeriod(string, {
              width: 'abbreviated',
              context: 'formatting',
            }) ||
            match2.dayPeriod(string, {
              width: 'narrow',
              context: 'formatting',
            })
          );
      }
    },
    set: function (date, _flags, value, _options) {
      date.setUTCHours(dayPeriodEnumToHours(value), 0, 0, 0);
      return date;
    },
    incompatibleTokens: ['a', 'B', 'H', 'K', 'k', 't', 'T'],
  },
  // in the morning, in the afternoon, in the evening, at night
  B: {
    priority: 80,
    parse: function (string, token, match2, _options) {
      switch (token) {
        case 'B':
        case 'BB':
        case 'BBB':
          return (
            match2.dayPeriod(string, {
              width: 'abbreviated',
              context: 'formatting',
            }) ||
            match2.dayPeriod(string, {
              width: 'narrow',
              context: 'formatting',
            })
          );
        case 'BBBBB':
          return match2.dayPeriod(string, {
            width: 'narrow',
            context: 'formatting',
          });
        case 'BBBB':
        default:
          return (
            match2.dayPeriod(string, {
              width: 'wide',
              context: 'formatting',
            }) ||
            match2.dayPeriod(string, {
              width: 'abbreviated',
              context: 'formatting',
            }) ||
            match2.dayPeriod(string, {
              width: 'narrow',
              context: 'formatting',
            })
          );
      }
    },
    set: function (date, _flags, value, _options) {
      date.setUTCHours(dayPeriodEnumToHours(value), 0, 0, 0);
      return date;
    },
    incompatibleTokens: ['a', 'b', 't', 'T'],
  },
  // Hour [1-12]
  h: {
    priority: 70,
    parse: function (string, token, match2, _options) {
      switch (token) {
        case 'h':
          return parseNumericPattern(numericPatterns.hour12h, string);
        case 'ho':
          return match2.ordinalNumber(string, {
            unit: 'hour',
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function (_date, value, _options) {
      return value >= 1 && value <= 12;
    },
    set: function (date, _flags, value, _options) {
      var isPM = date.getUTCHours() >= 12;
      if (isPM && value < 12) {
        date.setUTCHours(value + 12, 0, 0, 0);
      } else if (!isPM && value === 12) {
        date.setUTCHours(0, 0, 0, 0);
      } else {
        date.setUTCHours(value, 0, 0, 0);
      }
      return date;
    },
    incompatibleTokens: ['H', 'K', 'k', 't', 'T'],
  },
  // Hour [0-23]
  H: {
    priority: 70,
    parse: function (string, token, match2, _options) {
      switch (token) {
        case 'H':
          return parseNumericPattern(numericPatterns.hour23h, string);
        case 'Ho':
          return match2.ordinalNumber(string, {
            unit: 'hour',
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function (_date, value, _options) {
      return value >= 0 && value <= 23;
    },
    set: function (date, _flags, value, _options) {
      date.setUTCHours(value, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ['a', 'b', 'h', 'K', 'k', 't', 'T'],
  },
  // Hour [0-11]
  K: {
    priority: 70,
    parse: function (string, token, match2, _options) {
      switch (token) {
        case 'K':
          return parseNumericPattern(numericPatterns.hour11h, string);
        case 'Ko':
          return match2.ordinalNumber(string, {
            unit: 'hour',
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function (_date, value, _options) {
      return value >= 0 && value <= 11;
    },
    set: function (date, _flags, value, _options) {
      var isPM = date.getUTCHours() >= 12;
      if (isPM && value < 12) {
        date.setUTCHours(value + 12, 0, 0, 0);
      } else {
        date.setUTCHours(value, 0, 0, 0);
      }
      return date;
    },
    incompatibleTokens: ['a', 'b', 'h', 'H', 'k', 't', 'T'],
  },
  // Hour [1-24]
  k: {
    priority: 70,
    parse: function (string, token, match2, _options) {
      switch (token) {
        case 'k':
          return parseNumericPattern(numericPatterns.hour24h, string);
        case 'ko':
          return match2.ordinalNumber(string, {
            unit: 'hour',
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function (_date, value, _options) {
      return value >= 1 && value <= 24;
    },
    set: function (date, _flags, value, _options) {
      var hours = value <= 24 ? value % 24 : value;
      date.setUTCHours(hours, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ['a', 'b', 'h', 'H', 'K', 't', 'T'],
  },
  // Minute
  m: {
    priority: 60,
    parse: function (string, token, match2, _options) {
      switch (token) {
        case 'm':
          return parseNumericPattern(numericPatterns.minute, string);
        case 'mo':
          return match2.ordinalNumber(string, {
            unit: 'minute',
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function (_date, value, _options) {
      return value >= 0 && value <= 59;
    },
    set: function (date, _flags, value, _options) {
      date.setUTCMinutes(value, 0, 0);
      return date;
    },
    incompatibleTokens: ['t', 'T'],
  },
  // Second
  s: {
    priority: 50,
    parse: function (string, token, match2, _options) {
      switch (token) {
        case 's':
          return parseNumericPattern(numericPatterns.second, string);
        case 'so':
          return match2.ordinalNumber(string, {
            unit: 'second',
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function (_date, value, _options) {
      return value >= 0 && value <= 59;
    },
    set: function (date, _flags, value, _options) {
      date.setUTCSeconds(value, 0);
      return date;
    },
    incompatibleTokens: ['t', 'T'],
  },
  // Fraction of second
  S: {
    priority: 30,
    parse: function (string, token, _match, _options) {
      var valueCallback = function (value) {
        return Math.floor(value * Math.pow(10, -token.length + 3));
      };
      return parseNDigits(token.length, string, valueCallback);
    },
    set: function (date, _flags, value, _options) {
      date.setUTCMilliseconds(value);
      return date;
    },
    incompatibleTokens: ['t', 'T'],
  },
  // Timezone (ISO-8601. +00:00 is `'Z'`)
  X: {
    priority: 10,
    parse: function (string, token, _match, _options) {
      switch (token) {
        case 'X':
          return parseTimezonePattern(
            timezonePatterns.basicOptionalMinutes,
            string
          );
        case 'XX':
          return parseTimezonePattern(timezonePatterns.basic, string);
        case 'XXXX':
          return parseTimezonePattern(
            timezonePatterns.basicOptionalSeconds,
            string
          );
        case 'XXXXX':
          return parseTimezonePattern(
            timezonePatterns.extendedOptionalSeconds,
            string
          );
        case 'XXX':
        default:
          return parseTimezonePattern(timezonePatterns.extended, string);
      }
    },
    set: function (date, flags, value, _options) {
      if (flags.timestampIsSet) {
        return date;
      }
      return new Date(date.getTime() - value);
    },
    incompatibleTokens: ['t', 'T', 'x'],
  },
  // Timezone (ISO-8601)
  x: {
    priority: 10,
    parse: function (string, token, _match, _options) {
      switch (token) {
        case 'x':
          return parseTimezonePattern(
            timezonePatterns.basicOptionalMinutes,
            string
          );
        case 'xx':
          return parseTimezonePattern(timezonePatterns.basic, string);
        case 'xxxx':
          return parseTimezonePattern(
            timezonePatterns.basicOptionalSeconds,
            string
          );
        case 'xxxxx':
          return parseTimezonePattern(
            timezonePatterns.extendedOptionalSeconds,
            string
          );
        case 'xxx':
        default:
          return parseTimezonePattern(timezonePatterns.extended, string);
      }
    },
    set: function (date, flags, value, _options) {
      if (flags.timestampIsSet) {
        return date;
      }
      return new Date(date.getTime() - value);
    },
    incompatibleTokens: ['t', 'T', 'X'],
  },
  // Seconds timestamp
  t: {
    priority: 40,
    parse: function (string, _token, _match, _options) {
      return parseAnyDigitsSigned(string);
    },
    set: function (_date, _flags, value, _options) {
      return [
        new Date(value * 1e3),
        {
          timestampIsSet: true,
        },
      ];
    },
    incompatibleTokens: '*',
  },
  // Milliseconds timestamp
  T: {
    priority: 20,
    parse: function (string, _token, _match, _options) {
      return parseAnyDigitsSigned(string);
    },
    set: function (_date, _flags, value, _options) {
      return [
        new Date(value),
        {
          timestampIsSet: true,
        },
      ];
    },
    incompatibleTokens: '*',
  },
};
var parsers_default = parsers;

// node_modules/date-fns/esm/parse/index.js
var TIMEZONE_UNIT_PRIORITY = 10;
var formattingTokensRegExp2 =
  /[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g;
var longFormattingTokensRegExp2 = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;
var escapedStringRegExp2 = /^'([^]*?)'?$/;
var doubleQuoteRegExp2 = /''/g;
var notWhitespaceRegExp = /\S/;
var unescapedLatinCharacterRegExp2 = /[a-zA-Z]/;
function parse(
  dirtyDateString,
  dirtyFormatString,
  dirtyReferenceDate,
  dirtyOptions
) {
  requiredArgs(3, arguments);
  var dateString = String(dirtyDateString);
  var formatString = String(dirtyFormatString);
  var options = dirtyOptions || {};
  var locale2 = options.locale || en_US_default;
  if (!locale2.match) {
    throw new RangeError('locale must contain match property');
  }
  var localeFirstWeekContainsDate =
    locale2.options && locale2.options.firstWeekContainsDate;
  var defaultFirstWeekContainsDate =
    localeFirstWeekContainsDate == null
      ? 1
      : toInteger(localeFirstWeekContainsDate);
  var firstWeekContainsDate =
    options.firstWeekContainsDate == null
      ? defaultFirstWeekContainsDate
      : toInteger(options.firstWeekContainsDate);
  if (!(firstWeekContainsDate >= 1 && firstWeekContainsDate <= 7)) {
    throw new RangeError(
      'firstWeekContainsDate must be between 1 and 7 inclusively'
    );
  }
  var localeWeekStartsOn = locale2.options && locale2.options.weekStartsOn;
  var defaultWeekStartsOn =
    localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);
  var weekStartsOn =
    options.weekStartsOn == null
      ? defaultWeekStartsOn
      : toInteger(options.weekStartsOn);
  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {
    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');
  }
  if (formatString === '') {
    if (dateString === '') {
      return toDate(dirtyReferenceDate);
    } else {
      return new Date(NaN);
    }
  }
  var subFnOptions = {
    firstWeekContainsDate,
    weekStartsOn,
    locale: locale2,
    // If timezone isn't specified, it will be set to the system timezone
  };
  var setters = [
    {
      priority: TIMEZONE_UNIT_PRIORITY,
      subPriority: -1,
      set: dateToSystemTimezone,
      index: 0,
    },
  ];
  var i;
  var tokens = formatString
    .match(longFormattingTokensRegExp2)
    .map(function (substring) {
      var firstCharacter2 = substring[0];
      if (firstCharacter2 === 'p' || firstCharacter2 === 'P') {
        var longFormatter = longFormatters_default[firstCharacter2];
        return longFormatter(substring, locale2.formatLong, subFnOptions);
      }
      return substring;
    })
    .join('')
    .match(formattingTokensRegExp2);
  var usedTokens = [];
  for (i = 0; i < tokens.length; i++) {
    var token = tokens[i];
    if (
      !options.useAdditionalWeekYearTokens &&
      isProtectedWeekYearToken(token)
    ) {
      throwProtectedError(token, formatString, dirtyDateString);
    }
    if (
      !options.useAdditionalDayOfYearTokens &&
      isProtectedDayOfYearToken(token)
    ) {
      throwProtectedError(token, formatString, dirtyDateString);
    }
    var firstCharacter = token[0];
    var parser = parsers_default[firstCharacter];
    if (parser) {
      var incompatibleTokens = parser.incompatibleTokens;
      if (Array.isArray(incompatibleTokens)) {
        var incompatibleToken = void 0;
        for (var _i = 0; _i < usedTokens.length; _i++) {
          var usedToken = usedTokens[_i].token;
          if (
            incompatibleTokens.indexOf(usedToken) !== -1 ||
            usedToken === firstCharacter
          ) {
            incompatibleToken = usedTokens[_i];
            break;
          }
        }
        if (incompatibleToken) {
          throw new RangeError(
            "The format string mustn't contain `"
              .concat(incompatibleToken.fullToken, '` and `')
              .concat(token, '` at the same time')
          );
        }
      } else if (parser.incompatibleTokens === '*' && usedTokens.length) {
        throw new RangeError(
          "The format string mustn't contain `".concat(
            token,
            '` and any other token at the same time'
          )
        );
      }
      usedTokens.push({
        token: firstCharacter,
        fullToken: token,
      });
      var parseResult = parser.parse(
        dateString,
        token,
        locale2.match,
        subFnOptions
      );
      if (!parseResult) {
        return new Date(NaN);
      }
      setters.push({
        priority: parser.priority,
        subPriority: parser.subPriority || 0,
        set: parser.set,
        validate: parser.validate,
        value: parseResult.value,
        index: setters.length,
      });
      dateString = parseResult.rest;
    } else {
      if (firstCharacter.match(unescapedLatinCharacterRegExp2)) {
        throw new RangeError(
          'Format string contains an unescaped latin alphabet character `' +
            firstCharacter +
            '`'
        );
      }
      if (token === "''") {
        token = "'";
      } else if (firstCharacter === "'") {
        token = cleanEscapedString2(token);
      }
      if (dateString.indexOf(token) === 0) {
        dateString = dateString.slice(token.length);
      } else {
        return new Date(NaN);
      }
    }
  }
  if (dateString.length > 0 && notWhitespaceRegExp.test(dateString)) {
    return new Date(NaN);
  }
  var uniquePrioritySetters = setters
    .map(function (setter2) {
      return setter2.priority;
    })
    .sort(function (a, b) {
      return b - a;
    })
    .filter(function (priority, index, array) {
      return array.indexOf(priority) === index;
    })
    .map(function (priority) {
      return setters
        .filter(function (setter2) {
          return setter2.priority === priority;
        })
        .sort(function (a, b) {
          return b.subPriority - a.subPriority;
        });
    })
    .map(function (setterArray) {
      return setterArray[0];
    });
  var date = toDate(dirtyReferenceDate);
  if (isNaN(date)) {
    return new Date(NaN);
  }
  var utcDate = subMilliseconds(date, getTimezoneOffsetInMilliseconds(date));
  var flags = {};
  for (i = 0; i < uniquePrioritySetters.length; i++) {
    var setter = uniquePrioritySetters[i];
    if (
      setter.validate &&
      !setter.validate(utcDate, setter.value, subFnOptions)
    ) {
      return new Date(NaN);
    }
    var result = setter.set(utcDate, flags, setter.value, subFnOptions);
    if (result[0]) {
      utcDate = result[0];
      assign(flags, result[1]);
    } else {
      utcDate = result;
    }
  }
  return utcDate;
}
function dateToSystemTimezone(date, flags) {
  if (flags.timestampIsSet) {
    return date;
  }
  var convertedDate = new Date(0);
  convertedDate.setFullYear(
    date.getUTCFullYear(),
    date.getUTCMonth(),
    date.getUTCDate()
  );
  convertedDate.setHours(
    date.getUTCHours(),
    date.getUTCMinutes(),
    date.getUTCSeconds(),
    date.getUTCMilliseconds()
  );
  return convertedDate;
}
function cleanEscapedString2(input) {
  return input.match(escapedStringRegExp2)[1].replace(doubleQuoteRegExp2, "'");
}

// src/Settings.ts
var import_obsidian5 = require('obsidian');

// src/suggesters/FolderSuggester.ts
var import_obsidian2 = require('obsidian');

// src/suggesters/suggest.ts
var import_obsidian = require('obsidian');

// node_modules/@popperjs/core/lib/enums.js
var top = 'top';
var bottom = 'bottom';
var right = 'right';
var left = 'left';
var auto = 'auto';
var basePlacements = [top, bottom, right, left];
var start = 'start';
var end = 'end';
var clippingParents = 'clippingParents';
var viewport = 'viewport';
var popper = 'popper';
var reference = 'reference';
var variationPlacements = /* @__PURE__ */ basePlacements.reduce(function (
  acc,
  placement
) {
  return acc.concat([placement + '-' + start, placement + '-' + end]);
}, []);
var placements = /* @__PURE__ */ []
  .concat(basePlacements, [auto])
  .reduce(function (acc, placement) {
    return acc.concat([
      placement,
      placement + '-' + start,
      placement + '-' + end,
    ]);
  }, []);
var beforeRead = 'beforeRead';
var read = 'read';
var afterRead = 'afterRead';
var beforeMain = 'beforeMain';
var main = 'main';
var afterMain = 'afterMain';
var beforeWrite = 'beforeWrite';
var write = 'write';
var afterWrite = 'afterWrite';
var modifierPhases = [
  beforeRead,
  read,
  afterRead,
  beforeMain,
  main,
  afterMain,
  beforeWrite,
  write,
  afterWrite,
];

// node_modules/@popperjs/core/lib/dom-utils/getNodeName.js
function getNodeName(element) {
  return element ? (element.nodeName || '').toLowerCase() : null;
}

// node_modules/@popperjs/core/lib/dom-utils/getWindow.js
function getWindow(node) {
  if (node == null) {
    return window;
  }
  if (node.toString() !== '[object Window]') {
    var ownerDocument = node.ownerDocument;
    return ownerDocument ? ownerDocument.defaultView || window : window;
  }
  return node;
}

// node_modules/@popperjs/core/lib/dom-utils/instanceOf.js
function isElement(node) {
  var OwnElement = getWindow(node).Element;
  return node instanceof OwnElement || node instanceof Element;
}
function isHTMLElement(node) {
  var OwnElement = getWindow(node).HTMLElement;
  return node instanceof OwnElement || node instanceof HTMLElement;
}
function isShadowRoot(node) {
  if (typeof ShadowRoot === 'undefined') {
    return false;
  }
  var OwnElement = getWindow(node).ShadowRoot;
  return node instanceof OwnElement || node instanceof ShadowRoot;
}

// node_modules/@popperjs/core/lib/modifiers/applyStyles.js
function applyStyles(_ref) {
  var state = _ref.state;
  Object.keys(state.elements).forEach(function (name) {
    var style = state.styles[name] || {};
    var attributes = state.attributes[name] || {};
    var element = state.elements[name];
    if (!isHTMLElement(element) || !getNodeName(element)) {
      return;
    }
    Object.assign(element.style, style);
    Object.keys(attributes).forEach(function (name2) {
      var value = attributes[name2];
      if (value === false) {
        element.removeAttribute(name2);
      } else {
        element.setAttribute(name2, value === true ? '' : value);
      }
    });
  });
}
function effect(_ref2) {
  var state = _ref2.state;
  var initialStyles = {
    popper: {
      position: state.options.strategy,
      left: '0',
      top: '0',
      margin: '0',
    },
    arrow: {
      position: 'absolute',
    },
    reference: {},
  };
  Object.assign(state.elements.popper.style, initialStyles.popper);
  state.styles = initialStyles;
  if (state.elements.arrow) {
    Object.assign(state.elements.arrow.style, initialStyles.arrow);
  }
  return function () {
    Object.keys(state.elements).forEach(function (name) {
      var element = state.elements[name];
      var attributes = state.attributes[name] || {};
      var styleProperties = Object.keys(
        state.styles.hasOwnProperty(name)
          ? state.styles[name]
          : initialStyles[name]
      );
      var style = styleProperties.reduce(function (style2, property) {
        style2[property] = '';
        return style2;
      }, {});
      if (!isHTMLElement(element) || !getNodeName(element)) {
        return;
      }
      Object.assign(element.style, style);
      Object.keys(attributes).forEach(function (attribute) {
        element.removeAttribute(attribute);
      });
    });
  };
}
var applyStyles_default = {
  name: 'applyStyles',
  enabled: true,
  phase: 'write',
  fn: applyStyles,
  effect,
  requires: ['computeStyles'],
};

// node_modules/@popperjs/core/lib/utils/getBasePlacement.js
function getBasePlacement(placement) {
  return placement.split('-')[0];
}

// node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js
function getBoundingClientRect(element, includeScale) {
  if (includeScale === void 0) {
    includeScale = false;
  }
  var rect = element.getBoundingClientRect();
  var scaleX = 1;
  var scaleY = 1;
  return {
    width: rect.width / scaleX,
    height: rect.height / scaleY,
    top: rect.top / scaleY,
    right: rect.right / scaleX,
    bottom: rect.bottom / scaleY,
    left: rect.left / scaleX,
    x: rect.left / scaleX,
    y: rect.top / scaleY,
  };
}

// node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js
function getLayoutRect(element) {
  var clientRect = getBoundingClientRect(element);
  var width = element.offsetWidth;
  var height = element.offsetHeight;
  if (Math.abs(clientRect.width - width) <= 1) {
    width = clientRect.width;
  }
  if (Math.abs(clientRect.height - height) <= 1) {
    height = clientRect.height;
  }
  return {
    x: element.offsetLeft,
    y: element.offsetTop,
    width,
    height,
  };
}

// node_modules/@popperjs/core/lib/dom-utils/contains.js
function contains(parent, child) {
  var rootNode = child.getRootNode && child.getRootNode();
  if (parent.contains(child)) {
    return true;
  } else if (rootNode && isShadowRoot(rootNode)) {
    var next = child;
    do {
      if (next && parent.isSameNode(next)) {
        return true;
      }
      next = next.parentNode || next.host;
    } while (next);
  }
  return false;
}

// node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js
function getComputedStyle(element) {
  return getWindow(element).getComputedStyle(element);
}

// node_modules/@popperjs/core/lib/dom-utils/isTableElement.js
function isTableElement(element) {
  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;
}

// node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js
function getDocumentElement(element) {
  return (
    (isElement(element)
      ? element.ownerDocument
      : // $FlowFixMe[prop-missing]
        element.document) || window.document
  ).documentElement;
}

// node_modules/@popperjs/core/lib/dom-utils/getParentNode.js
function getParentNode(element) {
  if (getNodeName(element) === 'html') {
    return element;
  }
  return (
    // this is a quicker (but less type safe) way to save quite some bytes from the bundle
    // $FlowFixMe[incompatible-return]
    // $FlowFixMe[prop-missing]
    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node
    element.parentNode || // DOM Element detected
    (isShadowRoot(element) ? element.host : null) || // ShadowRoot detected
    // $FlowFixMe[incompatible-call]: HTMLElement is a Node
    getDocumentElement(element)
  );
}

// node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js
function getTrueOffsetParent(element) {
  if (
    !isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837
    getComputedStyle(element).position === 'fixed'
  ) {
    return null;
  }
  return element.offsetParent;
}
function getContainingBlock(element) {
  var isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') !== -1;
  var isIE = navigator.userAgent.indexOf('Trident') !== -1;
  if (isIE && isHTMLElement(element)) {
    var elementCss = getComputedStyle(element);
    if (elementCss.position === 'fixed') {
      return null;
    }
  }
  var currentNode = getParentNode(element);
  while (
    isHTMLElement(currentNode) &&
    ['html', 'body'].indexOf(getNodeName(currentNode)) < 0
  ) {
    var css = getComputedStyle(currentNode);
    if (
      css.transform !== 'none' ||
      css.perspective !== 'none' ||
      css.contain === 'paint' ||
      ['transform', 'perspective'].indexOf(css.willChange) !== -1 ||
      (isFirefox && css.willChange === 'filter') ||
      (isFirefox && css.filter && css.filter !== 'none')
    ) {
      return currentNode;
    } else {
      currentNode = currentNode.parentNode;
    }
  }
  return null;
}
function getOffsetParent(element) {
  var window2 = getWindow(element);
  var offsetParent = getTrueOffsetParent(element);
  while (
    offsetParent &&
    isTableElement(offsetParent) &&
    getComputedStyle(offsetParent).position === 'static'
  ) {
    offsetParent = getTrueOffsetParent(offsetParent);
  }
  if (
    offsetParent &&
    (getNodeName(offsetParent) === 'html' ||
      (getNodeName(offsetParent) === 'body' &&
        getComputedStyle(offsetParent).position === 'static'))
  ) {
    return window2;
  }
  return offsetParent || getContainingBlock(element) || window2;
}

// node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js
function getMainAxisFromPlacement(placement) {
  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';
}

// node_modules/@popperjs/core/lib/utils/math.js
var max = Math.max;
var min = Math.min;
var round = Math.round;

// node_modules/@popperjs/core/lib/utils/within.js
function within(min2, value, max2) {
  return max(min2, min(value, max2));
}

// node_modules/@popperjs/core/lib/utils/getFreshSideObject.js
function getFreshSideObject() {
  return {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  };
}

// node_modules/@popperjs/core/lib/utils/mergePaddingObject.js
function mergePaddingObject(paddingObject) {
  return Object.assign({}, getFreshSideObject(), paddingObject);
}

// node_modules/@popperjs/core/lib/utils/expandToHashMap.js
function expandToHashMap(value, keys) {
  return keys.reduce(function (hashMap, key) {
    hashMap[key] = value;
    return hashMap;
  }, {});
}

// node_modules/@popperjs/core/lib/modifiers/arrow.js
var toPaddingObject = function toPaddingObject2(padding, state) {
  padding =
    typeof padding === 'function'
      ? padding(
          Object.assign({}, state.rects, {
            placement: state.placement,
          })
        )
      : padding;
  return mergePaddingObject(
    typeof padding !== 'number'
      ? padding
      : expandToHashMap(padding, basePlacements)
  );
};
function arrow(_ref) {
  var _state$modifiersData$;
  var state = _ref.state,
    name = _ref.name,
    options = _ref.options;
  var arrowElement = state.elements.arrow;
  var popperOffsets2 = state.modifiersData.popperOffsets;
  var basePlacement = getBasePlacement(state.placement);
  var axis = getMainAxisFromPlacement(basePlacement);
  var isVertical = [left, right].indexOf(basePlacement) >= 0;
  var len = isVertical ? 'height' : 'width';
  if (!arrowElement || !popperOffsets2) {
    return;
  }
  var paddingObject = toPaddingObject(options.padding, state);
  var arrowRect = getLayoutRect(arrowElement);
  var minProp = axis === 'y' ? top : left;
  var maxProp = axis === 'y' ? bottom : right;
  var endDiff =
    state.rects.reference[len] +
    state.rects.reference[axis] -
    popperOffsets2[axis] -
    state.rects.popper[len];
  var startDiff = popperOffsets2[axis] - state.rects.reference[axis];
  var arrowOffsetParent = getOffsetParent(arrowElement);
  var clientSize = arrowOffsetParent
    ? axis === 'y'
      ? arrowOffsetParent.clientHeight || 0
      : arrowOffsetParent.clientWidth || 0
    : 0;
  var centerToReference = endDiff / 2 - startDiff / 2;
  var min2 = paddingObject[minProp];
  var max2 = clientSize - arrowRect[len] - paddingObject[maxProp];
  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;
  var offset2 = within(min2, center, max2);
  var axisProp = axis;
  state.modifiersData[name] =
    ((_state$modifiersData$ = {}),
    (_state$modifiersData$[axisProp] = offset2),
    (_state$modifiersData$.centerOffset = offset2 - center),
    _state$modifiersData$);
}
function effect2(_ref2) {
  var state = _ref2.state,
    options = _ref2.options;
  var _options$element = options.element,
    arrowElement =
      _options$element === void 0 ? '[data-popper-arrow]' : _options$element;
  if (arrowElement == null) {
    return;
  }
  if (typeof arrowElement === 'string') {
    arrowElement = state.elements.popper.querySelector(arrowElement);
    if (!arrowElement) {
      return;
    }
  }
  if (true) {
    if (!isHTMLElement(arrowElement)) {
      console.error(
        [
          'Popper: "arrow" element must be an HTMLElement (not an SVGElement).',
          'To use an SVG arrow, wrap it in an HTMLElement that will be used as',
          'the arrow.',
        ].join(' ')
      );
    }
  }
  if (!contains(state.elements.popper, arrowElement)) {
    if (true) {
      console.error(
        [
          'Popper: "arrow" modifier\'s `element` must be a child of the popper',
          'element.',
        ].join(' ')
      );
    }
    return;
  }
  state.elements.arrow = arrowElement;
}
var arrow_default = {
  name: 'arrow',
  enabled: true,
  phase: 'main',
  fn: arrow,
  effect: effect2,
  requires: ['popperOffsets'],
  requiresIfExists: ['preventOverflow'],
};

// node_modules/@popperjs/core/lib/utils/getVariation.js
function getVariation(placement) {
  return placement.split('-')[1];
}

// node_modules/@popperjs/core/lib/modifiers/computeStyles.js
var unsetSides = {
  top: 'auto',
  right: 'auto',
  bottom: 'auto',
  left: 'auto',
};
function roundOffsetsByDPR(_ref) {
  var x = _ref.x,
    y = _ref.y;
  var win = window;
  var dpr = win.devicePixelRatio || 1;
  return {
    x: round(round(x * dpr) / dpr) || 0,
    y: round(round(y * dpr) / dpr) || 0,
  };
}
function mapToStyles(_ref2) {
  var _Object$assign2;
  var popper2 = _ref2.popper,
    popperRect = _ref2.popperRect,
    placement = _ref2.placement,
    variation = _ref2.variation,
    offsets = _ref2.offsets,
    position = _ref2.position,
    gpuAcceleration = _ref2.gpuAcceleration,
    adaptive = _ref2.adaptive,
    roundOffsets = _ref2.roundOffsets;
  var _ref3 =
      roundOffsets === true
        ? roundOffsetsByDPR(offsets)
        : typeof roundOffsets === 'function'
          ? roundOffsets(offsets)
          : offsets,
    _ref3$x = _ref3.x,
    x = _ref3$x === void 0 ? 0 : _ref3$x,
    _ref3$y = _ref3.y,
    y = _ref3$y === void 0 ? 0 : _ref3$y;
  var hasX = offsets.hasOwnProperty('x');
  var hasY = offsets.hasOwnProperty('y');
  var sideX = left;
  var sideY = top;
  var win = window;
  if (adaptive) {
    var offsetParent = getOffsetParent(popper2);
    var heightProp = 'clientHeight';
    var widthProp = 'clientWidth';
    if (offsetParent === getWindow(popper2)) {
      offsetParent = getDocumentElement(popper2);
      if (
        getComputedStyle(offsetParent).position !== 'static' &&
        position === 'absolute'
      ) {
        heightProp = 'scrollHeight';
        widthProp = 'scrollWidth';
      }
    }
    offsetParent = offsetParent;
    if (
      placement === top ||
      ((placement === left || placement === right) && variation === end)
    ) {
      sideY = bottom;
      y -= offsetParent[heightProp] - popperRect.height;
      y *= gpuAcceleration ? 1 : -1;
    }
    if (
      placement === left ||
      ((placement === top || placement === bottom) && variation === end)
    ) {
      sideX = right;
      x -= offsetParent[widthProp] - popperRect.width;
      x *= gpuAcceleration ? 1 : -1;
    }
  }
  var commonStyles = Object.assign(
    {
      position,
    },
    adaptive && unsetSides
  );
  if (gpuAcceleration) {
    var _Object$assign;
    return Object.assign(
      {},
      commonStyles,
      ((_Object$assign = {}),
      (_Object$assign[sideY] = hasY ? '0' : ''),
      (_Object$assign[sideX] = hasX ? '0' : ''),
      (_Object$assign.transform =
        (win.devicePixelRatio || 1) <= 1
          ? 'translate(' + x + 'px, ' + y + 'px)'
          : 'translate3d(' + x + 'px, ' + y + 'px, 0)'),
      _Object$assign)
    );
  }
  return Object.assign(
    {},
    commonStyles,
    ((_Object$assign2 = {}),
    (_Object$assign2[sideY] = hasY ? y + 'px' : ''),
    (_Object$assign2[sideX] = hasX ? x + 'px' : ''),
    (_Object$assign2.transform = ''),
    _Object$assign2)
  );
}
function computeStyles(_ref4) {
  var state = _ref4.state,
    options = _ref4.options;
  var _options$gpuAccelerat = options.gpuAcceleration,
    gpuAcceleration =
      _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,
    _options$adaptive = options.adaptive,
    adaptive = _options$adaptive === void 0 ? true : _options$adaptive,
    _options$roundOffsets = options.roundOffsets,
    roundOffsets =
      _options$roundOffsets === void 0 ? true : _options$roundOffsets;
  if (true) {
    var transitionProperty =
      getComputedStyle(state.elements.popper).transitionProperty || '';
    if (
      adaptive &&
      ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {
        return transitionProperty.indexOf(property) >= 0;
      })
    ) {
      console.warn(
        [
          'Popper: Detected CSS transitions on at least one of the following',
          'CSS properties: "transform", "top", "right", "bottom", "left".',
          '\n\n',
          'Disable the "computeStyles" modifier\'s `adaptive` option to allow',
          'for smooth transitions, or remove these properties from the CSS',
          'transition declaration on the popper element if only transitioning',
          'opacity or background-color for example.',
          '\n\n',
          'We recommend using the popper element as a wrapper around an inner',
          'element that can have any CSS property transitioned for animations.',
        ].join(' ')
      );
    }
  }
  var commonStyles = {
    placement: getBasePlacement(state.placement),
    variation: getVariation(state.placement),
    popper: state.elements.popper,
    popperRect: state.rects.popper,
    gpuAcceleration,
  };
  if (state.modifiersData.popperOffsets != null) {
    state.styles.popper = Object.assign(
      {},
      state.styles.popper,
      mapToStyles(
        Object.assign({}, commonStyles, {
          offsets: state.modifiersData.popperOffsets,
          position: state.options.strategy,
          adaptive,
          roundOffsets,
        })
      )
    );
  }
  if (state.modifiersData.arrow != null) {
    state.styles.arrow = Object.assign(
      {},
      state.styles.arrow,
      mapToStyles(
        Object.assign({}, commonStyles, {
          offsets: state.modifiersData.arrow,
          position: 'absolute',
          adaptive: false,
          roundOffsets,
        })
      )
    );
  }
  state.attributes.popper = Object.assign({}, state.attributes.popper, {
    'data-popper-placement': state.placement,
  });
}
var computeStyles_default = {
  name: 'computeStyles',
  enabled: true,
  phase: 'beforeWrite',
  fn: computeStyles,
  data: {},
};

// node_modules/@popperjs/core/lib/modifiers/eventListeners.js
var passive = {
  passive: true,
};
function effect3(_ref) {
  var state = _ref.state,
    instance = _ref.instance,
    options = _ref.options;
  var _options$scroll = options.scroll,
    scroll = _options$scroll === void 0 ? true : _options$scroll,
    _options$resize = options.resize,
    resize = _options$resize === void 0 ? true : _options$resize;
  var window2 = getWindow(state.elements.popper);
  var scrollParents = [].concat(
    state.scrollParents.reference,
    state.scrollParents.popper
  );
  if (scroll) {
    scrollParents.forEach(function (scrollParent) {
      scrollParent.addEventListener('scroll', instance.update, passive);
    });
  }
  if (resize) {
    window2.addEventListener('resize', instance.update, passive);
  }
  return function () {
    if (scroll) {
      scrollParents.forEach(function (scrollParent) {
        scrollParent.removeEventListener('scroll', instance.update, passive);
      });
    }
    if (resize) {
      window2.removeEventListener('resize', instance.update, passive);
    }
  };
}
var eventListeners_default = {
  name: 'eventListeners',
  enabled: true,
  phase: 'write',
  fn: function fn() {},
  effect: effect3,
  data: {},
};

// node_modules/@popperjs/core/lib/utils/getOppositePlacement.js
var hash = {
  left: 'right',
  right: 'left',
  bottom: 'top',
  top: 'bottom',
};
function getOppositePlacement(placement) {
  return placement.replace(/left|right|bottom|top/g, function (matched) {
    return hash[matched];
  });
}

// node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js
var hash2 = {
  start: 'end',
  end: 'start',
};
function getOppositeVariationPlacement(placement) {
  return placement.replace(/start|end/g, function (matched) {
    return hash2[matched];
  });
}

// node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js
function getWindowScroll(node) {
  var win = getWindow(node);
  var scrollLeft = win.pageXOffset;
  var scrollTop = win.pageYOffset;
  return {
    scrollLeft,
    scrollTop,
  };
}

// node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js
function getWindowScrollBarX(element) {
  return (
    getBoundingClientRect(getDocumentElement(element)).left +
    getWindowScroll(element).scrollLeft
  );
}

// node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js
function getViewportRect(element) {
  var win = getWindow(element);
  var html = getDocumentElement(element);
  var visualViewport = win.visualViewport;
  var width = html.clientWidth;
  var height = html.clientHeight;
  var x = 0;
  var y = 0;
  if (visualViewport) {
    width = visualViewport.width;
    height = visualViewport.height;
    if (!/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {
      x = visualViewport.offsetLeft;
      y = visualViewport.offsetTop;
    }
  }
  return {
    width,
    height,
    x: x + getWindowScrollBarX(element),
    y,
  };
}

// node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js
function getDocumentRect(element) {
  var _element$ownerDocumen;
  var html = getDocumentElement(element);
  var winScroll = getWindowScroll(element);
  var body =
    (_element$ownerDocumen = element.ownerDocument) == null
      ? void 0
      : _element$ownerDocumen.body;
  var width = max(
    html.scrollWidth,
    html.clientWidth,
    body ? body.scrollWidth : 0,
    body ? body.clientWidth : 0
  );
  var height = max(
    html.scrollHeight,
    html.clientHeight,
    body ? body.scrollHeight : 0,
    body ? body.clientHeight : 0
  );
  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);
  var y = -winScroll.scrollTop;
  if (getComputedStyle(body || html).direction === 'rtl') {
    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;
  }
  return {
    width,
    height,
    x,
    y,
  };
}

// node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js
function isScrollParent(element) {
  var _getComputedStyle = getComputedStyle(element),
    overflow = _getComputedStyle.overflow,
    overflowX = _getComputedStyle.overflowX,
    overflowY = _getComputedStyle.overflowY;
  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);
}

// node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js
function getScrollParent(node) {
  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {
    return node.ownerDocument.body;
  }
  if (isHTMLElement(node) && isScrollParent(node)) {
    return node;
  }
  return getScrollParent(getParentNode(node));
}

// node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js
function listScrollParents(element, list) {
  var _element$ownerDocumen;
  if (list === void 0) {
    list = [];
  }
  var scrollParent = getScrollParent(element);
  var isBody =
    scrollParent ===
    ((_element$ownerDocumen = element.ownerDocument) == null
      ? void 0
      : _element$ownerDocumen.body);
  var win = getWindow(scrollParent);
  var target = isBody
    ? [win].concat(
        win.visualViewport || [],
        isScrollParent(scrollParent) ? scrollParent : []
      )
    : scrollParent;
  var updatedList = list.concat(target);
  return isBody
    ? updatedList
    : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here
      updatedList.concat(listScrollParents(getParentNode(target)));
}

// node_modules/@popperjs/core/lib/utils/rectToClientRect.js
function rectToClientRect(rect) {
  return Object.assign({}, rect, {
    left: rect.x,
    top: rect.y,
    right: rect.x + rect.width,
    bottom: rect.y + rect.height,
  });
}

// node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js
function getInnerBoundingClientRect(element) {
  var rect = getBoundingClientRect(element);
  rect.top = rect.top + element.clientTop;
  rect.left = rect.left + element.clientLeft;
  rect.bottom = rect.top + element.clientHeight;
  rect.right = rect.left + element.clientWidth;
  rect.width = element.clientWidth;
  rect.height = element.clientHeight;
  rect.x = rect.left;
  rect.y = rect.top;
  return rect;
}
function getClientRectFromMixedType(element, clippingParent) {
  return clippingParent === viewport
    ? rectToClientRect(getViewportRect(element))
    : isHTMLElement(clippingParent)
      ? getInnerBoundingClientRect(clippingParent)
      : rectToClientRect(getDocumentRect(getDocumentElement(element)));
}
function getClippingParents(element) {
  var clippingParents2 = listScrollParents(getParentNode(element));
  var canEscapeClipping =
    ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;
  var clipperElement =
    canEscapeClipping && isHTMLElement(element)
      ? getOffsetParent(element)
      : element;
  if (!isElement(clipperElement)) {
    return [];
  }
  return clippingParents2.filter(function (clippingParent) {
    return (
      isElement(clippingParent) &&
      contains(clippingParent, clipperElement) &&
      getNodeName(clippingParent) !== 'body'
    );
  });
}
function getClippingRect(element, boundary, rootBoundary) {
  var mainClippingParents =
    boundary === 'clippingParents'
      ? getClippingParents(element)
      : [].concat(boundary);
  var clippingParents2 = [].concat(mainClippingParents, [rootBoundary]);
  var firstClippingParent = clippingParents2[0];
  var clippingRect = clippingParents2.reduce(
    function (accRect, clippingParent) {
      var rect = getClientRectFromMixedType(element, clippingParent);
      accRect.top = max(rect.top, accRect.top);
      accRect.right = min(rect.right, accRect.right);
      accRect.bottom = min(rect.bottom, accRect.bottom);
      accRect.left = max(rect.left, accRect.left);
      return accRect;
    },
    getClientRectFromMixedType(element, firstClippingParent)
  );
  clippingRect.width = clippingRect.right - clippingRect.left;
  clippingRect.height = clippingRect.bottom - clippingRect.top;
  clippingRect.x = clippingRect.left;
  clippingRect.y = clippingRect.top;
  return clippingRect;
}

// node_modules/@popperjs/core/lib/utils/computeOffsets.js
function computeOffsets(_ref) {
  var reference2 = _ref.reference,
    element = _ref.element,
    placement = _ref.placement;
  var basePlacement = placement ? getBasePlacement(placement) : null;
  var variation = placement ? getVariation(placement) : null;
  var commonX = reference2.x + reference2.width / 2 - element.width / 2;
  var commonY = reference2.y + reference2.height / 2 - element.height / 2;
  var offsets;
  switch (basePlacement) {
    case top:
      offsets = {
        x: commonX,
        y: reference2.y - element.height,
      };
      break;
    case bottom:
      offsets = {
        x: commonX,
        y: reference2.y + reference2.height,
      };
      break;
    case right:
      offsets = {
        x: reference2.x + reference2.width,
        y: commonY,
      };
      break;
    case left:
      offsets = {
        x: reference2.x - element.width,
        y: commonY,
      };
      break;
    default:
      offsets = {
        x: reference2.x,
        y: reference2.y,
      };
  }
  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;
  if (mainAxis != null) {
    var len = mainAxis === 'y' ? 'height' : 'width';
    switch (variation) {
      case start:
        offsets[mainAxis] =
          offsets[mainAxis] - (reference2[len] / 2 - element[len] / 2);
        break;
      case end:
        offsets[mainAxis] =
          offsets[mainAxis] + (reference2[len] / 2 - element[len] / 2);
        break;
      default:
    }
  }
  return offsets;
}

// node_modules/@popperjs/core/lib/utils/detectOverflow.js
function detectOverflow(state, options) {
  if (options === void 0) {
    options = {};
  }
  var _options = options,
    _options$placement = _options.placement,
    placement =
      _options$placement === void 0 ? state.placement : _options$placement,
    _options$boundary = _options.boundary,
    boundary =
      _options$boundary === void 0 ? clippingParents : _options$boundary,
    _options$rootBoundary = _options.rootBoundary,
    rootBoundary =
      _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,
    _options$elementConte = _options.elementContext,
    elementContext =
      _options$elementConte === void 0 ? popper : _options$elementConte,
    _options$altBoundary = _options.altBoundary,
    altBoundary =
      _options$altBoundary === void 0 ? false : _options$altBoundary,
    _options$padding = _options.padding,
    padding = _options$padding === void 0 ? 0 : _options$padding;
  var paddingObject = mergePaddingObject(
    typeof padding !== 'number'
      ? padding
      : expandToHashMap(padding, basePlacements)
  );
  var altContext = elementContext === popper ? reference : popper;
  var popperRect = state.rects.popper;
  var element = state.elements[altBoundary ? altContext : elementContext];
  var clippingClientRect = getClippingRect(
    isElement(element)
      ? element
      : element.contextElement || getDocumentElement(state.elements.popper),
    boundary,
    rootBoundary
  );
  var referenceClientRect = getBoundingClientRect(state.elements.reference);
  var popperOffsets2 = computeOffsets({
    reference: referenceClientRect,
    element: popperRect,
    strategy: 'absolute',
    placement,
  });
  var popperClientRect = rectToClientRect(
    Object.assign({}, popperRect, popperOffsets2)
  );
  var elementClientRect =
    elementContext === popper ? popperClientRect : referenceClientRect;
  var overflowOffsets = {
    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,
    bottom:
      elementClientRect.bottom -
      clippingClientRect.bottom +
      paddingObject.bottom,
    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,
    right:
      elementClientRect.right - clippingClientRect.right + paddingObject.right,
  };
  var offsetData = state.modifiersData.offset;
  if (elementContext === popper && offsetData) {
    var offset2 = offsetData[placement];
    Object.keys(overflowOffsets).forEach(function (key) {
      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;
      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';
      overflowOffsets[key] += offset2[axis] * multiply;
    });
  }
  return overflowOffsets;
}

// node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js
function computeAutoPlacement(state, options) {
  if (options === void 0) {
    options = {};
  }
  var _options = options,
    placement = _options.placement,
    boundary = _options.boundary,
    rootBoundary = _options.rootBoundary,
    padding = _options.padding,
    flipVariations = _options.flipVariations,
    _options$allowedAutoP = _options.allowedAutoPlacements,
    allowedAutoPlacements =
      _options$allowedAutoP === void 0 ? placements : _options$allowedAutoP;
  var variation = getVariation(placement);
  var placements2 = variation
    ? flipVariations
      ? variationPlacements
      : variationPlacements.filter(function (placement2) {
          return getVariation(placement2) === variation;
        })
    : basePlacements;
  var allowedPlacements = placements2.filter(function (placement2) {
    return allowedAutoPlacements.indexOf(placement2) >= 0;
  });
  if (allowedPlacements.length === 0) {
    allowedPlacements = placements2;
    if (true) {
      console.error(
        [
          'Popper: The `allowedAutoPlacements` option did not allow any',
          'placements. Ensure the `placement` option matches the variation',
          'of the allowed placements.',
          'For example, "auto" cannot be used to allow "bottom-start".',
          'Use "auto-start" instead.',
        ].join(' ')
      );
    }
  }
  var overflows = allowedPlacements.reduce(function (acc, placement2) {
    acc[placement2] = detectOverflow(state, {
      placement: placement2,
      boundary,
      rootBoundary,
      padding,
    })[getBasePlacement(placement2)];
    return acc;
  }, {});
  return Object.keys(overflows).sort(function (a, b) {
    return overflows[a] - overflows[b];
  });
}

// node_modules/@popperjs/core/lib/modifiers/flip.js
function getExpandedFallbackPlacements(placement) {
  if (getBasePlacement(placement) === auto) {
    return [];
  }
  var oppositePlacement = getOppositePlacement(placement);
  return [
    getOppositeVariationPlacement(placement),
    oppositePlacement,
    getOppositeVariationPlacement(oppositePlacement),
  ];
}
function flip(_ref) {
  var state = _ref.state,
    options = _ref.options,
    name = _ref.name;
  if (state.modifiersData[name]._skip) {
    return;
  }
  var _options$mainAxis = options.mainAxis,
    checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,
    _options$altAxis = options.altAxis,
    checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,
    specifiedFallbackPlacements = options.fallbackPlacements,
    padding = options.padding,
    boundary = options.boundary,
    rootBoundary = options.rootBoundary,
    altBoundary = options.altBoundary,
    _options$flipVariatio = options.flipVariations,
    flipVariations =
      _options$flipVariatio === void 0 ? true : _options$flipVariatio,
    allowedAutoPlacements = options.allowedAutoPlacements;
  var preferredPlacement = state.options.placement;
  var basePlacement = getBasePlacement(preferredPlacement);
  var isBasePlacement = basePlacement === preferredPlacement;
  var fallbackPlacements =
    specifiedFallbackPlacements ||
    (isBasePlacement || !flipVariations
      ? [getOppositePlacement(preferredPlacement)]
      : getExpandedFallbackPlacements(preferredPlacement));
  var placements2 = [preferredPlacement]
    .concat(fallbackPlacements)
    .reduce(function (acc, placement2) {
      return acc.concat(
        getBasePlacement(placement2) === auto
          ? computeAutoPlacement(state, {
              placement: placement2,
              boundary,
              rootBoundary,
              padding,
              flipVariations,
              allowedAutoPlacements,
            })
          : placement2
      );
    }, []);
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var checksMap = /* @__PURE__ */ new Map();
  var makeFallbackChecks = true;
  var firstFittingPlacement = placements2[0];
  for (var i = 0; i < placements2.length; i++) {
    var placement = placements2[i];
    var _basePlacement = getBasePlacement(placement);
    var isStartVariation = getVariation(placement) === start;
    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;
    var len = isVertical ? 'width' : 'height';
    var overflow = detectOverflow(state, {
      placement,
      boundary,
      rootBoundary,
      altBoundary,
      padding,
    });
    var mainVariationSide = isVertical
      ? isStartVariation
        ? right
        : left
      : isStartVariation
        ? bottom
        : top;
    if (referenceRect[len] > popperRect[len]) {
      mainVariationSide = getOppositePlacement(mainVariationSide);
    }
    var altVariationSide = getOppositePlacement(mainVariationSide);
    var checks = [];
    if (checkMainAxis) {
      checks.push(overflow[_basePlacement] <= 0);
    }
    if (checkAltAxis) {
      checks.push(
        overflow[mainVariationSide] <= 0,
        overflow[altVariationSide] <= 0
      );
    }
    if (
      checks.every(function (check) {
        return check;
      })
    ) {
      firstFittingPlacement = placement;
      makeFallbackChecks = false;
      break;
    }
    checksMap.set(placement, checks);
  }
  if (makeFallbackChecks) {
    var numberOfChecks = flipVariations ? 3 : 1;
    var _loop = function _loop2(_i2) {
      var fittingPlacement = placements2.find(function (placement2) {
        var checks2 = checksMap.get(placement2);
        if (checks2) {
          return checks2.slice(0, _i2).every(function (check) {
            return check;
          });
        }
      });
      if (fittingPlacement) {
        firstFittingPlacement = fittingPlacement;
        return 'break';
      }
    };
    for (var _i = numberOfChecks; _i > 0; _i--) {
      var _ret = _loop(_i);
      if (_ret === 'break') break;
    }
  }
  if (state.placement !== firstFittingPlacement) {
    state.modifiersData[name]._skip = true;
    state.placement = firstFittingPlacement;
    state.reset = true;
  }
}
var flip_default = {
  name: 'flip',
  enabled: true,
  phase: 'main',
  fn: flip,
  requiresIfExists: ['offset'],
  data: {
    _skip: false,
  },
};

// node_modules/@popperjs/core/lib/modifiers/hide.js
function getSideOffsets(overflow, rect, preventedOffsets) {
  if (preventedOffsets === void 0) {
    preventedOffsets = {
      x: 0,
      y: 0,
    };
  }
  return {
    top: overflow.top - rect.height - preventedOffsets.y,
    right: overflow.right - rect.width + preventedOffsets.x,
    bottom: overflow.bottom - rect.height + preventedOffsets.y,
    left: overflow.left - rect.width - preventedOffsets.x,
  };
}
function isAnySideFullyClipped(overflow) {
  return [top, right, bottom, left].some(function (side) {
    return overflow[side] >= 0;
  });
}
function hide(_ref) {
  var state = _ref.state,
    name = _ref.name;
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var preventedOffsets = state.modifiersData.preventOverflow;
  var referenceOverflow = detectOverflow(state, {
    elementContext: 'reference',
  });
  var popperAltOverflow = detectOverflow(state, {
    altBoundary: true,
  });
  var referenceClippingOffsets = getSideOffsets(
    referenceOverflow,
    referenceRect
  );
  var popperEscapeOffsets = getSideOffsets(
    popperAltOverflow,
    popperRect,
    preventedOffsets
  );
  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);
  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);
  state.modifiersData[name] = {
    referenceClippingOffsets,
    popperEscapeOffsets,
    isReferenceHidden,
    hasPopperEscaped,
  };
  state.attributes.popper = Object.assign({}, state.attributes.popper, {
    'data-popper-reference-hidden': isReferenceHidden,
    'data-popper-escaped': hasPopperEscaped,
  });
}
var hide_default = {
  name: 'hide',
  enabled: true,
  phase: 'main',
  requiresIfExists: ['preventOverflow'],
  fn: hide,
};

// node_modules/@popperjs/core/lib/modifiers/offset.js
function distanceAndSkiddingToXY(placement, rects, offset2) {
  var basePlacement = getBasePlacement(placement);
  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;
  var _ref =
      typeof offset2 === 'function'
        ? offset2(
            Object.assign({}, rects, {
              placement,
            })
          )
        : offset2,
    skidding = _ref[0],
    distance = _ref[1];
  skidding = skidding || 0;
  distance = (distance || 0) * invertDistance;
  return [left, right].indexOf(basePlacement) >= 0
    ? {
        x: distance,
        y: skidding,
      }
    : {
        x: skidding,
        y: distance,
      };
}
function offset(_ref2) {
  var state = _ref2.state,
    options = _ref2.options,
    name = _ref2.name;
  var _options$offset = options.offset,
    offset2 = _options$offset === void 0 ? [0, 0] : _options$offset;
  var data = placements.reduce(function (acc, placement) {
    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset2);
    return acc;
  }, {});
  var _data$state$placement = data[state.placement],
    x = _data$state$placement.x,
    y = _data$state$placement.y;
  if (state.modifiersData.popperOffsets != null) {
    state.modifiersData.popperOffsets.x += x;
    state.modifiersData.popperOffsets.y += y;
  }
  state.modifiersData[name] = data;
}
var offset_default = {
  name: 'offset',
  enabled: true,
  phase: 'main',
  requires: ['popperOffsets'],
  fn: offset,
};

// node_modules/@popperjs/core/lib/modifiers/popperOffsets.js
function popperOffsets(_ref) {
  var state = _ref.state,
    name = _ref.name;
  state.modifiersData[name] = computeOffsets({
    reference: state.rects.reference,
    element: state.rects.popper,
    strategy: 'absolute',
    placement: state.placement,
  });
}
var popperOffsets_default = {
  name: 'popperOffsets',
  enabled: true,
  phase: 'read',
  fn: popperOffsets,
  data: {},
};

// node_modules/@popperjs/core/lib/utils/getAltAxis.js
function getAltAxis(axis) {
  return axis === 'x' ? 'y' : 'x';
}

// node_modules/@popperjs/core/lib/modifiers/preventOverflow.js
function preventOverflow(_ref) {
  var state = _ref.state,
    options = _ref.options,
    name = _ref.name;
  var _options$mainAxis = options.mainAxis,
    checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,
    _options$altAxis = options.altAxis,
    checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,
    boundary = options.boundary,
    rootBoundary = options.rootBoundary,
    altBoundary = options.altBoundary,
    padding = options.padding,
    _options$tether = options.tether,
    tether = _options$tether === void 0 ? true : _options$tether,
    _options$tetherOffset = options.tetherOffset,
    tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;
  var overflow = detectOverflow(state, {
    boundary,
    rootBoundary,
    padding,
    altBoundary,
  });
  var basePlacement = getBasePlacement(state.placement);
  var variation = getVariation(state.placement);
  var isBasePlacement = !variation;
  var mainAxis = getMainAxisFromPlacement(basePlacement);
  var altAxis = getAltAxis(mainAxis);
  var popperOffsets2 = state.modifiersData.popperOffsets;
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var tetherOffsetValue =
    typeof tetherOffset === 'function'
      ? tetherOffset(
          Object.assign({}, state.rects, {
            placement: state.placement,
          })
        )
      : tetherOffset;
  var data = {
    x: 0,
    y: 0,
  };
  if (!popperOffsets2) {
    return;
  }
  if (checkMainAxis || checkAltAxis) {
    var mainSide = mainAxis === 'y' ? top : left;
    var altSide = mainAxis === 'y' ? bottom : right;
    var len = mainAxis === 'y' ? 'height' : 'width';
    var offset2 = popperOffsets2[mainAxis];
    var min2 = popperOffsets2[mainAxis] + overflow[mainSide];
    var max2 = popperOffsets2[mainAxis] - overflow[altSide];
    var additive = tether ? -popperRect[len] / 2 : 0;
    var minLen = variation === start ? referenceRect[len] : popperRect[len];
    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len];
    var arrowElement = state.elements.arrow;
    var arrowRect =
      tether && arrowElement
        ? getLayoutRect(arrowElement)
        : {
            width: 0,
            height: 0,
          };
    var arrowPaddingObject = state.modifiersData['arrow#persistent']
      ? state.modifiersData['arrow#persistent'].padding
      : getFreshSideObject();
    var arrowPaddingMin = arrowPaddingObject[mainSide];
    var arrowPaddingMax = arrowPaddingObject[altSide];
    var arrowLen = within(0, referenceRect[len], arrowRect[len]);
    var minOffset = isBasePlacement
      ? referenceRect[len] / 2 -
        additive -
        arrowLen -
        arrowPaddingMin -
        tetherOffsetValue
      : minLen - arrowLen - arrowPaddingMin - tetherOffsetValue;
    var maxOffset = isBasePlacement
      ? -referenceRect[len] / 2 +
        additive +
        arrowLen +
        arrowPaddingMax +
        tetherOffsetValue
      : maxLen + arrowLen + arrowPaddingMax + tetherOffsetValue;
    var arrowOffsetParent =
      state.elements.arrow && getOffsetParent(state.elements.arrow);
    var clientOffset = arrowOffsetParent
      ? mainAxis === 'y'
        ? arrowOffsetParent.clientTop || 0
        : arrowOffsetParent.clientLeft || 0
      : 0;
    var offsetModifierValue = state.modifiersData.offset
      ? state.modifiersData.offset[state.placement][mainAxis]
      : 0;
    var tetherMin =
      popperOffsets2[mainAxis] + minOffset - offsetModifierValue - clientOffset;
    var tetherMax = popperOffsets2[mainAxis] + maxOffset - offsetModifierValue;
    if (checkMainAxis) {
      var preventedOffset = within(
        tether ? min(min2, tetherMin) : min2,
        offset2,
        tether ? max(max2, tetherMax) : max2
      );
      popperOffsets2[mainAxis] = preventedOffset;
      data[mainAxis] = preventedOffset - offset2;
    }
    if (checkAltAxis) {
      var _mainSide = mainAxis === 'x' ? top : left;
      var _altSide = mainAxis === 'x' ? bottom : right;
      var _offset = popperOffsets2[altAxis];
      var _min = _offset + overflow[_mainSide];
      var _max = _offset - overflow[_altSide];
      var _preventedOffset = within(
        tether ? min(_min, tetherMin) : _min,
        _offset,
        tether ? max(_max, tetherMax) : _max
      );
      popperOffsets2[altAxis] = _preventedOffset;
      data[altAxis] = _preventedOffset - _offset;
    }
  }
  state.modifiersData[name] = data;
}
var preventOverflow_default = {
  name: 'preventOverflow',
  enabled: true,
  phase: 'main',
  fn: preventOverflow,
  requiresIfExists: ['offset'],
};

// node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js
function getHTMLElementScroll(element) {
  return {
    scrollLeft: element.scrollLeft,
    scrollTop: element.scrollTop,
  };
}

// node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js
function getNodeScroll(node) {
  if (node === getWindow(node) || !isHTMLElement(node)) {
    return getWindowScroll(node);
  } else {
    return getHTMLElementScroll(node);
  }
}

// node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js
function isElementScaled(element) {
  var rect = element.getBoundingClientRect();
  var scaleX = rect.width / element.offsetWidth || 1;
  var scaleY = rect.height / element.offsetHeight || 1;
  return scaleX !== 1 || scaleY !== 1;
}
function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {
  if (isFixed === void 0) {
    isFixed = false;
  }
  var isOffsetParentAnElement = isHTMLElement(offsetParent);
  var offsetParentIsScaled =
    isHTMLElement(offsetParent) && isElementScaled(offsetParent);
  var documentElement = getDocumentElement(offsetParent);
  var rect = getBoundingClientRect(
    elementOrVirtualElement,
    offsetParentIsScaled
  );
  var scroll = {
    scrollLeft: 0,
    scrollTop: 0,
  };
  var offsets = {
    x: 0,
    y: 0,
  };
  if (isOffsetParentAnElement || (!isOffsetParentAnElement && !isFixed)) {
    if (
      getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078
      isScrollParent(documentElement)
    ) {
      scroll = getNodeScroll(offsetParent);
    }
    if (isHTMLElement(offsetParent)) {
      offsets = getBoundingClientRect(offsetParent, true);
      offsets.x += offsetParent.clientLeft;
      offsets.y += offsetParent.clientTop;
    } else if (documentElement) {
      offsets.x = getWindowScrollBarX(documentElement);
    }
  }
  return {
    x: rect.left + scroll.scrollLeft - offsets.x,
    y: rect.top + scroll.scrollTop - offsets.y,
    width: rect.width,
    height: rect.height,
  };
}

// node_modules/@popperjs/core/lib/utils/orderModifiers.js
function order(modifiers) {
  var map = /* @__PURE__ */ new Map();
  var visited = /* @__PURE__ */ new Set();
  var result = [];
  modifiers.forEach(function (modifier) {
    map.set(modifier.name, modifier);
  });
  function sort(modifier) {
    visited.add(modifier.name);
    var requires = [].concat(
      modifier.requires || [],
      modifier.requiresIfExists || []
    );
    requires.forEach(function (dep) {
      if (!visited.has(dep)) {
        var depModifier = map.get(dep);
        if (depModifier) {
          sort(depModifier);
        }
      }
    });
    result.push(modifier);
  }
  modifiers.forEach(function (modifier) {
    if (!visited.has(modifier.name)) {
      sort(modifier);
    }
  });
  return result;
}
function orderModifiers(modifiers) {
  var orderedModifiers = order(modifiers);
  return modifierPhases.reduce(function (acc, phase) {
    return acc.concat(
      orderedModifiers.filter(function (modifier) {
        return modifier.phase === phase;
      })
    );
  }, []);
}

// node_modules/@popperjs/core/lib/utils/debounce.js
function debounce(fn2) {
  var pending;
  return function () {
    if (!pending) {
      pending = new Promise(function (resolve) {
        Promise.resolve().then(function () {
          pending = void 0;
          resolve(fn2());
        });
      });
    }
    return pending;
  };
}

// node_modules/@popperjs/core/lib/utils/format.js
function format2(str) {
  for (
    var _len = arguments.length,
      args = new Array(_len > 1 ? _len - 1 : 0),
      _key = 1;
    _key < _len;
    _key++
  ) {
    args[_key - 1] = arguments[_key];
  }
  return [].concat(args).reduce(function (p, c) {
    return p.replace(/%s/, c);
  }, str);
}

// node_modules/@popperjs/core/lib/utils/validateModifiers.js
var INVALID_MODIFIER_ERROR =
  'Popper: modifier "%s" provided an invalid %s property, expected %s but got %s';
var MISSING_DEPENDENCY_ERROR =
  'Popper: modifier "%s" requires "%s", but "%s" modifier is not available';
var VALID_PROPERTIES = [
  'name',
  'enabled',
  'phase',
  'fn',
  'effect',
  'requires',
  'options',
];
function validateModifiers(modifiers) {
  modifiers.forEach(function (modifier) {
    []
      .concat(Object.keys(modifier), VALID_PROPERTIES)
      .filter(function (value, index, self2) {
        return self2.indexOf(value) === index;
      })
      .forEach(function (key) {
        switch (key) {
          case 'name':
            if (typeof modifier.name !== 'string') {
              console.error(
                format2(
                  INVALID_MODIFIER_ERROR,
                  String(modifier.name),
                  '"name"',
                  '"string"',
                  '"' + String(modifier.name) + '"'
                )
              );
            }
            break;
          case 'enabled':
            if (typeof modifier.enabled !== 'boolean') {
              console.error(
                format2(
                  INVALID_MODIFIER_ERROR,
                  modifier.name,
                  '"enabled"',
                  '"boolean"',
                  '"' + String(modifier.enabled) + '"'
                )
              );
            }
            break;
          case 'phase':
            if (modifierPhases.indexOf(modifier.phase) < 0) {
              console.error(
                format2(
                  INVALID_MODIFIER_ERROR,
                  modifier.name,
                  '"phase"',
                  'either ' + modifierPhases.join(', '),
                  '"' + String(modifier.phase) + '"'
                )
              );
            }
            break;
          case 'fn':
            if (typeof modifier.fn !== 'function') {
              console.error(
                format2(
                  INVALID_MODIFIER_ERROR,
                  modifier.name,
                  '"fn"',
                  '"function"',
                  '"' + String(modifier.fn) + '"'
                )
              );
            }
            break;
          case 'effect':
            if (
              modifier.effect != null &&
              typeof modifier.effect !== 'function'
            ) {
              console.error(
                format2(
                  INVALID_MODIFIER_ERROR,
                  modifier.name,
                  '"effect"',
                  '"function"',
                  '"' + String(modifier.fn) + '"'
                )
              );
            }
            break;
          case 'requires':
            if (
              modifier.requires != null &&
              !Array.isArray(modifier.requires)
            ) {
              console.error(
                format2(
                  INVALID_MODIFIER_ERROR,
                  modifier.name,
                  '"requires"',
                  '"array"',
                  '"' + String(modifier.requires) + '"'
                )
              );
            }
            break;
          case 'requiresIfExists':
            if (!Array.isArray(modifier.requiresIfExists)) {
              console.error(
                format2(
                  INVALID_MODIFIER_ERROR,
                  modifier.name,
                  '"requiresIfExists"',
                  '"array"',
                  '"' + String(modifier.requiresIfExists) + '"'
                )
              );
            }
            break;
          case 'options':
          case 'data':
            break;
          default:
            console.error(
              'PopperJS: an invalid property has been provided to the "' +
                modifier.name +
                '" modifier, valid properties are ' +
                VALID_PROPERTIES.map(function (s) {
                  return '"' + s + '"';
                }).join(', ') +
                '; but "' +
                key +
                '" was provided.'
            );
        }
        modifier.requires &&
          modifier.requires.forEach(function (requirement) {
            if (
              modifiers.find(function (mod) {
                return mod.name === requirement;
              }) == null
            ) {
              console.error(
                format2(
                  MISSING_DEPENDENCY_ERROR,
                  String(modifier.name),
                  requirement,
                  requirement
                )
              );
            }
          });
      });
  });
}

// node_modules/@popperjs/core/lib/utils/uniqueBy.js
function uniqueBy(arr, fn2) {
  var identifiers = /* @__PURE__ */ new Set();
  return arr.filter(function (item) {
    var identifier = fn2(item);
    if (!identifiers.has(identifier)) {
      identifiers.add(identifier);
      return true;
    }
  });
}

// node_modules/@popperjs/core/lib/utils/mergeByName.js
function mergeByName(modifiers) {
  var merged = modifiers.reduce(function (merged2, current) {
    var existing = merged2[current.name];
    merged2[current.name] = existing
      ? Object.assign({}, existing, current, {
          options: Object.assign({}, existing.options, current.options),
          data: Object.assign({}, existing.data, current.data),
        })
      : current;
    return merged2;
  }, {});
  return Object.keys(merged).map(function (key) {
    return merged[key];
  });
}

// node_modules/@popperjs/core/lib/createPopper.js
var INVALID_ELEMENT_ERROR =
  'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';
var INFINITE_LOOP_ERROR =
  'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';
var DEFAULT_OPTIONS = {
  placement: 'bottom',
  modifiers: [],
  strategy: 'absolute',
};
function areValidElements() {
  for (
    var _len = arguments.length, args = new Array(_len), _key = 0;
    _key < _len;
    _key++
  ) {
    args[_key] = arguments[_key];
  }
  return !args.some(function (element) {
    return !(element && typeof element.getBoundingClientRect === 'function');
  });
}
function popperGenerator(generatorOptions) {
  if (generatorOptions === void 0) {
    generatorOptions = {};
  }
  var _generatorOptions = generatorOptions,
    _generatorOptions$def = _generatorOptions.defaultModifiers,
    defaultModifiers2 =
      _generatorOptions$def === void 0 ? [] : _generatorOptions$def,
    _generatorOptions$def2 = _generatorOptions.defaultOptions,
    defaultOptions =
      _generatorOptions$def2 === void 0
        ? DEFAULT_OPTIONS
        : _generatorOptions$def2;
  return function createPopper2(reference2, popper2, options) {
    if (options === void 0) {
      options = defaultOptions;
    }
    var state = {
      placement: 'bottom',
      orderedModifiers: [],
      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),
      modifiersData: {},
      elements: {
        reference: reference2,
        popper: popper2,
      },
      attributes: {},
      styles: {},
    };
    var effectCleanupFns = [];
    var isDestroyed = false;
    var instance = {
      state,
      setOptions: function setOptions(setOptionsAction) {
        var options2 =
          typeof setOptionsAction === 'function'
            ? setOptionsAction(state.options)
            : setOptionsAction;
        cleanupModifierEffects();
        state.options = Object.assign(
          {},
          defaultOptions,
          state.options,
          options2
        );
        state.scrollParents = {
          reference: isElement(reference2)
            ? listScrollParents(reference2)
            : reference2.contextElement
              ? listScrollParents(reference2.contextElement)
              : [],
          popper: listScrollParents(popper2),
        };
        var orderedModifiers = orderModifiers(
          mergeByName([].concat(defaultModifiers2, state.options.modifiers))
        );
        state.orderedModifiers = orderedModifiers.filter(function (m) {
          return m.enabled;
        });
        if (true) {
          var modifiers = uniqueBy(
            [].concat(orderedModifiers, state.options.modifiers),
            function (_ref) {
              var name = _ref.name;
              return name;
            }
          );
          validateModifiers(modifiers);
          if (getBasePlacement(state.options.placement) === auto) {
            var flipModifier = state.orderedModifiers.find(function (_ref2) {
              var name = _ref2.name;
              return name === 'flip';
            });
            if (!flipModifier) {
              console.error(
                [
                  'Popper: "auto" placements require the "flip" modifier be',
                  'present and enabled to work.',
                ].join(' ')
              );
            }
          }
          var _getComputedStyle = getComputedStyle(popper2),
            marginTop = _getComputedStyle.marginTop,
            marginRight = _getComputedStyle.marginRight,
            marginBottom = _getComputedStyle.marginBottom,
            marginLeft = _getComputedStyle.marginLeft;
          if (
            [marginTop, marginRight, marginBottom, marginLeft].some(
              function (margin) {
                return parseFloat(margin);
              }
            )
          ) {
            console.warn(
              [
                'Popper: CSS "margin" styles cannot be used to apply padding',
                'between the popper and its reference element or boundary.',
                'To replicate margin, use the `offset` modifier, as well as',
                'the `padding` option in the `preventOverflow` and `flip`',
                'modifiers.',
              ].join(' ')
            );
          }
        }
        runModifierEffects();
        return instance.update();
      },
      // Sync update – it will always be executed, even if not necessary. This
      // is useful for low frequency updates where sync behavior simplifies the
      // logic.
      // For high frequency updates (e.g. `resize` and `scroll` events), always
      // prefer the async Popper#update method
      forceUpdate: function forceUpdate() {
        if (isDestroyed) {
          return;
        }
        var _state$elements = state.elements,
          reference3 = _state$elements.reference,
          popper3 = _state$elements.popper;
        if (!areValidElements(reference3, popper3)) {
          if (true) {
            console.error(INVALID_ELEMENT_ERROR);
          }
          return;
        }
        state.rects = {
          reference: getCompositeRect(
            reference3,
            getOffsetParent(popper3),
            state.options.strategy === 'fixed'
          ),
          popper: getLayoutRect(popper3),
        };
        state.reset = false;
        state.placement = state.options.placement;
        state.orderedModifiers.forEach(function (modifier) {
          return (state.modifiersData[modifier.name] = Object.assign(
            {},
            modifier.data
          ));
        });
        var __debug_loops__ = 0;
        for (var index = 0; index < state.orderedModifiers.length; index++) {
          if (true) {
            __debug_loops__ += 1;
            if (__debug_loops__ > 100) {
              console.error(INFINITE_LOOP_ERROR);
              break;
            }
          }
          if (state.reset === true) {
            state.reset = false;
            index = -1;
            continue;
          }
          var _state$orderedModifie = state.orderedModifiers[index],
            fn2 = _state$orderedModifie.fn,
            _state$orderedModifie2 = _state$orderedModifie.options,
            _options =
              _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,
            name = _state$orderedModifie.name;
          if (typeof fn2 === 'function') {
            state =
              fn2({
                state,
                options: _options,
                name,
                instance,
              }) || state;
          }
        }
      },
      // Async and optimistically optimized update – it will not be executed if
      // not necessary (debounced to run at most once-per-tick)
      update: debounce(function () {
        return new Promise(function (resolve) {
          instance.forceUpdate();
          resolve(state);
        });
      }),
      destroy: function destroy() {
        cleanupModifierEffects();
        isDestroyed = true;
      },
    };
    if (!areValidElements(reference2, popper2)) {
      if (true) {
        console.error(INVALID_ELEMENT_ERROR);
      }
      return instance;
    }
    instance.setOptions(options).then(function (state2) {
      if (!isDestroyed && options.onFirstUpdate) {
        options.onFirstUpdate(state2);
      }
    });
    function runModifierEffects() {
      state.orderedModifiers.forEach(function (_ref3) {
        var name = _ref3.name,
          _ref3$options = _ref3.options,
          options2 = _ref3$options === void 0 ? {} : _ref3$options,
          effect4 = _ref3.effect;
        if (typeof effect4 === 'function') {
          var cleanupFn = effect4({
            state,
            name,
            instance,
            options: options2,
          });
          var noopFn = function noopFn2() {};
          effectCleanupFns.push(cleanupFn || noopFn);
        }
      });
    }
    function cleanupModifierEffects() {
      effectCleanupFns.forEach(function (fn2) {
        return fn2();
      });
      effectCleanupFns = [];
    }
    return instance;
  };
}

// node_modules/@popperjs/core/lib/popper.js
var defaultModifiers = [
  eventListeners_default,
  popperOffsets_default,
  computeStyles_default,
  applyStyles_default,
  offset_default,
  flip_default,
  preventOverflow_default,
  arrow_default,
  hide_default,
];
var createPopper = /* @__PURE__ */ popperGenerator({
  defaultModifiers,
});

// src/suggesters/suggest.ts
var wrapAround = (value, size) => {
  return ((value % size) + size) % size;
};
var Suggest = class {
  constructor(owner, containerEl, scope) {
    this.values = [];
    this.suggestions = [];
    this.selectedItem = 0;
    this.owner = owner;
    this.containerEl = containerEl;
    containerEl.on(
      'click',
      '.suggestion-item',
      // @ts-ignore todo: fix later
      this.onSuggestionClick.bind(this)
    );
    containerEl.on(
      'mousemove',
      '.suggestion-item',
      // @ts-ignore todo: fix later
      this.onSuggestionMouseover.bind(this)
    );
    scope.register([], 'ArrowUp', event => {
      if (!event.isComposing) {
        this.setSelectedItem(this.selectedItem - 1, true);
        return false;
      }
    });
    scope.register([], 'ArrowDown', event => {
      if (!event.isComposing) {
        this.setSelectedItem(this.selectedItem + 1, true);
        return false;
      }
    });
    scope.register([], 'Enter', event => {
      if (!event.isComposing) {
        this.useSelectedItem(event);
        return false;
      }
    });
  }
  onSuggestionClick(event, el) {
    event.preventDefault();
    const item = this.suggestions.indexOf(el);
    this.setSelectedItem(item, false);
    this.useSelectedItem(event);
  }
  onSuggestionMouseover(_event, el) {
    const item = this.suggestions.indexOf(el);
    this.setSelectedItem(item, false);
  }
  setSuggestions(values) {
    this.containerEl.empty();
    const suggestionEls = [];
    values.forEach(value => {
      const suggestionEl = this.containerEl.createDiv('suggestion-item');
      this.owner.renderSuggestion(value, suggestionEl);
      suggestionEls.push(suggestionEl);
    });
    this.values = values;
    this.suggestions = suggestionEls;
    this.setSelectedItem(0, false);
  }
  useSelectedItem(event) {
    const currentValue = this.values[this.selectedItem];
    if (currentValue) {
      this.owner.selectSuggestion(currentValue, event);
    }
  }
  setSelectedItem(selectedIndex, scrollIntoView) {
    const normalizedIndex = wrapAround(selectedIndex, this.suggestions.length);
    const prevSelectedSuggestion = this.suggestions[this.selectedItem];
    const selectedSuggestion = this.suggestions[normalizedIndex];
    prevSelectedSuggestion == null
      ? void 0
      : prevSelectedSuggestion.removeClass('is-selected');
    selectedSuggestion == null
      ? void 0
      : selectedSuggestion.addClass('is-selected');
    this.selectedItem = normalizedIndex;
    if (scrollIntoView) {
      selectedSuggestion.scrollIntoView(false);
    }
  }
};
var TextInputSuggest = class {
  constructor(app, inputEl) {
    this.app = app;
    this.inputEl = inputEl;
    this.scope = new import_obsidian.Scope();
    this.suggestEl = createDiv('suggestion-container');
    const suggestion = this.suggestEl.createDiv('suggestion');
    this.suggest = new Suggest(this, suggestion, this.scope);
    this.scope.register([], 'Escape', this.close.bind(this));
    this.inputEl.addEventListener('input', this.onInputChanged.bind(this));
    this.inputEl.addEventListener('focus', this.onInputChanged.bind(this));
    this.inputEl.addEventListener('blur', this.close.bind(this));
    this.suggestEl.on('mousedown', '.suggestion-container', event => {
      event.preventDefault();
    });
  }
  onInputChanged() {
    const inputStr = this.inputEl.value;
    const suggestions = this.getSuggestions(inputStr);
    if (!suggestions) {
      this.close();
      return;
    }
    if (suggestions.length > 0) {
      this.suggest.setSuggestions(suggestions);
      this.open(this.app.dom.appContainerEl, this.inputEl);
    } else {
      this.close();
    }
  }
  open(container, inputEl) {
    this.app.keymap.pushScope(this.scope);
    container.appendChild(this.suggestEl);
    this.popper = createPopper(inputEl, this.suggestEl, {
      placement: 'bottom-start',
      modifiers: [
        {
          name: 'sameWidth',
          enabled: true,
          fn: ({ state, instance }) => {
            const targetWidth = `${state.rects.reference.width}px`;
            if (state.styles.popper.width === targetWidth) {
              return;
            }
            state.styles.popper.width = targetWidth;
            instance.update();
          },
          phase: 'beforeWrite',
          requires: ['computeStyles'],
        },
      ],
    });
  }
  close() {
    this.app.keymap.popScope(this.scope);
    this.suggest.setSuggestions([]);
    if (this.popper) this.popper.destroy();
    this.suggestEl.detach();
  }
};

// src/suggesters/FolderSuggester.ts
var FolderSuggest = class extends TextInputSuggest {
  getSuggestions(inputStr) {
    const abstractFiles = this.app.vault.getAllLoadedFiles();
    const folders = [];
    const lowerCaseInputStr = inputStr.toLowerCase();
    abstractFiles.forEach(folder => {
      if (
        folder instanceof import_obsidian2.TFolder &&
        folder.path.toLowerCase().contains(lowerCaseInputStr)
      ) {
        folders.push(folder);
      }
    });
    return folders;
  }
  renderSuggestion(file, el) {
    el.setText(file.path);
  }
  selectSuggestion(file) {
    this.inputEl.value = file.path;
    this.inputEl.trigger('input');
    this.close();
  }
};

// src/utils.ts
function onlyUniqueArray(value, index, self2) {
  return self2.indexOf(value) === index;
}
function isTFile(value) {
  return 'stat' in value;
}

// node_modules/date-fns/esm/addDays/index.js
function addDays(dirtyDate, dirtyAmount) {
  requiredArgs(2, arguments);
  var date = toDate(dirtyDate);
  var amount = toInteger(dirtyAmount);
  if (isNaN(amount)) {
    return new Date(NaN);
  }
  if (!amount) {
    return date;
  }
  date.setDate(date.getDate() + amount);
  return date;
}

// node_modules/date-fns/esm/addMonths/index.js
function addMonths(dirtyDate, dirtyAmount) {
  requiredArgs(2, arguments);
  var date = toDate(dirtyDate);
  var amount = toInteger(dirtyAmount);
  if (isNaN(amount)) {
    return new Date(NaN);
  }
  if (!amount) {
    return date;
  }
  var dayOfMonth = date.getDate();
  var endOfDesiredMonth = new Date(date.getTime());
  endOfDesiredMonth.setMonth(date.getMonth() + amount + 1, 0);
  var daysInMonth = endOfDesiredMonth.getDate();
  if (dayOfMonth >= daysInMonth) {
    return endOfDesiredMonth;
  } else {
    date.setFullYear(
      endOfDesiredMonth.getFullYear(),
      endOfDesiredMonth.getMonth(),
      dayOfMonth
    );
    return date;
  }
}

// node_modules/date-fns/esm/add/index.js
function add(dirtyDate, duration) {
  requiredArgs(2, arguments);
  if (!duration || typeof duration !== 'object') return new Date(NaN);
  var years = 'years' in duration ? toInteger(duration.years) : 0;
  var months = 'months' in duration ? toInteger(duration.months) : 0;
  var weeks = 'weeks' in duration ? toInteger(duration.weeks) : 0;
  var days = 'days' in duration ? toInteger(duration.days) : 0;
  var hours = 'hours' in duration ? toInteger(duration.hours) : 0;
  var minutes = 'minutes' in duration ? toInteger(duration.minutes) : 0;
  var seconds = 'seconds' in duration ? toInteger(duration.seconds) : 0;
  var date = toDate(dirtyDate);
  var dateWithMonths =
    months || years ? addMonths(date, months + years * 12) : date;
  var dateWithDays =
    days || weeks ? addDays(dateWithMonths, days + weeks * 7) : dateWithMonths;
  var minutesToAdd = minutes + hours * 60;
  var secondsToAdd = seconds + minutesToAdd * 60;
  var msToAdd = secondsToAdd * 1e3;
  var finalDate = new Date(dateWithDays.getTime() + msToAdd);
  return finalDate;
}

// node_modules/date-fns/esm/isAfter/index.js
function isAfter(dirtyDate, dirtyDateToCompare) {
  requiredArgs(2, arguments);
  var date = toDate(dirtyDate);
  var dateToCompare = toDate(dirtyDateToCompare);
  return date.getTime() > dateToCompare.getTime();
}

// src/UpdateAllModal.ts
var import_obsidian3 = require('obsidian');
var createTextSpan = text => {
  const textSpan = document.createElement('span');
  textSpan.setText(text);
  return textSpan;
};
var createBr = () => document.createElement('br');
var UpdateAllModal = class extends import_obsidian3.Modal {
  constructor(app, plugin) {
    super(app);
    this.isOpened = false;
    this.plugin = plugin;
  }
  async onRun() {
    if (!this.divContainer) {
      this.close();
      return;
    }
    const allMdFiles = await this.plugin.getAllFilesPossiblyAffected();
    const progress = document.createElement('progress');
    progress.setAttr('max', allMdFiles.length);
    const fileCounter = document.createElement('span');
    const updateCount = count => {
      progress.setAttr('value', count);
      fileCounter.setText(`${count}/${allMdFiles.length}`);
    };
    updateCount(0);
    const wrapperBar = document.createElement('div');
    wrapperBar.append(progress, fileCounter);
    wrapperBar.addClass('progress-section');
    const header = createTextSpan('Updating files...');
    this.divContainer.replaceChildren(header, wrapperBar);
    if (this.settingsSection) {
      this.contentEl.removeChild(this.settingsSection.settingEl);
    }
    for (let i = 0; i < allMdFiles.length; i++) {
      if (!this.isOpened) {
        new import_obsidian3.Notice('Bulk update for header stopped.', 2e3);
        return;
      }
      updateCount(i + 1);
      await this.plugin.handleFileChange(allMdFiles[i], 'bulk');
    }
    const doneMessage = createTextSpan(
      'Done ! You can safely close this modal.'
    );
    const el = new import_obsidian3.Setting(this.containerEl).addButton(btn => {
      btn.setButtonText('Close').onClick(() => {
        this.close();
      });
    }).settingEl;
    this.divContainer.replaceChildren(doneMessage, createBr(), createBr(), el);
  }
  async onOpen() {
    this.isOpened = true;
    let { contentEl } = this;
    contentEl.addClass('update-time-on-edit--bulk-modal');
    const header = contentEl.createEl('h2', {
      text: `Finding eligible files in the vault...`,
    });
    const allMdFiles = await this.plugin.getAllFilesPossiblyAffected();
    header.setText(`Update all ${allMdFiles.length} files in the vault`);
    const div = contentEl.createDiv();
    this.divContainer = div;
    div.append(
      div.createSpan({
        text: 'This will update all created and updated time on files affected by this plugin',
      }),
      createBr(),
      createBr(),
      div.createSpan({
        text: `WARNING: this action will affect ${allMdFiles.length} in your vault. Make sure you tuned the settings correctly, and make a backup.`,
        cls: 'update-time-on-edit--settings--warn',
      }),
      createBr(),
      createBr()
    );
    this.settingsSection = new import_obsidian3.Setting(contentEl)
      .addButton(btn => {
        btn
          .setButtonText('Run')
          .setCta()
          .onClick(() => {
            this.onRun();
          });
        this.runButton = btn;
      })
      .addButton(btn => {
        this.cancelButton = btn;
        btn.setButtonText('Cancel').onClick(() => {
          this.close();
        });
      });
  }
  onClose() {
    let { contentEl } = this;
    contentEl.empty();
    this.isOpened = false;
  }
};

// src/UpdateAllCacheData.ts
var import_obsidian4 = require('obsidian');
var createTextSpan2 = text => {
  const textSpan = document.createElement('span');
  textSpan.setText(text);
  return textSpan;
};
var createBr2 = () => document.createElement('br');
var UpdateAllCacheData = class extends import_obsidian4.Modal {
  constructor(app, plugin) {
    super(app);
    this.isOpened = false;
    this.plugin = plugin;
  }
  async onRun() {
    if (!this.divContainer) {
      this.close();
      return;
    }
    const allMdFiles = await this.plugin.getAllFilesPossiblyAffected();
    const progress = document.createElement('progress');
    progress.setAttr('max', allMdFiles.length);
    const fileCounter = document.createElement('span');
    const updateCount = count => {
      progress.setAttr('value', count);
      fileCounter.setText(`${count}/${allMdFiles.length}`);
    };
    updateCount(0);
    const wrapperBar = document.createElement('div');
    wrapperBar.append(progress, fileCounter);
    wrapperBar.addClass('progress-section');
    const header = createTextSpan2('Updating cache...');
    this.divContainer.replaceChildren(header, wrapperBar);
    if (this.settingsSection) {
      this.contentEl.removeChild(this.settingsSection.settingEl);
    }
    for (let i = 0; i < allMdFiles.length; i++) {
      if (!this.isOpened) {
        new import_obsidian4.Notice('Bulk update for header stopped.', 2e3);
        return;
      }
      updateCount(i + 1);
      await this.plugin.populateCacheForFile(allMdFiles[i]);
    }
    const doneMessage = createTextSpan2(
      'Done ! You can safely close this modal.'
    );
    const el = new import_obsidian4.Setting(this.containerEl).addButton(btn => {
      btn.setButtonText('Close').onClick(() => {
        this.close();
      });
    }).settingEl;
    this.divContainer.replaceChildren(
      doneMessage,
      createBr2(),
      createBr2(),
      el
    );
  }
  async onOpen() {
    this.isOpened = true;
    let { contentEl } = this;
    contentEl.addClass('update-time-on-edit--bulk-modal');
    const header = contentEl.createEl('h2', {
      text: `Finding eligible files in the vault...`,
    });
    const allMdFiles = await this.plugin.getAllFilesPossiblyAffected();
    header.setText(`Create all ${allMdFiles.length} files in the hash cache`);
    const div = contentEl.createDiv();
    this.divContainer = div;
    div.append(
      div.createSpan({
        text: 'This will update all cache data on files affected by this plugin',
      }),
      createBr2(),
      createBr2()
    );
    this.settingsSection = new import_obsidian4.Setting(contentEl)
      .addButton(btn => {
        btn
          .setButtonText('Run')
          .setCta()
          .onClick(() => {
            this.onRun();
          });
        this.runButton = btn;
      })
      .addButton(btn => {
        this.cancelButton = btn;
        btn.setButtonText('Cancel').onClick(() => {
          this.close();
        });
      });
  }
  onClose() {
    let { contentEl } = this;
    contentEl.empty();
    this.isOpened = false;
  }
};

// src/Settings.ts
var DEFAULT_SETTINGS = {
  dateFormat: "yyyy-MM-dd'T'HH:mm",
  enableCreateTime: true,
  headerUpdated: 'updated',
  headerCreated: 'created',
  minMinutesBetweenSaves: 1,
  ignoreGlobalFolder: [],
  ignoreCreatedFolder: [],
  enableExperimentalHash: false,
  fileHashMap: {},
};
var UpdateTimeOnEditSettingsTab = class extends import_obsidian5.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  display() {
    let { containerEl } = this;
    containerEl.empty();
    containerEl.createEl('h2', { text: 'Global settings' });
    this.addExcludedFoldersSetting();
    this.addTimeBetweenUpdates();
    this.addDateFormat();
    new import_obsidian5.Setting(this.containerEl)
      .setName('Update all files')
      .setDesc(
        'This plugin will only work on new files, but if you want to update all files in your vault at once, you can do it here.'
      )
      .addButton(cb => {
        cb.setButtonText('Update all files').onClick(() => {
          new UpdateAllModal(this.app, this.plugin).open();
        });
      });
    containerEl.createEl('h2', { text: 'Updated at' });
    this.addFrontMatterUpdated();
    containerEl.createEl('h2', { text: 'Created at' });
    this.addEnableCreated();
    this.addFrontMatterCreated();
    this.addExcludedCreatedFoldersSetting();
    containerEl.createEl('h2', { text: 'Experimental settings' });
    new import_obsidian5.Setting(this.containerEl)
      .setName('Enable hash matcher')
      .setDesc(
        'Using a hash system to prevent too many updates happening, especially with sync.'
      )
      .addToggle(cb => {
        var _a;
        return cb
          .setValue(
            (_a = this.plugin.settings.enableExperimentalHash) != null
              ? _a
              : false
          )
          .onChange(async newValue => {
            this.plugin.settings.enableExperimentalHash = newValue;
            await this.saveSettings();
          });
      })
      .addButton(cb =>
        cb.setButtonText('Fill initial cache').onClick(() => {
          new UpdateAllCacheData(this.app, this.plugin).open();
        })
      );
  }
  async saveSettings() {
    await this.plugin.saveSettings();
  }
  addDateFormat() {
    this.createDateFormatEditor({
      getValue: () => this.plugin.settings.dateFormat,
      name: 'Date format',
      description: 'The date format for read and write',
      setValue: newValue => (this.plugin.settings.dateFormat = newValue),
    });
  }
  createDateFormatEditor({ description, name, getValue, setValue }) {
    const createDoc = () => {
      const descr = document.createDocumentFragment();
      descr.append(
        description,
        descr.createEl('br'),
        'Check ',
        descr.createEl('a', {
          href: 'https://date-fns.org/v2.25.0/docs/format',
          text: 'date-fns documentation',
        }),
        descr.createEl('br'),
        `Currently: ${format(new Date(), getValue())}`,
        descr.createEl('br'),
        `Obsidian default format for date properties: yyyy-MM-dd'T'HH:mm`
      );
      return descr;
    };
    let dformat = new import_obsidian5.Setting(this.containerEl)
      .setName(name)
      .setDesc(createDoc())
      .addText(text =>
        text
          .setPlaceholder(DEFAULT_SETTINGS.dateFormat)
          .setValue(getValue())
          .onChange(async value => {
            setValue(value);
            dformat.setDesc(createDoc());
            await this.saveSettings();
          })
      );
  }
  addTimeBetweenUpdates() {
    new import_obsidian5.Setting(this.containerEl)
      .setName('Minimum number of minutes between update')
      .setDesc('If your files are updating too often, increase this.')
      .addSlider(slider =>
        slider
          .setLimits(1, 30, 1)
          .setValue(this.plugin.settings.minMinutesBetweenSaves)
          .onChange(async value => {
            this.plugin.settings.minMinutesBetweenSaves = value;
            await this.saveSettings();
          })
          .setDynamicTooltip()
      );
  }
  addEnableCreated() {
    new import_obsidian5.Setting(this.containerEl)
      .setName('Enable the created front matter key update')
      .setDesc('Currently, it is set to now if not present')
      .addToggle(toggle =>
        toggle
          .setValue(this.plugin.settings.enableCreateTime)
          .onChange(async newValue => {
            this.plugin.settings.enableCreateTime = newValue;
            await this.saveSettings();
            this.display();
          })
      );
  }
  addFrontMatterUpdated() {
    new import_obsidian5.Setting(this.containerEl)
      .setName('Front matter updated name')
      .setDesc('The key in the front matter yaml for the update time.')
      .addText(text => {
        var _a;
        return text
          .setPlaceholder('updated')
          .setValue((_a = this.plugin.settings.headerUpdated) != null ? _a : '')
          .onChange(async value => {
            this.plugin.settings.headerUpdated = value;
            await this.saveSettings();
          });
      });
  }
  addFrontMatterCreated() {
    if (!this.plugin.settings.enableCreateTime) {
      return;
    }
    new import_obsidian5.Setting(this.containerEl)
      .setName('Front matter created name')
      .setDesc('The key in the front matter yaml for the creation time')
      .addText(text => {
        var _a;
        return text
          .setPlaceholder('updated')
          .setValue((_a = this.plugin.settings.headerCreated) != null ? _a : '')
          .onChange(async value => {
            this.plugin.settings.headerCreated = value;
            await this.saveSettings();
          });
      });
  }
  addExcludedCreatedFoldersSetting() {
    var _a;
    if (!this.plugin.settings.enableCreateTime) {
      return;
    }
    this.doSearchAndRemoveList({
      currentList:
        (_a = this.plugin.settings.ignoreCreatedFolder) != null ? _a : [],
      setValue: async newValue => {
        this.plugin.settings.ignoreCreatedFolder = newValue;
      },
      name: 'Folder(s) to exclude for updating the created property',
      description:
        'Any file updated in this folder will not trigger a created update.',
    });
  }
  addExcludedFoldersSetting() {
    this.doSearchAndRemoveList({
      currentList: this.plugin.getIgnoreFolders(),
      setValue: async newValue => {
        this.plugin.settings.ignoreGlobalFolder = newValue;
      },
      name: 'Folder to exclude of all updates',
      description:
        'Any file updated in this folder will not trigger an updated and created update.',
    });
  }
  doSearchAndRemoveList({ currentList, setValue, description, name }) {
    let searchInput;
    new import_obsidian5.Setting(this.containerEl)
      .setName(name)
      .setDesc(description)
      .addSearch(cb => {
        searchInput = cb;
        new FolderSuggest(this.app, cb.inputEl);
        cb.setPlaceholder('Example: folder1/folder2');
        cb.containerEl.addClass('time_search');
      })
      .addButton(cb => {
        cb.setIcon('plus');
        cb.setTooltip('Add folder');
        cb.onClick(async () => {
          if (!searchInput) {
            return;
          }
          const newFolder = searchInput.getValue();
          await setValue([...currentList, newFolder].filter(onlyUniqueArray));
          await this.saveSettings();
          searchInput.setValue('');
          this.display();
        });
      });
    currentList.forEach(ignoreFolder =>
      new import_obsidian5.Setting(this.containerEl)
        .setName(ignoreFolder)
        .addButton(button =>
          button.setButtonText('Remove').onClick(async () => {
            await setValue(currentList.filter(value => value !== ignoreFolder));
            await this.saveSettings();
            this.display();
          })
        )
    );
  }
};

// src/main.ts
var import_js_sha256 = __toESM(require_sha256());
var UpdateTimeOnSavePlugin = class extends import_obsidian6.Plugin {
  parseDate(input) {
    if (typeof input === 'string') {
      try {
        const parsedDate = parse(input, this.settings.dateFormat, new Date());
        if (isNaN(parsedDate.getTime())) {
          this.log('NAN DATE', parsedDate);
          return void 0;
        }
        return parsedDate;
      } catch (e) {
        console.error(e);
        return void 0;
      }
    }
    return new Date(input);
  }
  formatDate(input) {
    return format(input, this.settings.dateFormat);
  }
  async onload() {
    this.log('loading plugin IN DEV');
    await this.loadSettings();
    this.setupOnEditHandler();
    this.addSettingTab(new UpdateTimeOnEditSettingsTab(this.app, this));
  }
  // Workaround since the first version of the plugin had a single string for
  // the option
  getIgnoreFolders() {
    var _a;
    if (typeof this.settings.ignoreGlobalFolder === 'string') {
      return [this.settings.ignoreGlobalFolder];
    }
    return (_a = this.settings.ignoreGlobalFolder) != null ? _a : [];
  }
  hashString(str) {
    return (0, import_js_sha256.sha256)(str);
  }
  async shouldFileBeIgnored(file) {
    if (!file.path) {
      return true;
    }
    if (!file.path.endsWith('.md')) {
      return true;
    }
    const fileContent = (await this.app.vault.read(file)).trim();
    const sha = this.hashString(fileContent);
    if (fileContent.length === 0) {
      return true;
    }
    if (this.settings.enableExperimentalHash) {
      const maybeHash = this.settings.fileHashMap[file.path];
      if (maybeHash) {
        const sha2 = this.hashString(fileContent);
        if (sha2 === maybeHash) {
          this.log('Ignoring file because, sha same');
          return true;
        }
      }
    }
    const isExcalidrawFile = this.isExcalidrawFile(file);
    if (isExcalidrawFile) {
      return true;
    }
    const ignores = this.getIgnoreFolders();
    if (!ignores) {
      return false;
    }
    return ignores.some(ignoreItem => file.path.startsWith(ignoreItem));
  }
  shouldIgnoreCreated(path) {
    if (!this.settings.enableCreateTime) {
      return true;
    }
    return (this.settings.ignoreCreatedFolder || []).some(itemIgnore =>
      path.startsWith(itemIgnore)
    );
  }
  shouldUpdateValue(currentMtime, updateHeader) {
    const nextUpdate = add(updateHeader, {
      minutes: this.settings.minMinutesBetweenSaves,
    });
    return isAfter(currentMtime, nextUpdate);
  }
  isExcalidrawFile(file) {
    const ea =
      //@ts-expect-error this is comming from global context, injected by Excalidraw
      typeof ExcalidrawAutomate === 'undefined'
        ? void 0
        : //@ts-expect-error this is comming from global context, injected by Excalidraw
          ExcalidrawAutomate;
    return ea ? ea.isExcalidrawFile(file) : false;
  }
  async getAllFilesPossiblyAffected() {
    const allFiles = this.app.vault.getMarkdownFiles();
    const result = [];
    for (const file of allFiles) {
      if (!(await this.shouldFileBeIgnored(file))) {
        result.push(file);
      }
    }
    return result;
  }
  async populateCacheForFile(file) {
    const fileContent = (await this.app.vault.read(file)).trim();
    const sha = this.hashString(fileContent);
    this.settings.fileHashMap[file.path] = sha;
    await this.saveSettings();
  }
  async handleFileChange(file, triggerSource) {
    if (!isTFile(file)) {
      return { status: 'ignored' };
    }
    if (await this.shouldFileBeIgnored(file)) {
      return { status: 'ignored' };
    }
    try {
      await this.app.fileManager.processFrontMatter(
        file,
        frontmatter => {
          this.log('current metadata: ', frontmatter);
          this.log('current stat: ', file.stat);
          const updatedKey = this.settings.headerUpdated;
          const createdKey = this.settings.headerCreated;
          const mTime = this.parseDate(file.stat.mtime);
          const cTime = this.parseDate(file.stat.ctime);
          if (!mTime || !cTime) {
            this.log('Something wrong happen, skipping');
            return;
          }
          if (!frontmatter[createdKey]) {
            if (!this.shouldIgnoreCreated(file.path)) {
              frontmatter[createdKey] = this.formatDate(cTime);
            }
          }
          const currentMTimeOnFile = this.parseDate(frontmatter[updatedKey]);
          if (!frontmatter[updatedKey] || !currentMTimeOnFile) {
            this.log('Update updatedKey');
            frontmatter[updatedKey] = this.formatDate(mTime);
            return;
          }
          if (this.shouldUpdateValue(mTime, currentMTimeOnFile)) {
            frontmatter[updatedKey] = this.formatDate(mTime);
            this.log('Update updatedKey');
            return;
          }
          this.log('Skipping updateKey');
        },
        { ctime: file.stat.ctime, mtime: file.stat.mtime }
      );
      await this.populateCacheForFile(file);
    } catch (e) {
      if ((e == null ? void 0 : e.name) === 'YAMLParseError') {
        const errorMessage = `Update time on edit failed
Malformed frontamtter on this file : ${file.path}

${e.message}`;
        new import_obsidian6.Notice(errorMessage, 4e3);
        console.error(errorMessage);
        return {
          status: 'error',
          error: e,
        };
      }
    }
    return {
      status: 'ok',
    };
  }
  setupOnEditHandler() {
    this.log('Setup handler');
    this.registerEvent(
      this.app.vault.on('modify', file => {
        this.log('TRIGGER FROM MODIFY');
        return this.handleFileChange(file, 'modify');
      })
    );
    this.registerEvent(
      this.app.vault.on('rename', (file, oldPath) => {
        const hash3 = this.settings.fileHashMap[oldPath];
        if (!hash3) {
          return;
        }
        this.settings.fileHashMap[file.path] = hash3;
        delete this.settings.fileHashMap[oldPath];
        this.saveSettings();
      })
    );
    this.registerEvent(
      this.app.vault.on('delete', async file => {
        const sha = this.settings.fileHashMap[file.path];
        if (!sha) {
          return;
        }
        delete this.settings.fileHashMap[file.path];
        this.saveSettings();
      })
    );
  }
  onunload() {
    this.log('unloading Update time on edit plugin');
  }
  log(...data) {
    if (true) {
      return;
    }
    console.log('[UTOE]:', ...data);
  }
  async loadSettings() {
    this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
  }
  async saveSettings() {
    await this.saveData(this.settings);
  }
};
/*! Bundled license information:

js-sha256/src/sha256.js:
  (**
   * [js-sha256]{@link https://github.com/emn178/js-sha256}
   *
   * @version 0.10.1
   * <AUTHOR> Yi-Cyuan [<EMAIL>]
   * @copyright Chen, Yi-Cyuan 2014-2023
   * @license MIT
   *)
*/

/* nosourcemap */

<script src=" https://fastly.jsdelivr.net/npm/luxon@3.2.1/build/global/luxon.min.js "></script>
<script defer>
  TIMESTAMP_FORMAT = "{{meta.timestampSettings.timestampFormat}}";
  document.querySelectorAll('.human-date').forEach(function (el) {
    date = el.getAttribute('data-date') || el.innerText
    parsed_date = luxon.DateTime.fromISO(date)
    if (parsed_date.invalid != null){
      // Date cannot be parsed
      parsed_date = luxon.DateTime.fromSQL(date)
    }
    if (parsed_date.invalid != null){
      // Date still cannot be parsed
      parsed_date = luxon.DateTime.fromHTML(date)
    }
    el.innerHTML = parsed_date.toFormat(TIMESTAMP_FORMAT);
  })
</script>
#!/usr/bin/env python3
"""
Script para remover o fundo da imagem do logo PVP
Mantém apenas os elementos principais: as letras P e o raio no centro
Versão corrigida e otimizada
"""

import os
import sys
from pathlib import Path

try:
    from rembg import remove
    from PIL import Image
except ImportError as e:
    print(f"Erro: Biblioteca não encontrada. Instalando dependências...")
    print(f"Execute: pip install rembg pillow")
    sys.exit(1)

def remove_background_from_image(input_path, output_path=None):
    """
    Remove o fundo da imagem usando a biblioteca rembg
    
    Args:
        input_path (str): Caminho para a imagem de entrada
        output_path (str): Caminho para salvar a imagem sem fundo (opcional)
    """
    
    # Verifica se o arquivo de entrada existe
    if not os.path.exists(input_path):
        print(f"Erro: Arquivo não encontrado: {input_path}")
        return False
    
    # Define o caminho de saída se não fornecido
    if output_path is None:
        input_file = Path(input_path)
        output_path = input_file.parent / f"{input_file.stem}_sem_fundo.png"
    
    try:
        print(f"Processando imagem: {input_path}")
        print("Removendo fundo...")
        
        # Lê a imagem de entrada
        with open(input_path, "rb") as input_file:
            input_data = input_file.read()
        
        # Remove o fundo (versão simples e confiável)
        output_data = remove(input_data)
        
        # Salva a imagem sem fundo
        with open(output_path, "wb") as output_file:
            output_file.write(output_data)
        
        print(f"✅ Fundo removido com sucesso!")
        print(f"📁 Imagem salva em: {output_path}")
        
        # Mostra informações sobre a imagem processada
        with Image.open(output_path) as img:
            print(f"📏 Dimensões: {img.size[0]}x{img.size[1]} pixels")
            print(f"🎨 Modo: {img.mode}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao processar a imagem: {str(e)}")
        return False

def main():
    """Função principal do script"""
    
    print("🎨 Removedor de Fundo - Logo PVP (Versão Corrigida)")
    print("=" * 50)
    
    # Caminho para a imagem do logo PVP
    input_image = "src/site/assets/favicon/pvp.jpg"
    
    # Verifica se o arquivo existe
    if not os.path.exists(input_image):
        print(f"❌ Arquivo não encontrado: {input_image}")
        print("Verifique se o caminho está correto.")
        return
    
    # Remove o fundo
    success = remove_background_from_image(input_image)
    
    if success:
        print("\n🎉 Processo concluído!")
        print("A imagem sem fundo está pronta para ser usada como logo.")
        print("\n💡 Características:")
        print("- Fundo transparente preservado")
        print("- Todos os detalhes importantes mantidos")
        print("- Qualidade original preservada")
        print("- Formato PNG com transparência")
    else:
        print("\n❌ Falha no processamento. Verifique os erros acima.")

if __name__ == "__main__":
    main() 
const projectsData = require('./src/site/_data/projects.js');

console.log('=== DEBUG PROJETOS ===');

// Verificar todos os projetos
console.log('\n1. Todos os projetos:');
Object.keys(projectsData.projects).forEach(id => {
  const project = projectsData.projects[id];
  console.log(`- ${id}: ${project.title} (${project.category})`);
});

// Verificar projetos residenciais
console.log('\n2. Projetos residenciais:');
Object.values(projectsData.projects)
  .filter(project => project.category === 'residencial')
  .forEach(project => {
    console.log(`- ${project.id}: ${project.title}`);
    console.log(`  coverImage: ${project.coverImage || 'NÃO DEFINIDA'}`);
    console.log(`  images: ${project.images ? project.images.length : 0} imagens`);
  });

// Verificar projeto GT especificamente
console.log('\n3. Projeto GT:');
const gtProject = projectsData.projects['casa-gt'];
if (gtProject) {
  console.log(`- ID: ${gtProject.id}`);
  console.log(`- Título: ${gtProject.title}`);
  console.log(`- Categoria: ${gtProject.category}`);
  console.log(`- CoverImage: ${gtProject.coverImage || 'NÃO DEFINIDA'}`);
  console.log(`- Images: ${gtProject.images ? gtProject.images.length : 0} imagens`);
} else {
  console.log('❌ Projeto GT não encontrado!');
} 
---
layout: layouts/base.njk
---

<!-- Hero Section -->
<section class="relative py-12 sm:py-16 lg:py-20 overflow-hidden min-h-[60vh] sm:min-h-[70vh] lg:min-h-[80vh]">
  {% if project.coverImage %}
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat" style="background-image: url('{{ project.coverImage }}'); background-size: cover;">
    <div class="absolute inset-0 bg-black bg-opacity-50"></div>
  </div>
  {% else %}
  <div class="absolute inset-0 bg-gradient-to-br from-primary-50 to-secondary-50"></div>
  {% endif %}
  
  <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
    <div class="text-center" data-aos="fade-up">
      <h1 class="text-4xl lg:text-5xl font-bold text-white mb-6">
        {{ project.title }}
      </h1>
      <p class="text-xl text-white max-w-3xl mx-auto mb-8">
        {{ project.subtitle }}
      </p>
      <p class="text-lg text-white max-w-4xl mx-auto mb-8">
        {{ project.description }}
      </p>
      
      <!-- Tags Extras -->
      {% if project.tags_extra %}
      <div class="flex flex-wrap justify-center gap-2 mb-8">
        {% for tag in project.tags_extra %}
        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white bg-opacity-20 text-white border border-white border-opacity-30">
          {{ tag }}
        </span>
        {% endfor %}
      </div>
      {% endif %}
      
      <!-- Breadcrumb -->
      <nav class="flex justify-center mb-8" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2 text-sm text-white">
          <li><a href="/" class="hover:text-primary-300 transition-colors duration-200">Home</a></li>
          <li class="flex items-center">
            <svg class="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <a href="/projetos-dedicada" class="hover:text-primary-300 transition-colors duration-200">Projetos</a>
          </li>
          <li class="flex items-center">
            <svg class="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <a href="/projetos/categoria/{{ project.category }}" class="hover:text-primary-300 transition-colors duration-200">{{ project.category | capitalize }}</a>
          </li>
          <li class="flex items-center">
            <svg class="w-4 h-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-primary-300 font-medium">{{ project.title }}</span>
          </li>
        </ol>
      </nav>
    </div>
  </div>
</section>

<!-- Informações do Projeto -->
<section class="py-16 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-6xl mx-auto">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        
        <!-- Coluna Principal - Detalhes do Projeto -->
        <div class="lg:col-span-2">
          
          <!-- Informações Básicas -->
          <div class="bg-gray-50 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Informações do Projeto</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 class="font-semibold text-gray-900 mb-2">Cliente</h3>
                <p class="text-gray-700">{{ project.client }}</p>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900 mb-2">Localização</h3>
                <p class="text-gray-700">{{ project.location }}</p>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900 mb-2">Ano</h3>
                <p class="text-gray-700">{{ project.year }}</p>
              </div>
              {% if project.details and project.details.area %}
              <div>
                <h3 class="font-semibold text-gray-900 mb-2">Área</h3>
                <p class="text-gray-700">{{ project.details.area }}</p>
              </div>
              {% endif %}
              {% if project.details and project.details.pavimentos %}
              <div>
                <h3 class="font-semibold text-gray-900 mb-2">Pavimentos</h3>
                <p class="text-gray-700">{{ project.details.pavimentos }}</p>
              </div>
              {% endif %}
              {% if project.details and project.details.ferramentas %}
              <div>
                <h3 class="font-semibold text-gray-900 mb-2">Ferramentas</h3>
                <p class="text-gray-700">{{ project.details.ferramentas | join(', ') }}</p>
              </div>
              {% endif %}
            </div>
          </div>

          <!-- Desafios do Projeto -->
          {% if project.challenges %}
          <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Desafios do Projeto</h2>
            <div class="space-y-4">
              {% for challenge in project.challenges %}
              <div class="flex items-start">
                <div class="flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mr-3 mt-1">
                  <svg class="w-4 h-4 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <p class="text-gray-700">{{ challenge }}</p>
              </div>
              {% endfor %}
            </div>
          </div>
          {% endif %}

          <!-- Conteúdo Rico do Projeto -->
          {% if project.richContent %}
          <div class="prose prose-lg max-w-none mb-8">
            {{ project.richContent | safe }}
          </div>
          {% endif %}

          <!-- Desafios, Soluções e Resultados -->
          {% if project.challenges %}
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Desafios</h2>
            <ul class="space-y-3">
              {% for challenge in project.challenges %}
              <li class="flex items-start">
                <span class="text-primary-600 mr-3 mt-1">•</span>
                <span class="text-gray-700">{{ challenge }}</span>
              </li>
              {% endfor %}
            </ul>
          </div>
          {% endif %}

          {% if project.solutions %}
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Soluções</h2>
            <ul class="space-y-3">
              {% for solution in project.solutions %}
              <li class="flex items-start">
                <span class="text-primary-600 mr-3 mt-1">•</span>
                <span class="text-gray-700">{{ solution }}</span>
              </li>
              {% endfor %}
            </ul>
          </div>
          {% endif %}

          {% if project.results %}
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Resultados</h2>
            <ul class="space-y-3">
              {% for result in project.results %}
              <li class="flex items-start">
                <span class="text-primary-600 mr-3 mt-1">•</span>
                <span class="text-gray-700">{{ result }}</span>
              </li>
              {% endfor %}
            </ul>
          </div>
          {% endif %}

        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          


          <!-- Documentos -->
          {% if project.documents %}
          <div class="bg-gray-50 rounded-lg p-6">
            <h3 class="text-xl font-bold text-gray-900 mb-4">Documentos</h3>
            <div class="space-y-6">
              {% for document in project.documents %}
              <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <div class="p-4 border-b border-gray-200">
                  <h4 class="font-semibold text-gray-900">{{ document.name }}</h4>
                </div>
                <div class="relative pdf-container">
                  <embed src="{{ document.url }}#toolbar=0&navpanes=0&scrollbar=0" 
                         type="application/pdf" 
                         class="w-full h-[500px]"
                         style="pointer-events: auto;">
                  <div class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 opacity-0 hover:opacity-100 transition-opacity duration-300">
                    <div class="bg-white rounded-full p-3 shadow-lg animate-pulse">
                      <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
              {% endfor %}
            </div>
          </div>
          {% endif %}

        </div>
      </div>
    </div>
  </div>
</section>





<!-- Call to Action -->
<section class="py-16 bg-primary-600">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto text-center">
      <h2 class="text-3xl font-bold text-white mb-6">Interessado em um projeto similar?</h2>
      <p class="text-xl text-primary-100 mb-8">Entre em contato conosco para discutir suas necessidades</p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="/contato" class="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200">
          Solicitar Orçamento
        </a>
        <a href="/projetos-dedicada" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors duration-200">
          Ver Mais Projetos
        </a>
      </div>
    </div>
  </div>
</section> 
// Teste da função carouselProjects
const projectsData = require('./src/site/_data/projects.js');

function testCarouselProjects() {
  const allProjects = Object.values(projectsData.projects);
  
  console.log('Total de projetos:', allProjects.length);
  
  // Separar projetos por categoria
  const predialProjects = allProjects.filter(p => p.category === 'predial');
  const comercialProjects = allProjects.filter(p => p.category === 'comercial');
  const residencialProjects = allProjects.filter(p => p.category === 'residencial');
  
  console.log('Projetos Prediais:', predialProjects.length);
  console.log('Projetos Comerciais:', comercialProjects.length);
  console.log('Projetos Residenciais:', residencialProjects.length);
  
  // Selecionar projetos aleatórios de cada categoria
  const getRandomProjects = (projects, count) => {
    const shuffled = projects.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  };
  
  let selectedProjects = [
    ...getRandomProjects(predialProjects, 2),
    ...getRandomProjects(comercialProjects, 2),
    ...getRandomProjects(residencialProjects, 2)
  ];
  
  console.log('Projetos selecionados:', selectedProjects.length);
  
  selectedProjects.forEach((project, index) => {
    console.log(`${index + 1}. ${project.title} (${project.category})`);
  });
  
  return selectedProjects;
}

testCarouselProjects(); 
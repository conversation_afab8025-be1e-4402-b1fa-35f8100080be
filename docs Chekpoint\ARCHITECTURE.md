# 🏗️ Arquitetura do Sistema - Portfólio Engenharia

## 📋 Visão Geral da Arquitetura

### Filosofia de Design

O portfólio segue os princípios de **arquitetura modular** e **separation of concerns**, onde cada componente tem uma responsabilidade específica e bem definida. O sistema é construído como um **site estático** para máxima performance e simplicidade de deploy.

### Princípios Arquiteturais

1. **Modularidade**: Componentes independentes e reutilizáveis
2. **Escalabilidade**: Estrutura que cresce com o projeto
3. **Manutenibilidade**: Código limpo e bem documentado
4. **Performance**: Otimização em todos os níveis
5. **Acessibilidade**: Design inclusivo desde o início

## 🏛️ Arquitetura de Alto Nível

```
┌─────────────────────────────────────────────────────────────┐
│                    Camada de Apresentação                   │
├─────────────────────────────────────────────────────────────┤
│  HTML Templates (Nunjucks)  │  CSS (Tailwind)  │  JS      │
├─────────────────────────────────────────────────────────────┤
│                    Camada de Processamento                  │
├─────────────────────────────────────────────────────────────┤
│  Eleventy (11ty) - Static Site Generator                  │
├─────────────────────────────────────────────────────────────┤
│                    Camada de Dados                         │
├─────────────────────────────────────────────────────────────┤
│  JSON/JS Data  │  Markdown Files  │  Static Assets       │
└─────────────────────────────────────────────────────────────┘
```

## 📁 Estrutura de Diretórios Detalhada

### Raiz do Projeto
```
portfolio-engenharia/
├── .github/                    # CI/CD e templates GitHub
├── .eleventy.js               # Configuração principal do Eleventy
├── tailwind.config.js         # Configuração do Tailwind CSS
├── package.json               # Dependências e scripts
├── README.md                  # Documentação principal
├── TECHNICAL_DOCS.md          # Documentação técnica
├── ARCHITECTURE.md            # Este arquivo
├── MAINTENANCE_CHECKLIST.md   # Checklist de manutenção
└── dist/                      # Build de produção (gerado)
```

### Código Fonte (`src/`)
```
src/
├── site/                      # Conteúdo principal do Eleventy
│   ├── _data/                 # Dados estruturados
│   │   ├── projects.js        # Dados dos projetos
│   │   └── projectPages.js    # Sistema de conteúdo rico
│   ├── _includes/             # Componentes reutilizáveis
│   │   ├── layouts/           # Layouts base e específicos
│   │   │   ├── base.njk       # Layout base com HTML5
│   │   │   ├── project.njk    # Layout de páginas de projeto
│   │   │   └── components/    # Componentes específicos
│   │   └── partials/          # Partes reutilizáveis
│   ├── assets/                # Ativos estáticos
│   │   ├── pdfs/              # Documentos PDF
│   │   └── imagens/           # Imagens e renders
│   ├── notes/                 # Conteúdo markdown rico
│   │   ├── SEFERIN GP/        # Projetos por cliente
│   │   ├── SEFERIN CD/
│   │   ├── REVIT/
│   │   └── ...
│   ├── styles/                # Estilos CSS/SCSS
│   │   ├── input.css          # Entrada do Tailwind
│   │   └── components/        # Estilos específicos
│   ├── scripts/               # JavaScript customizado
│   ├── legal/                 # Páginas legais
│   ├── img/                   # Imagens otimizadas
│   └── [páginas].njk         # Páginas individuais
└── helpers/                   # Utilitários e helpers
```

## 🔄 Fluxo de Dados

### 1. Entrada de Dados
```mermaid
graph TD
    A[Dados JSON] --> B[Eleventy Data Layer]
    C[Markdown Files] --> B
    D[Static Assets] --> B
    B --> E[Template Processing]
    E --> F[HTML Output]
```

### 2. Processamento de Conteúdo
```mermaid
graph LR
    A[projects.js] --> B[projectPages.js]
    C[Markdown Files] --> B
    B --> D[Eleventy Pagination]
    D --> E[project.njk Template]
    E --> F[HTML Pages]
```

### 3. Sistema de Build
```mermaid
graph TD
    A[Source Files] --> B[Eleventy Build]
    C[Tailwind CSS] --> B
    D[Image Optimization] --> B
    B --> E[HTML Output]
    B --> F[CSS Output]
    B --> G[Optimized Assets]
    E --> H[Netlify Deploy]
    F --> H
    G --> H
```

## 🧩 Componentes do Sistema

### 1. Sistema de Dados (`_data/`)

#### `projects.js`
```javascript
// Estrutura de dados centralizada
module.exports = {
  projects: {
    'project-id': {
      // Metadados básicos
      id: 'project-id',
      title: 'Project Title',
      subtitle: 'Project Subtitle',
      category: 'residencial|comercial|predial',
      
      // Informações detalhadas
      client: 'Client Name',
      location: 'Location',
      year: 2023,
      description: 'Project description...',
      
      // Estrutura de dados
      details: { area: '280 m²', pavimentos: 3 },
      challenges: ['Challenge 1', 'Challenge 2'],
      solutions: ['Solution 1', 'Solution 2'],
      results: ['Result 1', 'Result 2'],
      
      // Assets
      images: [
        { src: '/assets/imagens/image.jpg', alt: 'Description' }
      ],
      documents: [
        { name: 'Planta Elétrica', url: '/assets/pdfs/planta.pdf' }
      ],
      
      // Métricas
      metrics: {
        complexity: 7,
        area: 280,
        duration: '2 meses'
      }
    }
  }
};
```

#### `projectPages.js`
```javascript
// Sistema de integração de conteúdo rico
const projects = require('./projects.js');
const fs = require('fs');
const path = require('path');
const MarkdownIt = require('markdown-it');

// Configuração do markdown-it
const md = new MarkdownIt({
  html: true,
  breaks: true,
  linkify: true
});

// Mapeamento de projetos para arquivos markdown
const markdownFiles = {
  'casa-gp': 'notes/SEFERIN GP/projeto-eletrico-gp_gp.md',
  'casa-cd': 'notes/SEFERIN CD/projeto-eletrico-cd_cd.md',
  // ... outros projetos
};

// Função de carregamento de conteúdo rico
function loadRichContent(projectId) {
  const markdownPath = markdownFiles[projectId];
  if (!markdownPath) return null;
  
  const fullPath = path.join(__dirname, '..', markdownPath);
  if (!fs.existsSync(fullPath)) return null;
  
  const content = fs.readFileSync(fullPath, 'utf8');
  const contentWithoutFrontmatter = content.replace(/^---[\s\S]*?---\s*/, '');
  const htmlContent = md.render(contentWithoutFrontmatter);
  
  return htmlContent;
}

// Processamento dos dados
const projectList = Object.values(projects.projects).map(project => {
  const richContent = loadRichContent(project.id);
  if (richContent) {
    project.richContent = richContent;
  }
  return project;
});

module.exports = { projectList };
```

### 2. Sistema de Templates (`_includes/`)

#### `layouts/base.njk`
```html
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{ title }} | Portfólio Engenharia</title>
  <meta name="description" content="{{ description }}">
  
  <!-- SEO Meta Tags -->
  <meta property="og:title" content="{{ title }}">
  <meta property="og:description" content="{{ description }}">
  <meta property="og:image" content="{{ ogImage }}">
  
  <!-- Styles -->
  <link rel="stylesheet" href="/styles/style.css">
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg">
</head>
<body class="bg-gray-50">
  <!-- Header -->
  {% include "partials/header.njk" %}
  
  <!-- Main Content -->
  <main>
    {{ content | safe }}
  </main>
  
  <!-- Footer -->
  {% include "partials/footer.njk" %}
  
  <!-- Scripts -->
  <script src="/scripts/main.js"></script>
</body>
</html>
```

#### `layouts/project.njk`
```html
{% extends "layouts/base.njk" %}

{% block content %}
<!-- Hero Section -->
<section class="bg-gradient-to-br from-primary-50 to-secondary-50 py-16">
  <div class="container mx-auto px-4">
    <h1 class="text-4xl font-bold text-gray-900 mb-4">{{ project.title }}</h1>
    <p class="text-xl text-gray-600">{{ project.subtitle }}</p>
  </div>
</section>

<!-- Project Details -->
<section class="py-12">
  <div class="container mx-auto px-4 grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2">
      <!-- Rich Content -->
      {% if project.richContent %}
      <div class="prose prose-lg max-w-none mb-8">
        {{ project.richContent | safe }}
      </div>
      {% endif %}
      
      <!-- Project Information -->
      <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Informações do Projeto</h2>
        <!-- Project details... -->
      </div>
    </div>
    
    <!-- Sidebar -->
    <div class="lg:col-span-1">
      <!-- Metrics -->
      <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
        <h3 class="text-xl font-bold text-gray-900 mb-4">Métricas</h3>
        <!-- Metrics content... -->
      </div>
      
      <!-- Documents -->
      <div class="bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-xl font-bold text-gray-900 mb-4">Documentação</h3>
        <!-- Documents content... -->
      </div>
    </div>
  </div>
</section>
{% endblock %}
```

### 3. Sistema de Paginação (`project-detail-generator.njk`)

```yaml
---
pagination:
  data: projectPages.projectList
  size: 1
  alias: project
permalink: "/projetos/{{ project.id | slug }}/"
layout: layouts/project.njk
eleventyComputed:
  title: "{{ project.title }}"
  description: "{{ project.subtitle }}"
---
{# Este arquivo não precisa de conteúdo. Ele apenas usa o frontmatter para gerar as páginas. #}
```

## 🎨 Sistema de Design

### Design Tokens
```css
/* Cores primárias */
:root {
  --color-primary-50: #eff6ff;
  --color-primary-600: #2563eb;
  --color-secondary-50: #f0fdf4;
  --color-secondary-600: #16a34a;
}

/* Tipografia */
:root {
  --font-family-sans: 'Inter', system-ui, sans-serif;
  --font-family-mono: 'JetBrains Mono', monospace;
}
```

### Componentes Reutilizáveis

#### Card de Projeto
```html
<div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
  <div class="aspect-w-16 aspect-h-9">
    <img src="{{ project.images[0].src }}" 
         alt="{{ project.images[0].alt }}"
         class="w-full h-full object-cover">
  </div>
  <div class="p-6">
    <h3 class="text-xl font-bold text-gray-900 mb-2">{{ project.title }}</h3>
    <p class="text-gray-600 mb-4">{{ project.subtitle }}</p>
    <div class="flex items-center justify-between">
      <span class="text-sm text-gray-500">{{ project.year }}</span>
      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
        {{ project.category }}
      </span>
    </div>
  </div>
</div>
```

#### Métricas
```html
<div class="bg-gradient-to-br from-primary-50 to-secondary-50 rounded-lg p-6">
  <h3 class="text-xl font-bold text-gray-900 mb-4">Métricas do Projeto</h3>
  <div class="space-y-4">
    <div class="flex justify-between items-center">
      <span class="text-gray-700">Complexidade</span>
      <div class="flex items-center">
        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
          <div class="bg-primary-600 h-2 rounded-full" 
               style="width: {{ project.metrics.complexity * 10 }}%"></div>
        </div>
        <span class="font-semibold text-primary-600">{{ project.metrics.complexity }}/10</span>
      </div>
    </div>
    <div class="flex justify-between">
      <span class="text-gray-700">Área</span>
      <span class="font-semibold text-gray-900">{{ project.metrics.area }} m²</span>
    </div>
    <div class="flex justify-between">
      <span class="text-gray-700">Duração</span>
      <span class="font-semibold text-gray-900">{{ project.metrics.duration }}</span>
    </div>
  </div>
</div>
```

## ⚡ Otimizações de Performance

### 1. Otimização de Imagens
```javascript
// .eleventy.js
eleventyConfig.addNunjucksAsyncShortcode("image", async (src, alt, cls = "") => {
  let metadata = await Image(src, {
    widths: [300, 600, 900, 1200],
    formats: ["webp", "jpeg"],
    urlPath: "/img/optimized/",
    outputDir: "./dist/img/optimized/",
    filenameFormat: function (id, src, width, format) {
      return `${id}-${width}.${format}`;
    }
  });
  
  let imageAttributes = { 
    alt, 
    loading: "lazy", 
    decoding: "async",
    class: cls
  };
  
  return Image.generateHTML(metadata, imageAttributes);
});
```

### 2. Minificação de HTML
```javascript
// .eleventy.js
if (process.env.ELEVENTY_ENV === "prod") {
  eleventyConfig.addTransform("htmlmin", function (content, outputPath) {
    if (outputPath.endsWith(".html")) {
      return minify(content, {
        collapseWhitespace: true,
        removeComments: true,
        minifyCSS: true,
        minifyJS: true
      });
    }
    return content;
  });
}
```

### 3. Lazy Loading
```html
<!-- Imagens com lazy loading -->
<img src="{{ image.src }}" 
     alt="{{ image.alt }}" 
     loading="lazy" 
     decoding="async"
     class="w-full h-full object-cover">
```

## 🔍 SEO e Acessibilidade

### Meta Tags Dinâmicas
```html
<!-- Meta tags básicas -->
<meta name="description" content="{{ description }}">
<meta name="keywords" content="{{ keywords | join(', ') }}">

<!-- Open Graph -->
<meta property="og:title" content="{{ title }}">
<meta property="og:description" content="{{ description }}">
<meta property="og:image" content="{{ ogImage }}">
<meta property="og:url" content="{{ page.url | absoluteUrl }}">

<!-- Twitter Card -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ title }}">
<meta name="twitter:description" content="{{ description }}">
```

### Schema Markup
```html
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Project",
  "name": "{{ project.title }}",
  "description": "{{ project.description }}",
  "client": {
    "@type": "Organization",
    "name": "{{ project.client }}"
  },
  "location": {
    "@type": "Place",
    "name": "{{ project.location }}"
  },
  "dateCreated": "{{ project.year }}"
}
</script>
```

### Acessibilidade
```html
<!-- Navegação por teclado -->
<nav role="navigation" aria-label="Menu principal">
  <ul>
    <li><a href="/" tabindex="0">Início</a></li>
    <li><a href="/projetos" tabindex="0">Projetos</a></li>
  </ul>
</nav>

<!-- Imagens com alt text descritivo -->
<img src="{{ image.src }}" 
     alt="{{ image.alt }}" 
     role="img"
     aria-describedby="image-description">

<!-- Contraste adequado -->
<div class="text-gray-900 bg-white">Texto com contraste adequado</div>
```

## 🧪 Testes e Qualidade

### Testes E2E com Cypress
```javascript
// cypress/e2e/project-pages.cy.js
describe('Project Pages', () => {
  it('should load project details correctly', () => {
    cy.visit('/projetos/casa-gp/');
    
    // Verificar elementos principais
    cy.get('h1').should('contain', 'Casa GP');
    cy.get('[data-testid="project-metrics"]').should('be.visible');
    cy.get('[data-testid="project-documents"]').should('be.visible');
    
    // Verificar funcionalidades
    cy.get('a[href*=".pdf"]').should('have.attr', 'target', '_blank');
    cy.get('img').should('have.attr', 'alt');
  });
  
  it('should be accessible', () => {
    cy.visit('/projetos/casa-gp/');
    cy.injectAxe();
    cy.checkA11y();
  });
});
```

### Linting e Formatação
```json
// .eslintrc.json
{
  "extends": ["eslint:recommended"],
  "env": {
    "node": true,
    "browser": true
  },
  "rules": {
    "no-unused-vars": "warn",
    "no-console": "warn"
  }
}
```

## 📊 Monitoramento e Analytics

### Métricas de Performance
```javascript
// scripts/analytics.js
// Web Vitals
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

function sendToAnalytics(metric) {
  // Enviar métricas para Google Analytics
  gtag('event', metric.name, {
    value: Math.round(metric.value),
    event_category: 'Web Vitals'
  });
}

getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);
```

### Error Tracking
```javascript
// scripts/error-tracking.js
window.addEventListener('error', function(e) {
  // Enviar erros para serviço de monitoramento
  console.error('Error:', e.error);
  
  // Google Analytics
  gtag('event', 'exception', {
    description: e.error.message,
    fatal: false
  });
});
```

---

*Esta documentação de arquitetura é mantida atualizada conforme o projeto evolui.* 
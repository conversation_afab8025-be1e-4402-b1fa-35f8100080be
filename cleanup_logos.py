#!/usr/bin/env python3
"""
Script para limpar e organizar os arquivos de logo
Remove arquivos de teste e organiza os finais
"""

import os
from pathlib import Path

def cleanup_logo_files():
    """Remove arquivos de teste e organiza os finais"""
    
    favicon_dir = Path("src/site/assets/favicon")
    
    # Lista de arquivos para remover (testes)
    files_to_remove = [
        "teste_sem_fundo.png",
        "pvp_sem_fundo.png",  # versão antiga
        "pvp_alt.png"  # se existir
    ]
    
    print("🧹 Limpando arquivos de teste...")
    
    for filename in files_to_remove:
        file_path = favicon_dir / filename
        if file_path.exists():
            file_path.unlink()
            print(f"🗑️ Removido: {filename}")
    
    # Verifica arquivos finais
    print("\n📁 Arquivos finais disponíveis:")
    
    final_files = [
        "pvp.jpg",  # Original
        "pvp_transparente.png",  # Versão com fundo transparente
        "Favicon.png"  # Favicon existente
    ]
    
    for filename in final_files:
        file_path = favicon_dir / filename
        if file_path.exists():
            size_kb = file_path.stat().st_size / 1024
            print(f"✅ {filename} ({size_kb:.1f} KB)")
        else:
            print(f"❌ {filename} (não encontrado)")
    
    print("\n🎯 Recomendação:")
    print("Use 'pvp_transparente.png' como seu novo logo!")
    print("- Fundo transparente: ✅")
    print("- Qualidade preservada: ✅")
    print("- Pronto para uso: ✅")

if __name__ == "__main__":
    cleanup_logo_files() 
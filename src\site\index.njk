---
layout: layouts/base.njk
title: Home
description: PVP Projects - Engenharia de Qualidade. Soluções integradas que unem técnica, segurança e design em Porto Alegre.
---

<!-- Hero Video Section (Full Screen) -->
<section class="relative h-screen overflow-hidden w-full hero-section">
  <!-- Video Background -->
  <video id="hero-video" 
         class="absolute inset-0 w-full h-full object-cover" 
         autoplay 
         muted 
         loop 
         playsinline
         preload="auto">
    <source src="/assets/video/hero_video.mp4" type="video/mp4" />
    Seu navegador não suporta o elemento video.
  </video>
  
  <!-- Simple overlay for text readability -->
  <div class="absolute inset-0 bg-black bg-opacity-30"></div>
  
  <!-- Content Overlay -->
  <div class="absolute inset-0 flex items-center justify-center">
    <div class="text-center text-white">
      <h1 class="text-5xl lg:text-7xl font-bold mb-6" data-aos="fade-up">
        <span class="text-yellow-400">Engenharia</span> de Qualidade
      </h1>
      <p class="text-xl lg:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed" data-aos="fade-up" data-aos-delay="200">
        Soluções técnicas lideradas por um <strong>Engenheiro Eletricista</strong> 
        com experiência em projetos de alta qualidade
      </p>
              <div class="flex flex-col sm:flex-row gap-4 justify-center" data-aos="fade-up" data-aos-delay="400">
          <a href="/projetos-dedicada" class="btn-primary bg-primary-600 text-white px-8 py-4 rounded-lg hover:bg-primary-700 transition-colors duration-200 font-semibold text-lg inline-flex items-center justify-center">
            Ver Projetos
            <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
            </svg>
          </a>
          <a href="/contato" class="btn-secondary border-2 border-white text-white px-8 py-4 rounded-lg hover:bg-white hover:text-gray-900 transition-colors duration-200 font-semibold text-lg inline-flex items-center justify-center">
            Solicitar Orçamento
          </a>
        </div>
    </div>
  </div>
  
  <!-- Scroll Indicator -->
  <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
    </svg>
  </div>
</section>

<!-- Stats Section -->
<section class="py-16 bg-gradient-light">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="metric-card glass p-6 rounded-xl shadow-glow border border-white/20 cursor-pointer hover:scale-105 transition-transform duration-300" data-anime="bounce-in">
        <div class="text-3xl font-bold text-primary-600 mb-2">{{ company.metrics.experience }}</div>
        <div class="text-gray-700 font-medium">Experiência</div>
      </div>
      <div class="metric-card glass p-6 rounded-xl shadow-glow border border-white/20 cursor-pointer hover:scale-105 transition-transform duration-300" data-anime="bounce-in">
        <div class="text-3xl font-bold text-primary-600 mb-2">{{ company.metrics.projects }}</div>
        <div class="text-gray-700 font-medium">Projetos</div>
      </div>
      <div class="metric-card glass p-6 rounded-xl shadow-glow border border-white/20 cursor-pointer hover:scale-105 transition-transform duration-300" data-anime="bounce-in">
        <div class="text-3xl font-bold text-primary-600 mb-2">{{ company.metrics.area }}</div>
        <div class="text-gray-700 font-medium">Área Projetada</div>
      </div>
      <div class="metric-card glass p-6 rounded-xl shadow-glow border border-white/20 cursor-pointer hover:scale-105 transition-transform duration-300" data-anime="bounce-in">
        <div class="text-3xl font-bold text-primary-600 mb-2">{{ company.metrics.satisfaction }}</div>
        <div class="text-gray-700 font-medium">Satisfação</div>
      </div>
    </div>
  </div>
</section>

<!-- Services Section -->
<section class="py-20 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Nossos Serviços</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Oferecemos soluções completas de engenharia para todos os tipos de projetos
      </p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      {% for service in (company.services or []) %}
      <div class="service-card bg-{{ service.bgColor }} p-8 rounded-xl hover:shadow-glow hover:scale-105 transition-all duration-300 cursor-pointer border border-white/20 relative h-[600px] flex flex-col" 
           data-aos="fade-up" 
           data-aos-delay="{{ loop.index * 100 }}"
           data-anime="fade-up">
        <div class="text-4xl mb-4 service-icon transform hover:scale-110 transition-transform duration-300">{{ service.icon }}</div>
        <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ service.title }}</h3>
        <p class="text-gray-600 mb-4">{{ service.description }}</p>
        <div class="flex-1 overflow-y-auto">
          <ul class="space-y-2 text-sm text-gray-600">
            {% for feature in service.features %}
            <li class="flex items-start">
              <svg class="w-4 h-4 text-primary-600 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-xs leading-relaxed">{{ feature }}</span>
            </li>
            {% endfor %}
          </ul>
        </div>
        
        <!-- Link para o serviço -->
        <a href="/servicos/{{ service.id }}" 
           class="mt-4 text-primary-600 text-xs underline hover:text-primary-700 transition-colors duration-200 text-center">
          Ver serviço →
        </a>
      </div>
      {% endfor %}
    </div>
  </div>
</section>

<!-- About Section -->
<section class="py-20 bg-gray-50">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <div data-aos="fade-right">
        <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">Sobre a PVP Projects</h2>
        <p class="text-lg text-gray-600 mb-6">{{ company.about.history }}</p>
        <p class="text-lg text-gray-600 mb-8">{{ company.about.focus }}</p>
        
        <div class="grid grid-cols-2 gap-6">
          <div class="text-center">
            <div class="text-2xl font-bold text-primary-600 mb-2">{{ company.about.experience }}</div>
            <div class="text-gray-600">Experiência</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-primary-600 mb-2">{{ company.about.education }}</div>
            <div class="text-gray-600">Formação</div>
          </div>
        </div>
      </div>
      
      <div class="flex justify-center" data-aos="fade-left">
        <figure class="text-center">
          <img src="/assets/perfil/foto perfil.jpg" 
               alt="Pedro Vitor Pagliarin - Engenheiro Eletricista" 
               class="w-34 h-34 rounded-full object-cover object-center shadow-lg mx-auto mb-4 border-4 border-white">
          <figcaption class="text-lg font-semibold text-gray-900">Pedro Vitor Pagliarin</figcaption>
          <p class="text-gray-600">Engenheiro Eletricista - UFRGS</p>
        </figure>
      </div>
    </div>
  </div>
</section>

<!-- Seção de Projetos em Destaque -->
<div class="relative py-20 bg-gradient-to-br from-gray-50 to-gray-100">
        <!-- Imagem de fundo -->
      <div class="absolute inset-0 overflow-hidden">
        <img src="/assets/imagens/render-barao.png" 
             alt="Edifício BdU" 
             class="w-full h-full object-cover opacity-50">
      </div>
  
  <!-- Conteúdo sobreposto -->
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
        Projetos em Destaque
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Conheça alguns dos nossos projetos mais representativos
      </p>
    </div>
    
    <!-- Botão para ver todos os projetos -->
    <div class="text-center mt-12">
      <a href="/projetos-dedicada" class="bg-primary-600 text-white px-8 py-4 rounded-lg hover:bg-primary-700 transition-colors duration-200 font-semibold text-lg inline-flex items-center">
        Ver Todos os Projetos
        <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
        </svg>
      </a>
    </div>
  </div>
</div>

<!-- CTA Section -->
<section class="py-20 bg-primary-600">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <div data-aos="fade-up">
      <h2 class="text-3xl lg:text-4xl font-bold text-white mb-6">Pronto para seu próximo projeto?</h2>
      <p class="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
        Entre em contato conosco e descubra como podemos transformar sua visão em realidade
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="/contato" class="bg-white text-primary-600 px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors duration-200 font-semibold text-lg">
          Solicitar Orçamento
        </a>
      </div>
    </div>
  </div>
</section> 
require('dotenv').config();
const settings = require('../../helpers/constants');

const allSettings = settings.ALL_NOTE_SETTINGS;

module.exports = {
  eleventyComputed: {
    layout: data => {
      if (data.tags.indexOf('gardenEntry') != -1) {
        return 'layouts/index.njk';
      }
      return 'layouts/note.njk';
    },
    permalink: data => {
      if (data.tags.indexOf('gardenEntry') != -1) {
        return '/';
      }
      // Desabilitar URLs diretas das notes para evitar conflitos com sistema de projetos
      return false;
    },
    eleventyExcludeFromCollections: true,
    settings: data => {
      const noteSettings = {};
      allSettings.forEach(setting => {
        let noteSetting = data[setting];
        let globalSetting = process.env[setting];

        let settingValue =
          noteSetting || (globalSetting === 'true' && noteSetting !== false);
        noteSettings[setting] = settingValue;
      });
      return noteSettings;
    },
  },
};

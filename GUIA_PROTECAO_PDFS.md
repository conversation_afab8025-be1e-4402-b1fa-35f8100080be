# 🔒 Guia de Proteção de PDFs - Implementação Completa

## ✅ Status Atual
- ✅ **Sistema implementado** no projeto GT (teste)
- ✅ **CSS de proteção** adicionado em `custom-style.scss`
- ✅ **JavaScript de proteção** adicionado em `main.js`
- ✅ **Página de teste** criada em `test-pdf-protection.html`

## 📋 Checklist para Aplicar em Todos os Projetos

### 1. Atualizar Links de PDF
**Remover `target="_blank"` de todos os links de PDF:**

```markdown
❌ ANTES (permite download):
[📄 **Ver Planta Elétrica**](/assets/pdfs/projeto-rp_eletrica.pdf){:target="_blank" .reveal}

✅ DEPOIS (protegido):
[📄 **Ver Planta Elétrica**](/assets/pdfs/projeto-rp_eletrica.pdf)
```

### 2. Atualizar Objetos PDF
**Adicionar parâmetros de proteção e remover links de download:**

```html
❌ ANTES:
<object data="/assets/pdfs/projeto.pdf#toolbar=0"
        type="application/pdf" width="100%" height="500">
  <p>PDF indisponível — <a href="/assets/pdfs/projeto.pdf" target="_blank">baixar</a>.</p>
</object>

✅ DEPOIS:
<object data="/assets/pdfs/projeto.pdf#toolbar=0&navpanes=0&scrollbar=0"
        type="application/pdf" width="100%" height="500">
  <p>PDF indisponível para visualização.</p>
</object>
```

## 🎯 Projetos para Atualizar

### FASE 1 - Projetos Prioritários (Testados)
1. ✅ **SEFERIN GT** - `projeto-eletrico-gt_gt.md` (JÁ IMPLEMENTADO)

### FASE 2 - Projetos Residenciais
2. **RAFAEL SPICKER** - `projeto-eletrico-e-hidrossanitario-rp_rafael.md`
3. **ADRIENE** - `reforma-ad_adriene.md`
4. **ALEXANDRE** - `projeto-eletrico-e-hidrossanitario-al_alexandre.md`
5. **LUIZ EDUARDO E PREICILA** - `projeto-lep_lep.md`
6. **NAIRO** - `projeto-nf_nairo.md`

### FASE 3 - Projetos Comerciais
7. **PREDIO COM COMERCIO** - `projeto-eletrico-e-hidrossanitario-rmc_predio-comercio.md`
8. **SEFERIN CD** - `projeto-eletrico-cd_cd.md`
9. **SEFERIN GP** - `projeto-eletrico-gp_gp.md`

### FASE 4 - Projetos Revit
10. **BARÃO DE UBA** - `projeto-eletrico-bu_barao-uba.md`
11. **CASA SITIO** - `projeto-eletrico-e-hidrosanitario-cs_casa-sitio.md`
12. **DOM PEDRITO** - `projeto-eletrico-dp_dom-pedrito.md`
13. **LOJA AVENIDA DO FORTE** - `projeto-eletrico-af_loja-avenida.md`
14. **LOJA JOAO WALLIG** - `projeto-eletrico-jw_loja-joao.md`
15. **LOJAS REMIÃO** - `projeto-eletrico-lr._lojas-remiao.md`
16. **MARISTA** - `projeto-eletrico-ma_marista.md`
17. **RODRIGO EMPRESA** - `projeto-eletrico-e-hidrossanitario-re_rodrigo-empresa.md`
18. **SICREDI** - `projeto-eletrico-si_sicredi.md`
19. **ZOTTI** - `projeto-eletrico-dz_zotti.md`

## 🔧 Script de Automação

Para facilitar a aplicação em todos os projetos, você pode usar este script Python:

```python
import os
import re

def update_pdf_protection():
    """
    Atualiza automaticamente a proteção de PDFs em todos os arquivos .md
    """
    notes_dir = "src/site/notes"
    
    # Padrões para substituir
    patterns = [
        # Remove target="_blank" de links
        (r'\{:target="_blank" \.reveal\}', ''),
        (r'\{:target="_blank"\}', ''),
        
        # Adiciona parâmetros de proteção aos objetos PDF
        (r'#toolbar=0"', '#toolbar=0&navpanes=0&scrollbar=0"'),
        
        # Remove links de download dos fallbacks
        (r'<p>PDF indisponível — <a href="[^"]*" target="_blank">baixar</a>\.</p>', 
         '<p>PDF indisponível para visualização.</p>'),
    ]
    
    for root, dirs, files in os.walk(notes_dir):
        for file in files:
            if file.endswith('.md'):
                file_path = os.path.join(root, file)
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # Aplica todas as substituições
                for pattern, replacement in patterns:
                    content = re.sub(pattern, replacement, content)
                
                # Salva se houve mudanças
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"✅ Atualizado: {file_path}")
                else:
                    print(f"⏭️ Sem mudanças: {file_path}")

if __name__ == "__main__":
    update_pdf_protection()
```

## 🧪 Como Testar

1. **Abra a página de teste:** `test-pdf-protection.html`
2. **Teste as proteções:**
   - Clique com botão direito no PDF
   - Pressione Ctrl+S
   - Pressione Ctrl+P
   - Pressione F12
   - Tente arrastar o PDF
   - Tente selecionar texto

3. **Verifique se aparecem os avisos de proteção**

## 🛡️ Recursos de Proteção Implementados

### CSS (Proteção Visual)
- ✅ Desabilita seleção de texto
- ✅ Remove menu de contexto
- ✅ Adiciona overlay de proteção
- ✅ Mostra aviso de proteção no hover

### JavaScript (Proteção Funcional)
- ✅ Remove `target="_blank"` automaticamente
- ✅ Intercepta cliques em links de PDF
- ✅ Abre modal protegido
- ✅ Bloqueia Ctrl+S, Ctrl+P, F12
- ✅ Previne menu de contexto
- ✅ Bloqueia tentativas de arrastar
- ✅ Mostra avisos visuais

### Parâmetros PDF (Proteção no Navegador)
- ✅ `#toolbar=0` - Remove barra de ferramentas
- ✅ `&navpanes=0` - Remove painéis de navegação
- ✅ `&scrollbar=0` - Remove barras de rolagem

## ⚠️ Limitações

1. **Usuários avançados** ainda podem contornar essas proteções
2. **Print Screen** ainda funciona (mas qualidade reduzida)
3. **Ferramentas de desenvolvedor** podem ser usadas
4. **Extensões de navegador** podem contornar

## 🎯 Objetivo Alcançado

✅ **Desencoraja downloads casuais**  
✅ **Mantém qualidade de visualização**  
✅ **Protege contra cópia simples**  
✅ **Interface profissional**  
✅ **Experiência de usuário positiva**

## 📝 Próximos Passos

1. **Teste o projeto GT** primeiro
2. **Aplique o script de automação** para todos os projetos
3. **Teste cada projeto** individualmente
4. **Monitore feedback** dos usuários
5. **Ajuste conforme necessário**

---

**🎉 Sistema de proteção implementado com sucesso!** 
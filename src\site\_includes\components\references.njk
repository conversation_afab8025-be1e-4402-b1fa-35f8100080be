    <script>
      if (window.location.hash) {
        document.getElementById(window.location.hash.slice(1)).classList.add('referred');
      }
      window.addEventListener('hashchange', (evt) => {
        const oldParts = evt.oldURL.split("#");
        if (oldParts[1]) {
          document.getElementById(oldParts[1]).classList.remove('referred');
        }
        const newParts = evt.newURL.split("#");
        if (newParts[1]) {
          document.getElementById(newParts[1]).classList.add('referred');
        }
      }, false);
      const url_parts = window.location.href.split("#")
      const url = url_parts[0];
      const referrence = url_parts[1];
      document.querySelectorAll(".cm-s-obsidian > *[id]").forEach(function (el) {
        el.ondblclick = function(evt) {
          const ref_url = url + '#' + evt.target.id
          navigator.clipboard.writeText(ref_url);
      }
      });

      
    </script>
---
permalink: /searchIndex.json
eleventyExcludeFromCollections: true
---
[
{% for projectId, project in projects.projects %}
{
		"title": "{{ project.title }} - {{ project.subtitle }}",
		"date":"{{ project.year }}-01-01",
		"url":"/projetos/{{ project.id }}/",
		"content": "{{ project.description }} {% if project.challenges %}Desafios: {% for challenge in project.challenges %}{{ challenge }} {% endfor %}{% endif %} {% if project.solutions %}Soluções: {% for solution in project.solutions %}{{ solution }} {% endfor %}{% endif %} {% if project.results %}Resultados: {% for result in project.results %}{{ result }} {% endfor %}{% endif %} Localização: {{ project.location }} Cliente: {{ project.client }} Área: {{ project.details.area }} Pavimentos: {{ project.details.pavimentos }} Ferramentas: {% for tool in project.details.ferramentas %}{{ tool }} {% endfor %}",
		"tags": ["{{ project.category }}", "{{ project.subcategory }}", "{{ project.client }}", "{{ project.location }}"{% if project.details.ferramentas %}{% for tool in project.details.ferramentas %}, "{{ tool }}"{% endfor %}{% endif %}{% if project.tags_extra %}{% for tag in project.tags_extra %}, "{{ tag }}"{% endfor %}{% endif %}]
}{% if not loop.last %},{% endif %}
{% endfor %}]
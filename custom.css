/* Base geral */
body {
  font-family: 'Inter', sans-serif;
  background: #fafafa;
  color: #1a1a1a;
  line-height: 1.6;
  max-width: 800px;
  margin: auto;
  padding: 2rem;
}

/* <PERSON>abe<PERSON>l<PERSON> */
h1, h2, h3, h4 {
  font-weight: 600;
  color: #1e3a8a;
  border-bottom: 1px solid #ddd;
  padding-bottom: 0.3em;
  margin-top: 2rem;
}

h1 {
  font-size: 2rem;
}
h2 {
  font-size: 1.6rem;
}
h3 {
  font-size: 1.3rem;
}

/* Links */
a {
  color: #3b82f6;
  text-decoration: none;
}
a:hover {
  text-decoration: underline;
}

/* Tip Box (🧠 como as caixinhas azuis do help.obsidian.md) */
.tip {
  border-left: 5px solid #60a5fa;
  background: #e0f2fe;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 6px;
}

/* Bloco de código */
pre {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  padding: 1rem;
  border-radius: 8px;
  overflow-x: auto;
}
code {
  background: #e5e7eb;
  border-radius: 4px;
  padding: 0 4px;
  font-family: 'Fira Code', monospace;
  font-size: 0.95em;
}

/* Blocos YAML */
.frontmatter {
  background-color: #fff7ed;
  border-left: 4px solid #fb923c;
  padding: 0.5rem 1rem;
  margin: 1rem 0;
  font-family: monospace;
}

/* Blockquote estilizado */
blockquote {
  border-left: 4px solid #9ca3af;
  padding-left: 1rem;
  font-style: italic;
  color: #4b5563;
  background-color: #f9fafb;
}

/* Tabela */
table {
  border-collapse: collapse;
  width: 100%;
  margin-top: 1rem;
}
th, td {
  border: 1px solid #ddd;
  padding: 0.75rem;
}
th {
  background-color: #f3f4f6;
  font-weight: 600;
}

/* Navegação lateral fixa (se quiser em tela grande) */
@media screen and (min-width: 1200px) {
  body {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 2rem;
  }

  .site-nav {
    position: sticky;
    top: 2rem;
    align-self: start;
    background: #f3f4f6;
    border-radius: 10px;
    padding: 1rem;
    height: fit-content;
  }

  .site-nav a {
    display: block;
    margin-bottom: 0.5rem;
    color: #1f2937;
    font-weight: 500;
  }

  .site-nav a:hover {
    color: #2563eb;
  }
}
